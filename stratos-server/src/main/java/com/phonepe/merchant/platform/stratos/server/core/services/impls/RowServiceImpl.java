package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.fasterxml.jackson.databind.JsonNode;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.row.*;
import com.phonepe.merchant.platform.stratos.models.row.requests.*;
import com.phonepe.merchant.platform.stratos.models.row.response.EdcMisRowSummary;
import com.phonepe.merchant.platform.stratos.models.row.response.PgMisRowSummary;
import com.phonepe.merchant.platform.stratos.models.row.response.RowHistoryResponse;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.DestinationRequest;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.EdcRowProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.FileRowProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.PgMisRowProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.EdcRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.FileRowProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.PgMisRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.CorrectRowStateRequest;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.response.RowResponse;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.RowService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.FileUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.FileRowProcessorHelper;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.EdcMisRowSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PgMisRowSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.row.EdcRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.row.PgMisRowProcessor;
import com.phonepe.merchants.platform.primus.models.transform.JsonNodeContentBag;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.ws.rs.core.Response;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class RowServiceImpl implements RowService {

    private final PgMisRowProcessorActor pgMisRowProcessorActor;

    private final EdcRowProcessorActor edcRowProcessorActor;
    private final RowRepository rowRepository;
    private final PgMisRowProcessor pgMisRowProcessor;
    private final EdcRowProcessor edcRowProcessor;
    private final FileRepository fileRepository;
    private final FileRowProcessorHelper fileRowProcessorHelper;
    private final PgMisRowSummaryFileFormatVisitor pgMisRowSummaryFileFormatVisitor;
    private final EdcMisRowSummaryFileFormatVisitor edcMisRowSummaryFileFormatVisitor;
    private final Provider<FileRowProcessorActor> fileRowProcessorActorProvider;
    private static final String ROW_CONTEXT_NULL = "Row Context is Null";
    private final Map<ActionType, List<StratosErrorCodeKey>> actorIgnoreErrorCodes;
    private final EdcService edcService;
    private final DisputeService disputeService;
    private final PaymentsTxnlClient paymentsTxnlClient;


    @Override
    public void processSignal(StratosRowSignalRequest stratosRowSignalRequest) {
        validateSignalContext(stratosRowSignalRequest);
        stratosRowSignalRequest.getRowSignalContext()
            .accept(new RowSignalContextVisitor<Void>() { //NOSONAR
                @Override
                @SneakyThrows
                public Void visit(PgMisRowSignalContext pgMisRowSignalContext) {
                    var message = PgMisRowMessage.builder()
                        .rowId(IdGenerator.generate("ROW").getId())
                        .rowType(DtoUtils.rowDtoToType(pgMisRowSignalContext.getRowType()))
                        .sourceId(stratosRowSignalRequest.getSourceId())
                        .transactionType(pgMisRowSignalContext.getTransactionType())
                        .autoRefund(pgMisRowSignalContext.isAutoRefund())
                        .bankCode(pgMisRowSignalContext.getBankCode())
                        .bankTransactionId(pgMisRowSignalContext.getBankTransactionId())
                        .cgst(pgMisRowSignalContext.getCgst())
                        .charges(pgMisRowSignalContext.getCharges())
                        .igst(pgMisRowSignalContext.getIgst())
                        .instrumentType(pgMisRowSignalContext.getInstrumentType())
                        .mid(pgMisRowSignalContext.getMid())
                        .netAmount(pgMisRowSignalContext.getNetAmount())
                        .paymentDate(pgMisRowSignalContext.getPaymentDate())
                        .pgTransactionId(pgMisRowSignalContext.getPgTransactionId())
                        .sgst(pgMisRowSignalContext.getSgst())
                        .transactionAmount(pgMisRowSignalContext.getTransactionAmount())
                        .transactionDate(pgMisRowSignalContext.getTransactionDate())
                        .transactionId(pgMisRowSignalContext.getTransactionId())
                        .transactionDate(pgMisRowSignalContext.getTransactionDate())
                        .pgId(pgMisRowSignalContext.getPgId())
                        .interchange(pgMisRowSignalContext.getInterchange())
                        .build();
                    pgMisRowProcessorActor.publish(message);
                    return null;
                }

                @Override
                @SneakyThrows
                public Void visit(EdcRowSignalContext edcRowSignalContext) {
                    var message = EdcRowMessage.builder()
                        .sourceId(stratosRowSignalRequest.getSourceId())
                        .rowId(IdGenerator.generate("ROW").getId())
                        .rowType(DtoUtils.rowDtoToType(edcRowSignalContext.getRowType()))
                        .transactionType(edcRowSignalContext.getTransactionType())
                        .bankTransactionId(edcRowSignalContext.getBankTransactionId())
                        .cgst(edcRowSignalContext.getCgst())
                        .igst(edcRowSignalContext.getIgst())
                        .sgst(edcRowSignalContext.getSgst())
                        .mid(edcRowSignalContext.getMid())
                        .charges(edcRowSignalContext.getCharges())
                        .pgTransactionId(edcRowSignalContext.getPgTransactionId())
                        .chargebackAcceptedAmount(edcRowSignalContext.getChargebackAcceptedAmount())
                        .instrumentType(edcRowSignalContext.getInstrumentType())
                        .transactionDate(edcRowSignalContext.getTransactionDate())
                        .transactionAmount(edcRowSignalContext.getTransactionAmount())
                        .transactionId(edcRowSignalContext.getTransactionId())
                        .build();
                    edcRowProcessorActor.publish(message);
                    return null;
                }
            });
    }

    @Override
    public RowHistoryResponse history(RowHistoryRequest rowHistoryRequest) {
        FileUtils.validateDateRangeThreshold(rowHistoryRequest.getDateRangeFilter().getDateRange());

        final var startDate = rowHistoryRequest.getDateRangeFilter()
            .getDateRange()
            .getStartDate().atStartOfDay();

        final var endDate = rowHistoryRequest.getDateRangeFilter()
            .getDateRange()
            .getEndDate().atTime(LocalTime.MAX);

        final var rowTypes = rowHistoryRequest.getRowTypes().stream()
            .map(DtoUtils::rowDtoToType)
            .collect(Collectors.toSet());

        return RowHistoryResponse.builder().rows(
                rowRepository.rowList(rowTypes, startDate, endDate)
                    .stream()
                    .map(row -> row.getRowType().accept(new RowTypeVisitor<RowDto>() {
                        @Override
                        public RowDto visitUpiFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitPgFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitToaNeuronRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitEdcFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitEdcMisRow() {
                            var message = MapperUtils.deserialize(
                                Optional.ofNullable(row.getRowContext()).map(RowContext::getContent)
                                    .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.SERIALIZATION_ERROR,
                                        Map.of(Constants.MESSAGE, ROW_CONTEXT_NULL))),
                                EdcRowMessage.class);
                            return EdcRowDto.builder()
                                .rowId(row.getRowId())
                                .sourceId(row.getSourceId())
                                .rowState(DtoUtils.rowStateToDto(row.getRowState()))
                                .pgTransactionId(message.getPgTransactionId())
                                .chargebackAcceptedAmount(message.getChargebackAcceptedAmount())
                                .transactionAmount(message.getTransactionAmount())
                                .transactionId(message.getTransactionId())
                                .transactionType(message.getTransactionType())
                                .build();
                        }

                        @Override
                        public RowDto visitNBFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitToaApiRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitWalletFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitFraudFileRow() {
                            return null;
                        }

                        @Override
                        public RowDto visitPgMisRow() {
                            var message = MapperUtils.deserialize(
                                Optional.ofNullable(row.getRowContext()).map(RowContext::getContent)
                                    .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.SERIALIZATION_ERROR,
                                        Map.of(Constants.MESSAGE, ROW_CONTEXT_NULL))),
                                PgMisRowMessage.class);

                            return PgMisRowDto.builder()
                                .rowId(row.getRowId())
                                .code(row.getRowContext().getCode())
                                .sourceId(row.getSourceId())
                                .rowState(DtoUtils.rowStateToDto(row.getRowState()))
                                .netAmount(message.getNetAmount())
                                .pgId(message.getPgId())
                                .pgTransactionId(message.getPgTransactionId())
                                .transactionAmount(message.getTransactionAmount())
                                .transactionId(message.getTransactionId())
                                .transactionType(message.getTransactionType())
                                .interchange(message.getInterchange())
                                .build();
                        }
                    }))
                    .collect(Collectors.toList()))
            .build();
    }

    @Override
    public void replay(final RowReplayRequest rowReplayRequest) {

        var row = getRowFromDetachedCriteria(rowReplayRequest.getSourceId(),
            DetachedCriteria.forClass(Row.class).add(
                    Restrictions.eq(Fields.rowId, rowReplayRequest.getRowId()))
                .add(Restrictions.eq(Fields.rowState, RowState.FAILED)));

        row.getRowType().accept(new RowTypeVisitor<Void>() {
            @Override
            @SneakyThrows
            public Void visitUpiFileRow() {
                replayFileRow(row, DisputeType.UPI_CHARGEBACK);
                return null;
            }

            @SneakyThrows
            @Override
            public Void visitPgFileRow() {
                replayFileRow(row, DisputeType.PG_CHARGEBACK);
                return null;
            }

            @Override
            public Void visitPgMisRow() {
                var pgMisRowMessage = MapperUtils.deserialize(row.getRowContext().getContent(),
                    PgMisRowMessage.class);
                pgMisRowProcessor.process(pgMisRowMessage);
                return null;
            }

            @Override
            public Void visitToaNeuronRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                    Map.of(Constants.MESSAGE, "This TOA row does not support retry"));
            }

            @SneakyThrows
            @Override
            public Void visitEdcFileRow() {
                replayFileRow(row, DisputeType.EDC_CHARGEBACK);
                return null;
            }

            @Override
            public Void visitEdcMisRow() {
                var edcRowMessage = MapperUtils.deserialize(row.getRowContext().getContent(),
                    EdcRowMessage.class);
                edcRowProcessor.process(edcRowMessage);
                return null;
            }
            @SneakyThrows
            @Override
            public Void visitNBFileRow() {
                replayFileRow(row, DisputeType.NB_CHARGEBACK);
                return null;
            }

            @Override
            public Void visitToaApiRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                    Map.of(Constants.MESSAGE, "This TOA row does not support retry"));
            }

            @SneakyThrows
            @Override
            public Void visitWalletFileRow() {
                replayFileRow(row, DisputeType.WALLET_CHARGEBACK);
                return null;
            }
            @SneakyThrows
            @Override
            public Void visitFraudFileRow() {
                replayFileRow(row, DisputeType.FRA_FRAUD);
                return null;
            }
        });
    }

    private void replayFileRow(final Row row, final DisputeType disputeType) throws Exception {
        var file = fileRepository.getFile(row.getSourceId());

        fileRowProcessorHelper.process(file.getFileId(), row.getRowId(),
            row.getRowContext().getContent(), disputeType, file.getFileType());
    }

    @Override
    public byte[] download(final RowDownloadRequest rowDownloadRequest) {
        var rowType = DtoUtils.rowDtoToType(rowDownloadRequest.getRowType());

        return rowType.accept(new RowTypeVisitor<>() {
            @Override
            public byte[] visitUpiFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "Upi Row Type Not Supported"));
            }

            @Override
            public byte[] visitPgFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "Pg Row Type Not Supported"));
            }

            @Override
            public byte[] visitPgMisRow() {

                return rowDownloadRequest.getFileFormat()
                    .accept(pgMisRowSummaryFileFormatVisitor, getPgMisCsvRows(rowDownloadRequest));
            }

            @Override
            public byte[] visitToaNeuronRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "TOA Row Type Not Supported"));
            }

            @Override
            public byte[] visitEdcFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "Edc Row Type Not Supported"));
            }

            @Override
            public byte[] visitEdcMisRow() {
                // need to implement this function
                return rowDownloadRequest.getFileFormat()
                    .accept(edcMisRowSummaryFileFormatVisitor,
                        getEdcMisCsvRows(rowDownloadRequest));
            }

            @Override
            public byte[] visitNBFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "NB Row Type Not Supported"));
            }

            @Override
            public byte[] visitToaApiRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "TOA Row Type Not Supported"));
            }

            @Override
            public byte[] visitWalletFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "Wallet Row Type Not Supported"));
            }

            @Override
            public byte[] visitFraudFileRow() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                    Map.of(Constants.MESSAGE, "Fraud Row Type Not Supported"));
            }
        });
    }

    private List<EdcMisRowSummary> getEdcMisCsvRows(RowDownloadRequest rowDownloadRequest) {
        var rowIdList = rowDownloadRequest.getRowList().stream().map(RowDownloadData::getRowId)
            .collect(Collectors.toList());

        return rowRepository.scatterGather(DetachedCriteria.forClass(Row.class).add(
                    Restrictions.eq(Fields.rowType, RowType.EDC_MIS_ROW))
                .add(Restrictions.in(Fields.rowId, rowIdList)))
            .stream()
            .map(TransformationUtils::transformEdcMisRowSummary)
            .collect(Collectors.toList());
    }


    private List<PgMisRowSummary> getPgMisCsvRows(final RowDownloadRequest rowDownloadRequest) {
        var rowIdList = rowDownloadRequest.getRowList().stream().map(RowDownloadData::getRowId)
            .collect(Collectors.toList());

        return rowRepository.scatterGather(DetachedCriteria.forClass(Row.class).add(
                    Restrictions.eq(Fields.rowType, RowType.PG_MIS_ROW))
                .add(Restrictions.in(Fields.rowId, rowIdList)))
            .stream()
            .map(TransformationUtils::transformPgMisRowSummary)
            .collect(Collectors.toList());
    }




    private Row getRowFromDetachedCriteria(final String sourceId,
        final DetachedCriteria detachedCriteria) {
        return rowRepository.select(sourceId,
                detachedCriteria
            )
            .stream()
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.ROW_NOT_FOUND,
                Map.of(Constants.MESSAGE, "Could not find row")));
    }

    @Override
    public void offsetPgMisRow(PgMisRowOffsetRequest pgMisRowOffsetRequest) {
        var debitRow = getRowFromDetachedCriteria(pgMisRowOffsetRequest.getDebitSourceId(),
            DetachedCriteria.forClass(Row.class).add(
                    Restrictions.eq(Fields.rowId, pgMisRowOffsetRequest.getDebitRowId()))
                .add(Restrictions.eq(Fields.rowState, RowState.FAILED))
                .add(Restrictions.eq(Fields.rowType, RowType.PG_MIS_ROW)));

        var creditRow = getRowFromDetachedCriteria(pgMisRowOffsetRequest.getCreditSourceId(),
            DetachedCriteria.forClass(Row.class).add(
                    Restrictions.eq(Fields.rowId, pgMisRowOffsetRequest.getCreditRowId()))
                .add(Restrictions.eq(Fields.rowState, RowState.FAILED))
                .add(Restrictions.eq(Fields.rowType, RowType.PG_MIS_ROW)));

        var debitRowMessage = MapperUtils.deserialize(debitRow.getRowContext().getContent(),
            PgMisRowMessage.class);

        var creditRowMessage = MapperUtils.deserialize(creditRow.getRowContext().getContent(),
            PgMisRowMessage.class);

        if (validateOffsetRows(debitRowMessage, creditRowMessage)) {

            rowRepository.updateState(debitRow.getSourceId(), debitRow.getRowId(),
                RowState.PROCESSED);
            rowRepository.updateState(creditRow.getSourceId(), creditRow.getRowId(),
                RowState.PROCESSED);
        } else {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR,
                Map.of(Constants.MESSAGE, "Invalid Offset Data"));
        }
    }

    @Override
    public Response processPrimusSignal(DestinationRequest<JsonNode,JsonNode> primusRowSignal) {
        FileRowProcessorMessage message = null;
        try {
            File file = fileRepository.getFile(primusRowSignal.getFileId());
            message = FileRowProcessorMessage.builder()
                .rowId(IdGenerator.generate("ROW").getId())
                .fileId(primusRowSignal.getFileId())
                .disputeType(file.getDisputeType())
                .fileType(file.getFileType())
                .content(JsonNodeContentBag.builder()
                    .content(primusRowSignal.getRequest().toString())
                    .build())
                .build();

            fileRowProcessorActorProvider.get().publish(message);
            return Response.accepted().build();
        }
        catch (DisputeException stratosError) {
            if (stratosError.getErrorCode().equals(StratosErrorCodeKey.INVALID_FILE)) {
                log.error("Error due to {} with fileid {}",
                    stratosError.getContext().get(Constants.MESSAGE), primusRowSignal.getFileId());
                return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey())).build();
            } else {
                log.error("Exception while processing row with rowId {}, exception is {} ",
                    Objects.requireNonNull(message).getRowId(), stratosError.getMessage());
            }
            return Response.accepted().build();
        }
        catch (Exception e) {
            log.error("Exception while processing rowsignal {},{}",primusRowSignal,e.getMessage());
            return Response.accepted().build();
        }
    }


    private boolean validateOffsetRows(final PgMisRowMessage debitRowMessage,
        final PgMisRowMessage creditRowMessage) {
        return (debitRowMessage.getTransactionType() == RowTransactionType.CHARGEBACK) &&
            (creditRowMessage.getTransactionType() == RowTransactionType.CHARGEBACK_REVERSED)
            && (Objects.equals(debitRowMessage.getTransactionId(),
            creditRowMessage.getTransactionId())) &&
            (Objects.equals(debitRowMessage.getNetAmount(), creditRowMessage.getNetAmount()));
    }

    // This is used for housekeeping only hence it in the Service impl and not in the interface
    public void updateState(final CorrectRowStateRequest correctRowStateRequest) {
        rowRepository.updateState(correctRowStateRequest.getSourceId(),
            correctRowStateRequest.getRowId(), correctRowStateRequest.getToRowState());
    }

    // This is used for housekeeping only hence it in the Service impl and not in the interface
    public RowResponse getRow(final String sourceId) {
        return RowResponse.fromRow(rowRepository.getRow(sourceId));
    }

    private void validateSignalContext(StratosRowSignalRequest stratosRowSignalRequest) {

        stratosRowSignalRequest.getRowSignalContext()
            .accept(new RowSignalContextVisitor<Void>() {
                @Override
                public Void visit(PgMisRowSignalContext pgMisRowSignalContext) {

                    try{
                        final var detailResponse = paymentsTxnlClient.getTransactionDetails(
                                pgMisRowSignalContext.getTransactionId());
                        if (!detailResponse.isSuccess()) {
                            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PAYMENTS_CLIENT_ERROR, Map.of(
                                Constants.MESSAGE, "Error occurred while fetching transaction details",
                                Constants.TRANSACTION_ID, pgMisRowSignalContext.getTransactionId()));
                        }
                    } catch (DisputeException e) {
                        log.error("Fetching transaction detail for id : {} failed with error",
                            pgMisRowSignalContext.getTransactionId(), e);
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION,
                            Map.of(Constants.MESSAGE, "Invalid transaction id " +
                                pgMisRowSignalContext.getTransactionId()));
                    }

                    return null;
                }

                @Override
                public Void visit(EdcRowSignalContext edcRowSignalContext) {
                    var dispute = disputeService.getDispute(edcRowSignalContext.getTransactionId(),
                        DisputeType.EDC_CHARGEBACK);
                    DisputeWorkflow disputeWorkflow = disputeService.getDisputeWorkflow(
                        edcRowSignalContext.getTransactionId(),
                        DisputeType.EDC_CHARGEBACK, dispute.getCurrentDisputeStage());
                    edcService.validateDebitAmount(edcRowSignalContext, disputeWorkflow);
                    return null;
                }
            });
    }
}
