package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.NpciChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.server.core.configs.NpciDisputeFlag;

import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.WalletDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeCategory;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.services.ChargebackService;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.ChargebackFilterVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.ChargebackSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.NetworkVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.NpciChargebackFilterVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.NpciChargebackSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.service.WalletChargebackService;
import com.phonepe.models.common.enums.PaymentState;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.ReversedTransaction;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.AccountPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.BnplPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.CreditLinePaymentInstrument;
import com.phonepe.models.payments.pay.instrument.DebitCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalVpaPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.ExternalWalletPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.GiftCardPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.NcmcPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.NetBankingPaymentInstrument;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentVisitor;
import com.phonepe.models.payments.pay.instrument.WalletPaymentInstrument;
import com.phonepe.models.payments.pay.receivers.Receiver;
import com.utils.PreConditions;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.Comparator;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.state.State;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class ChargebackServiceImpl implements ChargebackService {

    private final DisputeService disputeService;

    private final PaymentsService paymentsService;

    private final ChargebackFilterVisitor chargebackFilterVisitor;

    private final DisputeStateMachineRegistry disputeStateMachineRegistry;

    private final ChargebackSummaryFileFormatVisitor chargebackSummaryFileFormatVisitor;

    private final NpciChargebackSummaryFileFormatVisitor npciChargebackSummaryFileFormatVisitor;

    private final MerchantService merchantService;
    private final EdcService edcService;

    private final WalletChargebackService walletChargebackService;
    private final NpciDisputeFlag npciDisputeFlag;
    private final NpciChargebackFilterVisitor npciChargebackFilterVisitor;

    @Override
    public Set<DisputeWorkflowState> getAllStates() {

        final var allRegisteredKeys = disputeStateMachineRegistry.getAllRegisteredKeys();

        return allRegisteredKeys.stream()
            .filter(k -> DisputeType.CHARGEBACKS.contains(k.getDisputeType()))
            .map(disputeStateMachineRegistry::getInternalStateMachine)
            .map(StateMachine::getStates)
            .flatMap(Collection::stream)
            .map(State::getId)
            .filter(state -> state.ordinal() < DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS.ordinal()
                || state.ordinal()
                > DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY.ordinal()) // After TOA_BLOCKED_DUE_TO_KS all TOA states
            .collect(Collectors.toSet());
    }

    @Override
    public List<ChargebackSummary> filter(final DisputeFilter filter) {

        final var disputePairs = filter.accept(chargebackFilterVisitor);

        return disputePairs.stream()
            .map(TransformationUtils::toChargebackSummary)
            .collect(Collectors.toList());
    }

    @Override
    public List<NpciChargebackSummary> npciChargebackFilter(final DisputeFilter filter) {

        final var disputePairs = filter.accept(npciChargebackFilterVisitor);

        return disputePairs.stream()
            .map(this::toNpciChargebackSummary)
            .toList();
    }

    @Override
    @SneakyThrows
    public byte[] download(final FileFormat fileFormat, final DisputeFilter filter) {

        final var disputePairs = filter.accept(chargebackFilterVisitor);

        final var chargebackSummaries = disputePairs.stream()
            .map(TransformationUtils::toChargebackSummaryRow)
            .map(this::enrichChargebackSummary)
            .collect(Collectors.toList());

        return fileFormat.accept(chargebackSummaryFileFormatVisitor, chargebackSummaries);
    }

    @Override
    @SneakyThrows
    public byte[] npciDownload(final FileFormat fileFormat, final DisputeFilter filter) {

        final var disputePairs = filter.accept(npciChargebackFilterVisitor);

        final var chargebackSummaries = disputePairs.stream()
            .map(this::enrichNpciChargebackSummaryRow)
            .collect(Collectors.toList());

        return fileFormat.accept(npciChargebackSummaryFileFormatVisitor, chargebackSummaries);
    }

    @Override
    public void updateChargebackStateBreachingTtl(final DateRange dateRange) {

        final var currentDate = LocalDate.now();

        if (dateRange.getEndDate().isAfter(currentDate)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_END_DATE, Map.of(
                "endDate", dateRange.getEndDate().toString(),
                "currentDate", currentDate.toString()));
        }

        final var allRegisteredKeys = disputeStateMachineRegistry.getAllRegisteredKeys();

        allRegisteredKeys.stream()
            .filter(k -> DisputeType.CHARGEBACKS.contains(k.getDisputeType()))
            .forEach(key ->
                this.updateChargebackStateBreachingTtlGivenStateMachineRegistryKey(key, dateRange));
    }

    private void updateChargebackStateBreachingTtlGivenStateMachineRegistryKey(
        final DisputeStateMachineRegistryKey disputeStateMachineRegistryKey,
        final DateRange dateRange) {

        final var internalStateMachine = disputeStateMachineRegistry.getInternalStateMachine(
            disputeStateMachineRegistryKey);

        final var transitions = internalStateMachine.getTransitions();
        final var sourceStateToEventMap = transitions.stream()
            .filter(t ->
                DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL == t.getTarget().getId())
            .collect(Collectors.toMap(
                t -> t.getSource().getId(),
                t -> t.getTrigger().getEvent()));

        final var disputeWorkflows = disputeService.getDisputeWorkflows(
            disputeStateMachineRegistryKey, sourceStateToEventMap.keySet(), dateRange);

        for (final DisputeWorkflow disputeWorkflow : disputeWorkflows) {
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                sourceStateToEventMap.get(disputeWorkflow.getCurrentState()),
                Constants.EMPTY_TRANSITION_CONTEXT);
        }
    }

    private void enrichChargebackSummaryFromPayment(
        final ChargebackSummaryRow chargebackSummaryRow) {
        final TransactionDetail transactionDetail = paymentsService.transactionDetailFromOriginalTransactionId(
            chargebackSummaryRow
                .getTransactionReferenceId());

        final var filteredReversedTransactions = filterReversedTransactions(
            transactionDetail.getAlreadyReversedTransactions());
        populateReversalTransactionIds(filteredReversedTransactions, chargebackSummaryRow);
        populateReversalAmounts(filteredReversedTransactions, chargebackSummaryRow);
        populateReversalStates(filteredReversedTransactions, chargebackSummaryRow);
        populateReversalDates(filteredReversedTransactions, chargebackSummaryRow);
        populateReversalUtrs(filteredReversedTransactions, chargebackSummaryRow);

        final var sentPayment = transactionDetail.getSentPayment();
        PreConditions.notNull(sentPayment);
        chargebackSummaryRow.setNetwork(
            (sentPayment.getPaidFrom() == null || sentPayment.getPaidFrom().isEmpty()) ? null :
                sentPayment.getPaidFrom().get(0).accept(new NetworkVisitor()));

        chargebackSummaryRow.setOriginalTransactionState(sentPayment.getPaymentState().name());
        chargebackSummaryRow.setOriginalTransactionDate(
            LocalDateTime.ofInstant(sentPayment.getSentAt().toInstant(), ZoneId
                .systemDefault()));
        final var contextMap = MapperUtils
            .convertValue(sentPayment.getContext(), TypeReferences.MAP_OF_STRING_OBJECT);
        chargebackSummaryRow.setMerchantOrderId((String) contextMap.get("merchantOrderId"));
        chargebackSummaryRow.setMerchantName(sentPayment.getTo()
            .stream()
            .findFirst()
            .map(Receiver::getName)
            .orElse(""));

    }

    private String dateConversion(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return localDate.format(formatter);
    }

    private String getFlag(Dispute dispute){
        if(dispute.getDisputeCategory().equals(DisputeCategory.FRAUD_CHARGEBACK)){
            return npciDisputeFlag.npciDisputeCategoryFlagMap.get(DisputeCategory.FRAUD_CHARGEBACK);
        }
        return dispute.getCurrentDisputeStage().accept(new DisputeStageVisitor<>() {
            @Override
            public String visitFirstLevel() {
                return npciDisputeFlag.npciDisputeStageFlagMap.get(DisputeStage.FIRST_LEVEL);
            }

            @Override
            public String visitPreArbitration() {
                return npciDisputeFlag.npciDisputeStageFlagMap.get(DisputeStage.PRE_ARBITRATION);
            }

            @Override
            public String visitPreChargeback() {
                return null;
            }
        });
    }

    private NpciChargebackSummaryRow enrichNpciChargebackSummaryFromPayment(
        final DisputeWorkflow disputeWorkflow) {
        final var dispute = disputeWorkflow.getDispute();
        final SentPayment sentPayment = paymentsService.transactionDetailFromOriginalTransactionId(
            disputeWorkflow.getTransactionReferenceId()).getSentPayment();
        final var walletSentPaymentInstrument = (sentPayment.getPaidFrom()
            .stream()
            .filter(
                paymentInstrument -> paymentInstrument.getType()
                    .equals(PaymentInstrumentType.WALLET))
            .findFirst()).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_CREATION,
                Map.of(
                    Constants.MESSAGE, "paymentInstrument type is not wallet"
                ))
        );

        WalletDisputeMetadata walletDisputeMetadata = walletChargebackService.getDisputeMetaForTrxId(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId()).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_METADATA_NOT_FOUND,
                Map.of(Constants.MESSAGE, "wallet dispute meta data not found for ",
                    "transaction Id ", disputeWorkflow.getTransactionReferenceId(),
                    "disputeWorkflow Id", disputeWorkflow.getDisputeWorkflowId()
                ))
        );

        return NpciChargebackSummaryRow.builder()
            .uTXID(((WalletPaymentInstrument) walletSentPaymentInstrument).getUpiTransactionId())
            .shdate(this.dateConversion(sentPayment.getSentAt()))
            .shser(dispute.getRrn())
            .bankadjref(dispute.getTransactionReferenceId())
            .adjamt((long) (TransformationUtils.paisaToRupees(disputeWorkflow.getDisputedAmount())))
            .flag(getFlag(dispute))
            .filename(TransformationUtils.getFileName())
            .reason(walletDisputeMetadata.getReasonCode())
            .build();
    }


    private ChargebackSummaryRow enrichChargebackSummary(final ChargebackSummaryRow
        chargebackSummaryRow) {
        try {
            DisputeType disputeType = DisputeType.valueOf(chargebackSummaryRow.getDisputeType());
            disputeType.accept(
                new DisputeTypeVisitor<Void>() {
                    @Override
                    public Void visitUpiChargeback() {
                        enrichChargebackSummaryFromPayment(chargebackSummaryRow);
                        return null;
                    }

                    @Override
                    public Void visitPgChargeback() {
                        visitUpiChargeback();
                        return null;
                    }

                    @Override
                    public Void visitUdirOutgoingComplaint() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE, "Udir Outgoing Complaints doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitUdirIncomingComplaint() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE, "Udir Incoming Complaints doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitP2PMToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE, "P2pm toa doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitEdcChargeback() {
                        edcService.enrichChargebackSummaryFromEdc(chargebackSummaryRow);
                        return null;
                    }

                    @Override
                    public Void visitNetBankingChargeback() {
                        return visitUpiChargeback();
                    }

                    @Override
                    public Void visitNotionalCreditToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE, "Notional toa doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitWalletChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Wallet doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitBbpsTatBreachToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                                Map.of(Constants.MESSAGE, "BBPS tat breach toa doesn't support chargeback Summary."));
                    }

                    @Override
                    public Void visitFraFraud() {
                        visitUpiChargeback();
                        return null;
                    }
                });

            final var merchantProfile = merchantService.getProfile(
                chargebackSummaryRow.getMerchantId());
            if (!Objects.isNull(merchantProfile)) {
                chargebackSummaryRow.setMerchantType(Optional.ofNullable(merchantProfile.getType())
                    .map(Enum::name)
                    .orElse(null));
                var address = merchantProfile.getAddress();
                if (!Objects.isNull(address)) {
                    chargebackSummaryRow.setArea(address.getArea());
                    chargebackSummaryRow.setCity(address.getCity());
                    chargebackSummaryRow.setState(address.getState());
                    chargebackSummaryRow.setPinCode(address.getPinCode());
                }

            }


        } catch (Exception e) {
            log.error("enrichChargebackSummary: failed", e);
            if (e instanceof DisputeException error) {
                String msg = String.valueOf(error.getContext().getOrDefault(Constants.MESSAGE, ""));
                String serviceName = String.valueOf(
                    error.getContext().getOrDefault(Constants.SERVICE_NAME, ""));
                String serviceErrorCode = String.valueOf(
                    error.getContext().getOrDefault(Constants.CODE, ""));
                chargebackSummaryRow.setErrorMessage(String.format(
                    "errorCode: %s, serviceName: %s, message: %s, serviceErrorCode: %s",
                    error.getErrorCode().name(), serviceName, msg, serviceErrorCode));
            } else {
                chargebackSummaryRow.setErrorMessage(e.getMessage());
            }
            return chargebackSummaryRow;
        }

        return chargebackSummaryRow;
    }

    public NpciChargebackSummary toNpciChargebackSummary(final DisputeWorkflow disputeWorkflow) {

        final var dispute = disputeWorkflow.getDispute();
        final SentPayment sentPayment = paymentsService.transactionDetailFromOriginalTransactionId(
            disputeWorkflow.getTransactionReferenceId()).getSentPayment();
        final var walletSentPaymentInstrument = (sentPayment.getPaidFrom()
            .stream()
            .filter(
                paymentInstrument -> paymentInstrument.getType()
                    .equals(PaymentInstrumentType.WALLET))
            .findFirst()).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_CREATION,
                Map.of(
                    Constants.MESSAGE, "paymentInstrument type is not wallet"
                ))
        );

        WalletDisputeMetadata walletDisputeMetadata = walletChargebackService.getDisputeMetaForTrxId(
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId()).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_METADATA_NOT_FOUND,
                Map.of(Constants.MESSAGE, "wallet dispute meta data not found for ",
                    "transaction Id ", disputeWorkflow.getTransactionReferenceId(),
                    "disputeWorkflow Id", disputeWorkflow.getDisputeWorkflowId()
                ))
        );

        return NpciChargebackSummary.builder()
            .utxid(((WalletPaymentInstrument) walletSentPaymentInstrument).getUpiTransactionId())
            .shdate(this.dateConversion(sentPayment.getSentAt()))
            .shser(dispute.getRrn())
            .bankadjref(dispute.getTransactionReferenceId())
            .adjamt((long) (TransformationUtils.paisaToRupees(disputeWorkflow.getDisputedAmount())))
            .flag(getFlag(dispute))
            .filename(TransformationUtils.getFileName())
            .reason(walletDisputeMetadata.getReasonCode())
            .build();
    }

    public NpciChargebackSummaryRow enrichNpciChargebackSummaryRow(
        final DisputeWorkflow disputeWorkflow) {
        try {
            DisputeType disputeType = disputeWorkflow.getDisputeType();
            return disputeType.accept(
                new DisputeTypeVisitor<NpciChargebackSummaryRow>() {
                    @Override
                    public NpciChargebackSummaryRow visitUpiChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "UPI Outgoing Complaints doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitPgChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Pg Outgoing Complaints doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitUdirOutgoingComplaint() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Udir Outgoing Complaints doesn't support chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitUdirIncomingComplaint() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Udir Incoming Complaints doesn't support chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitP2PMToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "P2pm toa doesn't support chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitEdcChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "EDC Outgoing Complaints doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitNetBankingChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "NetBanking Outgoing Complaints doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitNotionalCreditToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Notional toa doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitBbpsTatBreachToa() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_ROW,
                            Map.of(Constants.MESSAGE,
                                "Bbps tat doesn't support npci chargeback Summary."));
                    }

                    @Override
                    public NpciChargebackSummaryRow visitWalletChargeback() {
                        return enrichNpciChargebackSummaryFromPayment(disputeWorkflow);
                    }

                    @Override
                    public NpciChargebackSummaryRow visitFraFraud() {
                        return null;
                    }
                });
        } catch (Exception e) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNABLE_TO_ENRICH_CHARGEBACK, Map.of(
                Constants.MESSAGE, "Unable to enrich chargeback summary row"
            ));
        }
    }

    //filters reversedTransactions based on the payment state (COMPLETED and FAILED)
    private List<ReversedTransaction> filterReversedTransactions(
        final List<ReversedTransaction> reversedTransactions) {
        List<ReversedTransaction> filteredReversedTransactions = reversedTransactions.stream()
            .filter(reversedTransaction -> reversedTransaction.getPaymentState()
                == PaymentState.COMPLETED)
            .collect(Collectors.toList());

        if (filteredReversedTransactions.isEmpty()) {
            filteredReversedTransactions = reversedTransactions.stream()
                .filter(reversedTransaction -> reversedTransaction.getPaymentState()
                    == PaymentState.FAILED)
                .sorted(Comparator.comparing(ReversedTransaction::getAttemptedAt).reversed())
                .limit(1)
                .collect(Collectors.toList());
        }

        return filteredReversedTransactions;
    }

    private void populateReversalUtrs(final List<ReversedTransaction> reversedTransactions,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalUtrIds(
            reversedTransactions.stream()
                .map(this::getUtrOrDefaultValue)
                .collect(Collectors.joining(Constants.SPLIT_MULTIVALUE_SEPARATOR))
        );
    }

    private String getUtrOrDefaultValue(final ReversedTransaction reversedTransaction) {
        return Optional.ofNullable(reversedTransaction)
            .filter(txn -> PaymentState.COMPLETED == reversedTransaction.getPaymentState())
            .map(ReversedTransaction::getTransactionId)
            .map(paymentsService::transactionDetailFromOriginalTransactionId)
            .map(TransactionDetail::getReceivedPayment)
            .map(ReceivedPayment::getReceivedIn)
            .map(paymentInstruments -> paymentInstruments.isEmpty() ?
                null :
                paymentInstruments.get(0))
            .map(paymentInstrument -> paymentInstrument.accept(
                new PaymentInstrumentVisitor<String>() {
                    @Override
                    public String visit(
                        final AccountPaymentInstrument accountPaymentInstrument) {
                        return accountPaymentInstrument.getUtr();
                    }

                    @Override
                    public String visit(
                        final CreditCardPaymentInstrument creditCardPaymentInstrument) {
                        return creditCardPaymentInstrument.getArn();
                    }

                    @Override
                    public String visit(
                        final DebitCardPaymentInstrument debitCardPaymentInstrument) {
                        return debitCardPaymentInstrument.getArn();
                    }

                    @Override
                    public String visit(
                        final NetBankingPaymentInstrument netBankingPaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(final WalletPaymentInstrument walletPaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(
                        final GiftCardPaymentInstrument giftCardPaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(
                        final ExternalVpaPaymentInstrument externalVpaPaymentInstrument) {
                        return externalVpaPaymentInstrument.getUtr();
                    }

                    @Override
                    public String visit(
                        final ExternalWalletPaymentInstrument externalWalletPaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(final BnplPaymentInstrument bnplPaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(CreditLinePaymentInstrument creditLinePaymentInstrument) {
                        return "NA";
                    }

                    @Override
                    public String visit(NcmcPaymentInstrument ncmcPaymentInstrument) {
                        return "NA";
                    }
                }))
            .orElse("NA");
    }

    private void populateReversalTransactionIds(
        final List<ReversedTransaction> reversedTransactions,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalTransactionIds(reversedTransactions.stream()
            .map(ReversedTransaction::getTransactionId)
            .collect(Collectors.joining(", ")));

    }

    private void populateReversalAmounts(final List<ReversedTransaction> reversedTransactions,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalAmounts(reversedTransactions.stream()
            .map(ReversedTransaction::getAmount)
            .map(Long::doubleValue)
            .map(TransformationUtils::paisaToRupees)
            .map(String::valueOf)
            .collect(Collectors.joining(", ")));
    }

    private void populateReversalStates(final List<ReversedTransaction> reversedTransactions,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalStates(reversedTransactions.stream()
            .map(ReversedTransaction::getPaymentState)
            .map(Enum::name)
            .collect(Collectors.joining(", ")));

    }

    private void populateReversalDates(final List<ReversedTransaction> reversedTransactions,
        final ChargebackSummaryRow chargebackSummaryRow) {
        chargebackSummaryRow.setReversalDates(reversedTransactions.stream()
            .map(ReversedTransaction::getAttemptedAt)
            .map(date -> new SimpleDateFormat("yyyy-MM-dd HH:mm").format(date))
            .collect(Collectors.joining(", ")));

    }
}
