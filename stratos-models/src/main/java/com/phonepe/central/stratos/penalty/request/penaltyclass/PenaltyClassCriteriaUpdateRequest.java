package com.phonepe.central.stratos.penalty.request.penaltyclass;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({ "type", "penaltyClassId", "details"})
public class PenaltyClassCriteriaUpdateRequest extends PenaltyClassUpdateRequest {

    @NotEmpty
    private List<PenaltyClassDetail> details;

    @Builder
    @JsonCreator
    public PenaltyClassCriteriaUpdateRequest(@JsonProperty("penaltyClassId") String penaltyClassId,@JsonProperty("details") List<PenaltyClassDetail> details) {
        super(PenaltyClassUpdateType.CRITERIA_UPDATE, penaltyClassId);
        this.details = details;

    }

    @Override
    public <T> T accept(PenaltyClassUpdateRequestVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
