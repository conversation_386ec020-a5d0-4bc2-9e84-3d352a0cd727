CREATE TABLE `template_v2_audit` (
  `id` bigint(20) NOT NULL,
  `REV` int(11) NOT NULL,
  `REVTYPE` tinyint(4) DEFAULT NULL,
  `workflow_type` varchar(255) DEFAULT NULL,
  `provider_id` varchar(255) DEFAULT NULL,
  `component_kit_version` bigint(20) DEFAULT NULL,
  `template_id` varchar(255) DEFAULT NULL,
  `template_version` bigint(20) DEFAULT NULL,
  `template_section_mappings` mediumtext DEFAULT NULL,
  `active` bit(1) DEFAULT NULL,
  `created` datetime(3) DEFAULT current_timestamp(3),
  `updated` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  PRIMARY KEY (`id`,`REV`),
  UNIQUE KEY `unq_idx_template_id` (`template_id`,`REV`),
  KEY `idx_provider_id_workflow_type` (`provider_id`,`workflow_type`),
  <PERSON><PERSON>Y `get_template_idx` (`workflow_type`,`provider_id`,`component_kit_version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;