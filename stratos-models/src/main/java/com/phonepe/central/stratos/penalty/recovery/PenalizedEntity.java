package com.phonepe.central.stratos.penalty.recovery;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.central.stratos.penalty.recovery.impl.MerchantPenalizedEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */

@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(name = PenalizedEntityType.MERCHANT_PENALIZED_ENTITY_TEXT, value = MerchantPenalizedEntity.class)})
public abstract class PenalizedEntity {

    private final PenalizedEntityType type;

    public abstract <T> T accept(PenalizedEntityVisitor<T> visitor);

}
