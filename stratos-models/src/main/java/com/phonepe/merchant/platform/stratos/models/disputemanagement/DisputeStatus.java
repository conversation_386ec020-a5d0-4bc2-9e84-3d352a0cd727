package com.phonepe.merchant.platform.stratos.models.disputemanagement;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.visitors.DisputeStatusVisitor;

public enum DisputeStatus {
    NEEDS_ACTION {
        @Override
        public <T> T accept(DisputeStatusVisitor<T> visitor) {
            return visitor.visitNeedsAction();
        }
    },
    UNDER_REVIEW {
        @Override
        public <T> T accept(DisputeStatusVisitor<T> visitor) {
            return visitor.visitUnderReview();
        }
    },
    ACCEPTED {
        @Override
        public <T> T accept(DisputeStatusVisitor<T> visitor) {
            return visitor.visitAccepted();
        }
    },
    REJECTED {
        @Override
        public <T> T accept(DisputeStatusVisitor<T> visitor) {
            return visitor.visitRejected();
        }
    };

    public abstract <T> T accept(final DisputeStatusVisitor<T> visitor);
}
