package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class CommentContext extends TransitionContext {

    private String comment;

    public CommentContext() {
        super(ContextType.COMMENT_CONTEXT);
    }

    @Builder
    public CommentContext(final String comment) {
        this();
        this.comment = comment;
    }
}
