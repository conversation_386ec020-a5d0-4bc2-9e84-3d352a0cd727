package com.phonepe.merchant.platform.stratos.models.row.requests;


import static com.phonepe.merchant.platform.stratos.models.row.RowTypeDto.Names.EDC_MIS_ROW;
import static com.phonepe.merchant.platform.stratos.models.row.RowTypeDto.Names.PG_MIS_ROW;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.row.RowSignalContextVisitor;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "rowType", visible = true)
@JsonSubTypes(value = {
    @Type(value = PgMisRowSignalContext.class, name = PG_MIS_ROW),
    @Type(value = EdcRowSignalContext.class, name = EDC_MIS_ROW)
})
public abstract class RowSignalContext {

    @NotNull
    private RowTypeDto rowType;

    public abstract <T> T accept(RowSignalContextVisitor<T> rowSignalContextVisitor);

}
