package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeData {
    @NotBlank
    private DisputeType disputeType;
    @NotBlank
    private DisputeStage disputeStage;
    private DisputeCategory disputeCategory;
}
