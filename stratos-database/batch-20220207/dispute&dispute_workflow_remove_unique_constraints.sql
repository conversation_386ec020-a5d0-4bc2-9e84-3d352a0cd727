ALTER TABLE `dispute` DROP INDEX `idx_unq_txn_dispute_type`;
ALTER TABLE `dispute` ADD INDEX `idx_txn_dispute_type` (`transaction_reference_id`,`dispute_type`);

ALTER TABLE `dispute_audit` DROP INDEX `idx_unq_txn_dispute_type`;
ALTER TABLE `dispute_audit` ADD INDEX `idx_txn_dispute_type` (`transaction_reference_id`,`dispute_type`,`REV`);

ALTER TABLE `dispute_workflow` DROP INDEX `idx_unq_txn_dispute_type_stage`;
ALTER TABLE `dispute_workflow` ADD INDEX `idx_txn_dispute_type_stage` (`transaction_reference_id`,`dispute_type`,`dispute_stage`);

ALTER TABLE `dispute_workflow_audit` DROP INDEX `idx_unq_txn_dispute_type_stage`;
ALTER TABLE `dispute_workflow_audit` ADD INDEX `idx_txn_dispute_type_stage` (`transaction_reference_id`,`dispute_type`,`dispute_stage`,`REV`);