package com.phonepe.central.stratos.penalty.disbursement;

@SuppressWarnings("java:S115")
public enum PenaltyDisbursementMode {
    REFUND {
        @Override
        public <T> T accept(PenaltyDisbursementModeVisitor<T> visitor) {
            return visitor.visitRefund();
        }
    },
    ToA {
        @Override
        public <T> T accept(PenaltyDisbursementModeVisitor<T> visitor) {
            return visitor.visitToA();
        }
    };

    public abstract <T> T accept(PenaltyDisbursementModeVisitor<T> visitor);

    public interface PenaltyDisbursementModeVisitor<T> {

        T visitRefund();

        T visitToA();
    }
}
