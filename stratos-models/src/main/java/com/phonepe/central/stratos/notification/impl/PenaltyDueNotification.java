package com.phonepe.central.stratos.notification.impl;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.notification.ChangeNotitification;
import com.phonepe.central.stratos.notification.NotificationType;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PenaltyDueNotification extends ChangeNotitification {
    @Valid
    @NotNull
    private PenaltyProbable probable;

    @Builder
    @JsonCreator
    public PenaltyDueNotification(@JsonProperty("") PenaltyProbable probable) {
        super(NotificationType.PENALTY_DUE_SHORTLY);
        this.probable = probable;
    }

    @Override
    public <T> T accept(ChangeNotificationVisitor<T> visitor) {
        return visitor.visit(this);
    }

}
