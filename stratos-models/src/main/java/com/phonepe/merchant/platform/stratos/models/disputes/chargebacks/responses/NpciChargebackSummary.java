package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses;

import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NpciChargebackSummary {

    @NotEmpty
    private String bankadjref;

    @NotEmpty
    private String flag;

    @NotEmpty
    private String shdate;

    @NotEmpty
    private long adjamt;

    @NotEmpty
    private String shser;

    @NotEmpty
    private String utxid;

    @NotEmpty
    private String filename;

    @NotEmpty
    private String reason;

    private String specifyother;
}
