package com.phonepe.merchant.platform.stratos.server.core.clients;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.KaizenCallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.verified.kaizen.caches.impl.ClientCallbackCache;
import com.phonepe.verified.kaizen.foxtrot.EventIngestionCommand;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.verified.kaizen.queue.messages.WorkflowClientCallbackMessage;
import com.phonepe.verified.kaizen.registries.HttpClientRegistry;
import com.phonepe.verified.kaizen.services.ProfileService;
import com.phonepe.verified.kaizen.services.WorkflowService;

import com.phonepe.verified.kaizen.services.visitors.CallbackWorkflowMessageBuilderProfileVisitor;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@Singleton
public class KaizenCallbackClient extends com.phonepe.verified.kaizen.clients.internal.CallbackClient{

    private final KaizenCallbackActor kaizenCallbackActor;
    private final WorkflowService workflowService;
    private final ProfileService profileService;

    @Inject
    public KaizenCallbackClient(ProfileService profileService, OlympusIMClient olympusIMClient, WorkflowService workflowService, HttpClientRegistry httpClientRegistry, ClientCallbackCache clientCallbackCache, EventIngestionCommand eventIngestionCommand
                                ,KaizenCallbackActor kaizenCallbackActor
    ) {
        super(profileService, olympusIMClient, workflowService, httpClientRegistry, clientCallbackCache, eventIngestionCommand);
        this.kaizenCallbackActor = kaizenCallbackActor;
        this.workflowService = workflowService;
        this.profileService = profileService;
    }

    @Override
    public void sendCallback(final WorkflowClientCallbackMessage workflowClientCallbackMessage){
        final var storedWorkflow = workflowService.validateAndGetWorkflow(
                workflowClientCallbackMessage.getWorkflowId());
        final var profile = profileService.get(storedWorkflow.getProfileId(), false);
        final var callbackWorkflowMessageBuilderProfileVisitorData = CallbackWorkflowMessageBuilderProfileVisitor.CallbackWorkflowMessageBuilderProfileVisitorData.builder()
                .storedWorkflow(storedWorkflow)
                .failureErrorCode(workflowClientCallbackMessage.getFailureErrorCode())
                .accessToken(workflowClientCallbackMessage.getAccessToken())
                .requestId(workflowClientCallbackMessage.getRequestId())
                .build();
        final WorkflowClientCallback workflowClientCallback = profile.accept(
                CallbackWorkflowMessageBuilderProfileVisitor.INSTANCE,
                callbackWorkflowMessageBuilderProfileVisitorData);
        try {
            kaizenCallbackActor.publish(workflowClientCallback);
        } catch (Exception e) {
            log.error("Error while publishing workflowClientCallback to kaizenCallbackActor", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KAIZEN_CALLBACK_ERROR,
                Map.of(Constants.MESSAGE, e.getMessage()));
        }
    }
}
