CREATE TABLE `session_management_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `profile_id` varchar(64) NOT NULL,
  `type` varchar(128) NOT NULL,
  `principal_builder` varchar(512) NOT NULL,
  `data_builder` varchar(512) NOT NULL,
  `disabled` bit(1) NOT NULL,
  `validate_timer` bit(1) NOT NULL,
  `validate_token` bit(1) NOT NULL,
  `validate_data` bit(1) NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  `source_type` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `profile_id_source_type_idx` (`profile_id`,`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;