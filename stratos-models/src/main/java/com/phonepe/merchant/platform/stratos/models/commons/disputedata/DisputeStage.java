package com.phonepe.merchant.platform.stratos.models.commons.disputedata;

import com.phonepe.merchant.platform.stratos.models.disputes.visitors.DisputeStageDtoVisitor;

public enum DisputeStage {
    FIRST_LEVEL {
        @Override
        public <T> T accept(DisputeStageDtoVisitor<T> visitor) {
            return visitor.visitFirstLevel();
        }
    },
    PRE_ARBITRATION {
        @Override
        public <T> T accept(DisputeStageDtoVisitor<T> visitor) {
            return visitor.visitPreArbitration();
        }
    },
    PRE_CHARGEBACK {
        @Override
        public <T> T accept(DisputeStageDtoVisitor<T> visitor) {
            return visitor.visitPreChargeback();
        }
    };

    public abstract <T> T accept(final DisputeStageDtoVisitor<T> visitor);
}
