package com.phonepe.merchant.platform.stratos.models.row.requests;

import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowSignalContextVisitor;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class EdcRowSignalContext extends RowSignalContext {

    @NotNull
    private String transactionId;
    @NotNull
    private RowTransactionType transactionType;
    @NotNull
    private BigDecimal transactionAmount;
    private Date transactionDate;
    private String bankTransactionId;
    private String instrumentType;
    @NotNull
    private BigDecimal chargebackAcceptedAmount;
    @NotNull
    private String pgTransactionId;
    private BigDecimal charges;
    private String mid;
    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;

    public EdcRowSignalContext() {
        super(RowTypeDto.EDC_MIS_ROW);
    }
    @SuppressWarnings("java:S107")
    @Builder
    public EdcRowSignalContext(final String transactionId, final RowTransactionType transactionType,
        final BigDecimal transactionAmount, final Date transactionDate,
        final String bankTransactionId,
        final String instrumentType, final BigDecimal chargebackAcceptedAmount,
        final String pgTransactionId,
        final BigDecimal charges, final String mid, final BigDecimal igst, final BigDecimal cgst,
        final BigDecimal sgst) {
        this();
        this.transactionId = transactionId;
        this.transactionType = transactionType;
        this.transactionAmount = transactionAmount;
        this.transactionDate = transactionDate;
        this.bankTransactionId = bankTransactionId;
        this.instrumentType = instrumentType;
        this.chargebackAcceptedAmount = chargebackAcceptedAmount;
        this.pgTransactionId = pgTransactionId;
        this.charges = charges;
        this.mid = mid;
        this.igst = igst;
        this.cgst = cgst;
        this.sgst = sgst;
    }

    @Override
    public <T> T accept(RowSignalContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
