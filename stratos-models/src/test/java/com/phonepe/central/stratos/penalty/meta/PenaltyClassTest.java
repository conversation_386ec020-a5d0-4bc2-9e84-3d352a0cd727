package com.phonepe.central.stratos.penalty.meta;

import static org.junit.jupiter.api.Assertions.*;
import com.phonepe.central.stratos.penalty.growth.GrowthUnit;
import com.phonepe.central.stratos.penalty.growth.PenaltyGrowthRate;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedAmountCalculation;
import com.phonepe.growth.mustang.composition.impl.Conjunction;
import com.phonepe.growth.mustang.criteria.impl.DNFCriteria;
import com.phonepe.growth.mustang.detail.impl.EqualityDetail;
import com.phonepe.growth.mustang.predicate.impl.IncludedPredicate;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import java.time.Duration;
import java.util.Set;

@Slf4j
@ExtendWith(MockitoExtension.class)
public class PenaltyClassTest {

    @Mock
    private EqualityDetail equalityDetail;

    @Mock
    private IncludedPredicate includedPredicate;

    @Mock
    private  Conjunction conjunction;

    @Mock
    private DNFCriteria dnfCriteria;

    @InjectMocks
    private PenaltyClassDetail penaltyClassDetail;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);

        Set<Object> values = Set.of(120, 140);
        equalityDetail = EqualityDetail.builder()
                .values(Set.of("100", "120"))
                .build();

        includedPredicate = IncludedPredicate.builder()
                .lhs("lhs")
                .weight(12L)
                .detail(equalityDetail)
                .values(values)
                .build();

        conjunction = Conjunction.builder()
                .predicate(includedPredicate)
                .build();

        dnfCriteria = DNFCriteria.builder()
                .id("disqualifier")
                .conjunction(conjunction)
                .build();

        DNFCriteria triggerDnfCriteria = DNFCriteria.builder()
                .id("trigger")
                .conjunction(Conjunction.builder()
                        .predicate(IncludedPredicate.builder()
                                .detail(EqualityDetail.builder()
                                        .values(Set.of("60,80"))
                                        .build())
                                .lhs("lhs")
                                .weight(12L)
                                .values(Set.of(120,140))
                                .build())
                        .build())
                .build();

        penaltyClassDetail = PenaltyClassDetail.builder()
                .penaltyCap(12)
                .growthRate(PenaltyGrowthRate.builder()
                        .unit(GrowthUnit.DAY)
                        .calculation(PenaltyConversionDateFixedAmountCalculation.builder()
                                .amountPath("amountPath")
                                .isDefault(true)
                                .build())
                        .build())
                .label("criteriaLabel")
                .criteria(PenaltyCriteria.builder()
                        .disQualifier(dnfCriteria)
                        .leeway(Duration.ofDays(10))
                        .trigger(triggerDnfCriteria)
                        .build())
                .build();
    }

    @Test
    void testPenaltyClassDetailCreation() {
        assertNotNull(penaltyClassDetail);
        assertEquals(12, penaltyClassDetail.getPenaltyCap());
        assertNotNull(penaltyClassDetail.getGrowthRate());
        assertEquals(GrowthUnit.DAY, penaltyClassDetail.getGrowthRate().getUnit());
        assertNotNull(penaltyClassDetail.getGrowthRate().getCalculation());
        assertEquals("amountPath", penaltyClassDetail.getGrowthRate().getCalculation().getAmountPath());
        assertTrue(penaltyClassDetail.getGrowthRate().getCalculation().isDefault());
        assertEquals("criteriaLabel", penaltyClassDetail.getLabel());
        assertNotNull(penaltyClassDetail.getCriteria());
        assertEquals(Duration.ofDays(10), penaltyClassDetail.getCriteria().getLeeway());
        assertNotNull(penaltyClassDetail.getCriteria().getDisQualifier());
        assertNotNull(penaltyClassDetail.getCriteria().getTrigger());
    }

    @Test
    public void testEqualityDetailCreation() {
        assertNotNull(equalityDetail, "EqualityDetail should not be null");
        assertEquals(Set.of("100", "120"), equalityDetail.getValues(), "Values should match expected set");
    }

    @Test
    public void testIncludedPredicateCreation() {
        assertNotNull(includedPredicate, "Predicate should not be null");
        assertNotNull(includedPredicate.getDetail(), "Predicate should have a non-null detail");
        assertEquals("lhs",includedPredicate.getLhs(), "LHS should match expected value");
        assertEquals(12L, includedPredicate.getWeight(), "Weight should match expected value");
    }

    @Test
    public void testConjunctionCreation() {
        assertNotNull(conjunction, "Conjunction should not be null");
        assertNotNull(conjunction.getPredicates(), "Conjunction should have a non-null predicate");
    }

    @Test
    public void testDNFCriteriaCreation() {
        assertNotNull(dnfCriteria, "DNFCriteria should not be null");
        assertEquals("disqualifier", dnfCriteria.getId(), "ID should match expected value");
        assertNotNull(dnfCriteria.getConjunctions(), "DNFCriteria should have a non-null conjunction");
    }
}
