package com.phonepe.central.stratos.penalty.growth.calculation.impl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.growth.calculation.CalculationType;

import java.math.BigDecimal;

import com.phonepe.central.stratos.penalty.growth.calculation.FixedAmountCalculation;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({"type", "amount", "amountPath", "isDefault"})
public class TransactionDateFixedAmountCalculation extends FixedAmountCalculation {

    @Builder
    @JsonCreator
    public TransactionDateFixedAmountCalculation(@JsonProperty("amount") BigDecimal amount,
                                                       @JsonProperty("amountPath") String amountPath,
                                                       @JsonProperty("isDefault") boolean isDefault) {
        super(CalculationType.TRANSACTION_DATE_FIXED_AMOUNT, amountPath, isDefault, amount);
    }

    @Override
    public <T> T accept(CalculationVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
