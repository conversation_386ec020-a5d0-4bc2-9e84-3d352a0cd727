package com.phonepe.central.stratos.penalty.request.penalty;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.PenaltyStatus;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({"type", "penaltyClassId", "tenantName", "tenantSubCategoryName", "status", "penaltyId"})
public class PenaltyBasicSearchRequest extends PenaltySearchRequest {
    private PenaltyStatus status;
    private String penaltyId;

    @Builder
    @JsonCreator
    public PenaltyBasicSearchRequest(@JsonProperty("penaltyClassId") String penaltyClassId,
                                     @JsonProperty("state") PenaltyStatus status,
                                     @JsonProperty("penaltyId") String penaltyId) {
        super(PenaltySearchType.BASIC_SEARCH, penaltyClassId);
        this.status = status;
        this.penaltyId = penaltyId;
    }

    @Override
    public <T> T accept(PenaltySearchRequestVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
