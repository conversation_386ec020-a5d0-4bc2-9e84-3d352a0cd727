package com.phonepe.merchant.platform.stratos.models.udir.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.payments.upiclientmodel.enums.ComplaintRequestType;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@SuppressWarnings("java:S2637")
@Data
@EqualsAndHashCode(callSuper = true)
public class UdirOutgoingRaiseComplaintRequest extends UdirRaiseComplaintRequest {

    @NotNull
    private String paymentTransactionId;

    @NotNull
    private ComplaintRequestType requestCode;

    private long adjAmount;

    @NotNull
    private String complaintId;

    @NotNull
    private DisputeStageDto disputeStage;

    public UdirOutgoingRaiseComplaintRequest() {
        super(UdirDisputeTypeDto.UDIR_OUTGOING_COMPLAINT);
    }

    @Builder
    public UdirOutgoingRaiseComplaintRequest(String paymentTransactionId,
        ComplaintRequestType requestCode, long adjAmount,
        DisputeStageDto disputeStage,
        String complaintId) {
        this();
        this.paymentTransactionId = paymentTransactionId;
        this.requestCode = requestCode;
        this.adjAmount = adjAmount;
        this.disputeStage = disputeStage;
        this.complaintId = complaintId;
    }

    @Override
    public <T> T accept(UdirRaiseComplaintRequestVisitor<T> udirRaiseComplaintRequestVisitor) {
        return udirRaiseComplaintRequestVisitor.visit(this);
    }
}
