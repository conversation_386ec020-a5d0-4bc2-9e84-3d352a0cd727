package com.phonepe.merchant.platform.stratos.models.accountingevents;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingEvent {

    @Valid
    @NotNull
    @NonNull
    private AccountingEventHeader header;

    @Valid
    @NotNull
    @NonNull
    private AccountingEventTransaction transaction;
}
