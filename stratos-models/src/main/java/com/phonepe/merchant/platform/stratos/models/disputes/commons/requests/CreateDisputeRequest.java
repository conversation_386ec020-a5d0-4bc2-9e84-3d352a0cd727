package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeData;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeCategoryDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import java.io.Serializable;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import org.apache.commons.lang3.EnumUtils;

@Data
@ToString
@SuperBuilder
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "transactionType", visible = true)
@JsonSubTypes(value = {
    @Type(value = WalletDisputeRequest.class, name = "WALLET")
})
@NoArgsConstructor(force = true)
@AllArgsConstructor
public abstract class CreateDisputeRequest implements Serializable {

    @NotNull
    private TransactionType transactionType;

    @NotEmpty
    private String transactionId;

    @NotNull
    @Min(1)
    private Integer disputedAmount;

    @NotNull
    @Valid
    private DisputeData disputeData;

    private Map<String, String> metadata;

    protected CreateDisputeRequest(TransactionType transactionType, String transactionId,
        Integer disputedAmount,
        DisputeData disputeData) {
        this.transactionId = transactionId;
        this.transactionType = transactionType;
        this.disputedAmount = disputedAmount;
        this.disputeData = disputeData;
    }

    @JsonIgnore
    @AssertTrue(message = "Validate dispute type and dispute category")
    public boolean isValid() {
        assert disputeData != null;
        return (EnumUtils.isValidEnum(DisputeTypeDto.class,
            getDisputeType()) &&
            EnumUtils.isValidEnum(DisputeCategoryDto.class,
                getDisputeCategory()));
    }

    private String getDisputeType(){
        return String.join("_", String.valueOf(transactionType),
            String.valueOf(disputeData.getDisputeType()));
    }

    private String getDisputeCategory(){
        return String.join("_", String.valueOf(disputeData.getDisputeCategory()),
            String.valueOf(disputeData.getDisputeType()));
    }
}