package com.phonepe.merchant.platform.stratos.models.row;

import com.phonepe.merchant.platform.stratos.models.files.RowStateDto;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PgMisRowDto extends RowDto {

    private String rowId;
    private RowStateDto rowState;
    private String sourceId;

    private String transactionId;
    private String pgId;
    private String pgTransactionId;
    private RowTransactionType transactionType;
    private BigDecimal transactionAmount;
    private BigDecimal netAmount;
    private String code;
    private String interchange;

    public PgMisRowDto() {
        super(RowTypeDto.PG_MIS_ROW);
    }

    @SuppressWarnings("java:S107")
    @Builder
    public PgMisRowDto(final String rowId,
        final RowStateDto rowState,
        final String sourceId,
        final String transactionId,
        final String pgId,
        final String pgTransactionId,
        final RowTransactionType transactionType,
        final BigDecimal transactionAmount,
        final BigDecimal netAmount,
        final String code,
        final String interchange) {
        this();

        this.rowId = rowId;
        this.rowState = rowState;
        this.sourceId = sourceId;
        this.transactionId = transactionId;
        this.pgId = pgId;
        this.pgTransactionId = pgTransactionId;
        this.transactionType = transactionType;
        this.transactionAmount = transactionAmount;
        this.netAmount = netAmount;
        this.code = code;
        this.interchange = interchange;
    }
}
