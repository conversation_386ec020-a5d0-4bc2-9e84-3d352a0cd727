package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PartialContestPayload extends ContestPayload{
    private long contestedAmount;

    public PartialContestPayload(){
        this.setContestType(ContestType.PARTIAL_CONTEST);
    }

    @Builder
    public PartialContestPayload(
        final String merchantId,
        final String disputeId,
        final long contestedAmount) {
        super(ContestType.PARTIAL_CONTEST, merchantId, disputeId);
        this.contestedAmount = contestedAmount;
    }

}
