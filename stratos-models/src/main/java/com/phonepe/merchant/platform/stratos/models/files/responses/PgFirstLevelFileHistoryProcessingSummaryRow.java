package com.phonepe.merchant.platform.stratos.models.files.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Row Id",
    "Row State",
    "Code",
    "Bank Ref No",
    "Reason Code",
    "Bank Disputed Amount",
    "PG_ID(RET_BID)",
    "CB Received Date",
    "SM Transaction id(SRC_PRN)",
    "Source",
    "Dispute Reason",
})
public class PgFirstLevelFileHistoryProcessingSummaryRow {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("Bank Ref No")
    private String bankRefNo;

    @JsonProperty("Reason Code")
    private String reasonCode;

    @JsonProperty("Bank Disputed Amount")
    private String bankDisputedAmount;

    @JsonProperty("PG_ID(RET_BID)")
    private String pgId;

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;

    @JsonProperty("SM Transaction id(SRC_PRN)")
    private String transactionId;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Dispute Reason")
    private String disputeReason;

}
