package com.phonepe.merchant.platform.stratos.server.v2.actions.fraud;

import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.creations.CreateDisputeEntryBaseAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class FraudCreateEntryAction extends CreateDisputeEntryBaseAction {

    private final Provider<FraudCreationActor> creationHandlingActorProvider;
    private final DisputeService disputeService;

    @Inject
    public FraudCreateEntryAction(
            final DisputeRepository disputeRepository,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final Provider<FraudCreationActor> creationHandlingActorProvider,
            final DisputeService disputeService,
            final EventIngester eventIngester,
            final CallbackActor callbackActor) {
        super(disputeRepository, disputeWorkflowRepository, eventIngester, callbackActor);
        this.creationHandlingActorProvider = creationHandlingActorProvider;
        this.disputeService = disputeService;
    }
    @Override
    @SneakyThrows
    protected void preTransition(final Dispute dispute, final DisputeWorkflow disputeWorkflow) {
        ValidationUtils.validateEntryDisputeAmount(disputeWorkflow, dispute, disputeService);
    }

    @Override
    @SneakyThrows
    protected void postTransition(final Dispute dispute, final DisputeWorkflow disputeWorkflow) {
        creationHandlingActorProvider.get().publish(DisputeWorkflowMessage.builder()
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .build());

    }
}
