package com.phonepe.central.stratos.penalty.growth;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum GrowthUnit {
    DAY(GrowthUnit.DAY_TEXT) {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitDay();
        }
    },
    WEEK(GrowthUnit.WEEK_TEXT) {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitWeek();
        }
    },
    MONTH(GrowthUnit.MONTH_TEXT) {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitMonth();
        }
    },
    YEAR(GrowthUnit.YEAR_TEXT) {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitYear();
        }
    };

    public static final String DAY_TEXT = "DAY";
    public static final String WEEK_TEXT = "WEEK";
    public static final String MONTH_TEXT = "MONTH";
    public static final String YEAR_TEXT = "YEAR";

    @Getter
    private String value;

    public abstract <T> T accept(Visitor<T> visitor);

    public interface Visitor<T> {

        T visitDay();

        T visitWeek();

        T visitMonth();

        T visitYear();
    }

}
