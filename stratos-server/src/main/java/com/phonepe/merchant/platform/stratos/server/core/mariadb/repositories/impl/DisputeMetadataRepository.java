package com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl;


import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.NetBankingDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.DisbursementDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.CrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import io.appform.dropwizard.sharding.DBShardingBundle;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Slf4j
@Singleton
public class DisputeMetadataRepository extends CrudRepository<DisputeMetadata> {

    @Inject
    public DisputeMetadataRepository(
        final DBShardingBundle<StratosConfiguration> dbShardingBundle) {
        super(
            dbShardingBundle.createRelatedObjectDao(DisputeMetadata.class),
            dbShardingBundle.createParentObjectDao(DisputeMetadata.class)
        );
    }

    public Optional<DisputeMetadata> select(final String transactionReferenceId,
        final String disputeWorkflowId) {
        final var detachedCriteria = getDetachedCriteria(transactionReferenceId, disputeWorkflowId);
        return this.select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public List<DisputeMetadata> select(final String disputeWorkflowId,
        final Set<DisputeMetadataType> disputeMetadataTypes) {
        final var detachedCriteria =  DetachedCriteria.forClass(DisputeMetadata.class)
            .add(Restrictions.eq(Fields.disputeWorkflowId, disputeWorkflowId))
            .add(Restrictions.in(Fields.disputeMetadataType, disputeMetadataTypes));
        return this.select(disputeWorkflowId, detachedCriteria).stream()
            .toList();
    }

    protected DetachedCriteria getDetachedCriteria(final String transactionReferenceId,
        final String disputeWorkflowId) {
        return DetachedCriteria.forClass(DisputeMetadata.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeWorkflowId, disputeWorkflowId));
    }

    protected <T> List<T> selectAllDisputeMetada(final String transactionReferenceId,
        final DisputeMetadataType disputeMetadataType,
        final DetachedCriteria detachedCriteria, final Class<T> clazz) {

        detachedCriteria.add(Restrictions.eq(Fields.disputeMetadataType, disputeMetadataType));
        detachedCriteria.addOrder(Order.desc(Fields.updatedAt));

        return this.select(transactionReferenceId, detachedCriteria)
            .stream()
            .map(clazz::cast)
            .toList();
    }
    public List<DisputeMetadata> listDisputeMetadataFromRefundTxnId(
            final String refundTxnId) {

        final var detachedCriteria = DetachedCriteria.forClass(DisputeMetadata.class)
            .add(Restrictions.eq(NetBankingDisputeMetadata.Fields.disbursementTransactionId,
                refundTxnId))
            .addOrder(Order.desc(Fields.updatedAt));

        return this.scatterGather(detachedCriteria);
    }


    public Optional<DisbursementDisputeMetadata> getLatestDisbursementMetaData(
            final String disbursementId) {

        final var detachedCriteria = DetachedCriteria.forClass(DisputeMetadata.class)
                .add(Restrictions.eq(DisbursementDisputeMetadata.Fields.disbursementTransactionId,
                        disbursementId))
                .addOrder(Order.desc(Fields.updatedAt));

        return this.scatterGather(detachedCriteria).stream()
                .map(DisbursementDisputeMetadata.class::cast)
                .toList().stream().sorted((a,b) -> b.getUpdatedAt().compareTo(a.getUpdatedAt())).findFirst();
    }
}
