package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PartialAcceptanceTransitionContext extends TransitionContext {

    private long acceptedAmount;

    public PartialAcceptanceTransitionContext() {
        super(ContextType.PARTIAL_ACCEPTANCE_CONTEXT);
    }

    @Builder
    public PartialAcceptanceTransitionContext(final long acceptedAmount) {
        this();
        this.acceptedAmount = acceptedAmount;
    }
}
