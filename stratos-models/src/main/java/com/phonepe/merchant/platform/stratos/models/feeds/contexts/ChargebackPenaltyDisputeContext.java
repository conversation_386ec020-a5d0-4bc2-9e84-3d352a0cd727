package com.phonepe.merchant.platform.stratos.models.feeds.contexts;

import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargebackPenaltyDisputeContext extends DisputeContext {

    private String instrumentTransactionId;

    private String disputeReferenceId;

    private String rrn;

    private String chargebackPenaltySourceType;

    private String chargebackPenaltySourceId;

    private long penaltyAmount;

    public ChargebackPenaltyDisputeContext() {
        super(DisputeContextType.CHARGEBACK_PENALTY);
    }

    @Builder
    public ChargebackPenaltyDisputeContext(
        final String instrumentTransactionId,
        final String disputeReferenceId,
        final String rrn,
        final String chargebackPenaltySourceType,
        final String chargebackPenaltySourceId,
        final long penaltyAmount) {
        this();
        this.instrumentTransactionId = instrumentTransactionId;
        this.disputeReferenceId = disputeReferenceId;
        this.rrn = rrn;
        this.chargebackPenaltySourceType = chargebackPenaltySourceType;
        this.chargebackPenaltySourceId = chargebackPenaltySourceId;
        this.penaltyAmount = penaltyAmount;
    }
}
