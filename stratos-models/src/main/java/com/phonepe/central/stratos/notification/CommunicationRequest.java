package com.phonepe.central.stratos.notification;


import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.central.stratos.notification.CommunicationType.Names;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "communicationType", visible = true)
@JsonSubTypes(value = {
    @Type(value = EmailCommunicationRequest.class, name = Names.EMAIL)}
)
public abstract class CommunicationRequest {

    @NotNull
    public CommunicationType communicationType;

    public abstract void accept(CommunicationRequestVisitor visitor);

    public interface CommunicationRequestVisitor {

        void visit(final EmailCommunicationRequest emailCommunicationRequest);
    }
}
