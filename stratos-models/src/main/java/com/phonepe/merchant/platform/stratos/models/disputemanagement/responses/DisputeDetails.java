package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeDetails {
    @NotBlank
    private String disputeId;
    private DisputeData disputeData;
    @NotBlank
    private DisputeStatus disputeStatus;
    private TransactionType transactionType;
    private String transactionId;
    private String merchantTransactionId;
    private long transactionAmount;
    private LocalDateTime respondBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;
    private List<DisputeEvent> disputeTimeline;
}
