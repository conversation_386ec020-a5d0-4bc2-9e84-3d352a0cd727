package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import static javax.ws.rs.core.HttpHeaders.CONTENT_DISPOSITION;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.files.FileRow;
import com.phonepe.merchant.platform.stratos.models.files.RowContextDto;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileHistoryResponse;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileRowResponse;
import com.phonepe.merchant.platform.stratos.models.files.responses.FileUploadResponse;
import com.phonepe.merchant.platform.stratos.models.files.responses.PrimusSOFResponse;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.FileUploadLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.FileUploadLockKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.DocstoreClient;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.FileProcessorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.File;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.FileRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.FileState;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.FileTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.RowState;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.ApiClientCallbackRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.EligibilityRequest;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.PrimusFileState;
import com.phonepe.merchant.platform.stratos.server.core.models.primus.WorkflowState.PrimusWorkflowStateVisitor;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.FileProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.FileProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.FileService;
import com.phonepe.merchant.platform.stratos.server.core.services.WardenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.ErrorUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.FileUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.EdcFileHistoryProcessingSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.FileFilterVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.NBFileHistoryProcessingSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PgPreArbFileHistoryProcessingSummaryFileFormatVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.UpiFileHistoryProcessingSummaryFileFormatVisitor;
import com.phonepe.merchants.platform.primus.models.transform.TransformedContentBag;
import com.phonepe.olympus.im.models.user.UserDetails;
import com.phonepe.services.warden.core.models.responses.instance.WardenWorkflowInstance;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

import javax.ws.rs.core.Response;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
public class FileServiceImpl implements FileService {

    public static final String FILE_UNDER_PROCESS_PROCESS = "A file with '%s' name is under already process";

    private final DocstoreClient docstoreClient;
    private final FileProcessorRegistry processorFactory;
    private final FileRepository fileRepository;
    private final RowRepository rowRepository;
    private final WardenService wardenService;

    private final Map<String, FileConfig> fileConfigs;
    private final List<DisputeType> fileUploadChecker;

    private final Provider<FileProcessorActor> fileProcessorActorProvider;
    private final FileFilterVisitor fileFilterVisitor;
    private final FileUploadLockCommand fileUploadLockCommand;

    private final UpiFileHistoryProcessingSummaryFileFormatVisitor upiFileHistoryProcessingSummaryFileFormatVisitor;

    private final PgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor pgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor;

    private final PgPreArbFileHistoryProcessingSummaryFileFormatVisitor pgPreArbFileHistoryProcessingSummaryFileFormatVisitor;

    private final EdcFileHistoryProcessingSummaryFileFormatVisitor edcFileHistoryProcessingSummaryFileFormatVisitor;
    private final NBFileHistoryProcessingSummaryFileFormatVisitor nbFileHistoryProcessingSummaryFileFormatVisitor;

    private final EventIngester eventIngester;

    private final Map<ActionType, List<StratosErrorCodeKey>> actorIgnoreErrorCodes;

    @Inject
    @SuppressWarnings("java:S107")
    public FileServiceImpl(final DocstoreClient docstoreClient,
        final FileProcessorRegistry processorFactory,
        final FileRepository fileRepository,
        final WardenService wardenService,
        final Map<String, FileConfig> fileConfigs,
        final List<DisputeType> fileUploadChecker,
        final Provider<FileProcessorActor> fileProcessorActorProvider,
        final RowRepository rowRepository,
        final FileFilterVisitor fileFilterVisitor,
        FileUploadLockCommand fileUploadLockCommand,
        final UpiFileHistoryProcessingSummaryFileFormatVisitor upiFileHistoryProcessingSummaryFileFormatVisitor,
        final PgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor pgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor,
        final PgPreArbFileHistoryProcessingSummaryFileFormatVisitor pgFileHistoryProcessingSummaryFileFormatVisitor,
        EdcFileHistoryProcessingSummaryFileFormatVisitor edcFileHistoryProcessingSummaryFileFormatVisitor,
        NBFileHistoryProcessingSummaryFileFormatVisitor nbFileHistoryProcessingSummaryFileFormatVisitor,
        final EventIngester eventIngester,
        Map<ActionType, List<StratosErrorCodeKey>> actorIgnoreErrorCodes) {
        this.docstoreClient = docstoreClient;
        this.processorFactory = processorFactory;
        this.fileRepository = fileRepository;
        this.wardenService = wardenService;
        this.fileConfigs = fileConfigs;
        this.fileUploadChecker = fileUploadChecker;
        this.fileProcessorActorProvider = fileProcessorActorProvider;
        this.rowRepository = rowRepository;
        this.fileFilterVisitor = fileFilterVisitor;
        this.fileUploadLockCommand = fileUploadLockCommand;
        this.upiFileHistoryProcessingSummaryFileFormatVisitor = upiFileHistoryProcessingSummaryFileFormatVisitor;
        this.pgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor = pgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor;
        this.pgPreArbFileHistoryProcessingSummaryFileFormatVisitor = pgFileHistoryProcessingSummaryFileFormatVisitor;
        this.edcFileHistoryProcessingSummaryFileFormatVisitor = edcFileHistoryProcessingSummaryFileFormatVisitor;
        this.nbFileHistoryProcessingSummaryFileFormatVisitor = nbFileHistoryProcessingSummaryFileFormatVisitor;
        this.eventIngester=eventIngester;
        this.actorIgnoreErrorCodes = actorIgnoreErrorCodes;
    }

    @Override
    public FileUploadResponse upload(final InputStream inputStream, final String fileName,
        final DisputeType disputeType, final FileType fileType,
        final UserDetails userDetails, String authToken) {

        try {
            final var config = fileConfigs.get(FileUtils.fileIdentifier(disputeType, fileType));

            FileUtils.validateFileName(fileName, config);
            FileUtils.idempotentFileNameCheck(fileName, fileRepository);
            acquireUploadLock(fileName);

            final var fileBytes = IOUtils.toByteArray(inputStream);

            final var fileRows = processFileContent(fileBytes, config);

            final var fileUploadResponse = uploadFileToDocstore(fileBytes, fileName);

            log.debug("File Upload to Docstore response:{}", fileUploadResponse);

            File file = saveFileDetailsInStorage(fileUploadResponse, fileName, disputeType, fileType,
                    userDetails, fileRows.size());
            if(!fileUploadChecker.isEmpty() &&  fileUploadChecker.contains(disputeType)){
                WardenWorkflowInstance workflow = wardenService.raiseFileUploadRequest(
                        file, authToken);
                log.info("Maker request raised for file : {}, warden request id: {}",
                        file.getFileId(), workflow.getWorkflowId());
            }
            else {
                fileProcessorActorProvider.get().publish(FileProcessorMessage.builder()
                    .fileId(fileUploadResponse.getId())
                    .build());
            }

            log.debug("Published to file processor actor response:{}", fileUploadResponse);

            releaseUploadLock(fileName);

            eventIngester.generateEvent(
                FoxtrotEventUtils.toFileUploadEvent(fileUploadResponse, fileType, fileRows.size(),
                    FileState.ACCEPTED, disputeType, userDetails,"Successfully ingested file"));

            return fileUploadResponse;
        } catch (final Exception e) {
            log.error("Error in uploading file", e);
            eventIngester.generateEvent(
                FoxtrotEventUtils.toFileUploadEvent(
                    FileUploadResponse.builder().fileName(fileName).build(), fileType, 0,
                    FileState.FAILED, disputeType, userDetails,e.getMessage()));

            if( !(e instanceof DisputeException error && error.getErrorCode().equals(StratosErrorCodeKey.FILE_LOCK))){
                // if failure is not due to lock then release the lock
                // so that the file can be processed again
                releaseUploadLock(fileName);
            }

            throw DisputeExceptionUtil.propagate(e);
        }
    }

    private void acquireUploadLock(String fileName){

        var lockKey =  FileUploadLockKey.builder()
            .fileName(fileName)
            .build();

        try {
            fileUploadLockCommand.strictSave(
                lockKey, lockKey.getKey()
            );
        } catch (Exception e) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FILE_LOCK,
                Map.of(Constants.MESSAGE,String.format(FILE_UNDER_PROCESS_PROCESS, fileName)));
        }
    }

    private void releaseUploadLock(String fileName){
        fileUploadLockCommand.delete(FileUploadLockKey.builder()
            .fileName(fileName)
            .build());
    }

    private File saveFileDetailsInStorage(final FileUploadResponse fileUploadResponse,
        final String fileName,
        final DisputeType disputeType, final FileType fileType,
        final UserDetails userDetails,
        final int rowCount) {
        final var file = File.builder()
            .fileId(fileUploadResponse.getId())
            .fileName(fileName)
            .fileState(FileState.ACCEPTED)
            .fileType(fileType)
            .disputeType(disputeType)
            .userType(AuthorizationUtils.getUserType(userDetails))
            .gandalfUserId(userDetails.getUserId())
            .key(StorageUtils.primaryKey())
            .rowCount(rowCount)
            .build();
        return fileRepository.saveFile(fileUploadResponse.getId(), file);
    }

    private FileUploadResponse uploadFileToDocstore(final byte[] fileBytes, final String fileName) {
        final var docStoreUploadResponse =
            docstoreClient.pushFile(fileBytes, fileName);

        if (docStoreUploadResponse.isSuccess()) {

            return FileUploadResponse.builder()
                .id(docStoreUploadResponse.getContext().getId())
                .fileName(fileName)
                .build();
        }
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR, Collections.emptyMap());
    }


    @Override
    public FileHistoryResponse getHistory(final FileFilter filter) {

        final var fileRows = filter.accept(fileFilterVisitor).stream()
            .map(file -> FileDto.builder()
                .createdAt(file.getCreatedAt())
                .fileId(file.getFileId())
                .fileName(file.getFileName())
                .disputeType(DtoUtils.disputeTypeToDto(file.getDisputeType()))
                .fileType(DtoUtils.fileTypeToDto(file.getFileType()))
                .fileState(DtoUtils.fileStateToDto(file.getFileState()))
                .build())
            .collect(Collectors.toList());

        return FileHistoryResponse.builder()
            .rows(fileRows)
            .build();
    }

    @Override
    public Response getFile(final String fileId) {

        final var file = fileRepository.getFile(fileId);

        return Response.ok()
            .header(CONTENT_DISPOSITION, "attachment;filename=" + file.getFileName())
            .entity(docstoreClient.getFile(fileId))
            .build();
    }

    @Override
    public List<TransformedContentBag> processFileContent(final byte[] fileBytes,
        final FileConfig config) {

        final ByteArrayInputStream inputStream = new ByteArrayInputStream(fileBytes);
        return processorFactory.get(config.getFileFormat())
            .process(inputStream, config);
    }

    @Override
    public FileRowResponse fileRow(final String fileId) {
        return FileRowResponse.builder()
            .rows(
                this.rowRepository.select(fileId, DetachedCriteria.forClass(Row.class)
                        .add(Restrictions.eq(Fields.sourceId, fileId))
                        .add(Restrictions.eq(Fields.rowState, RowState.FAILED)))
                    .stream()
                    .map(row -> FileRow.builder()
                        .rowId(row.getRowId())
                        .rowContext(
                            Optional.ofNullable(row.getRowContext())
                                .map(context -> RowContextDto.builder()
                                    .code(context.getCode())
                                    .content(MapperUtils.deserialize(context.getContent(),
                                        new TypeReference<>() {
                                        })
                                    )
                                    .build())
                                .orElse(null))
                        .rowState(DtoUtils.rowStateToDto(row.getRowState()))
                        .build())
                    .collect(Collectors.toList()))
            .build();
    }
    @Override
    public byte[] downloadProcessingRow(FileFormat fileFormat, String fileId) {
        List<FileRow> fileRows = fileRow(fileId).getRows();
        var fileType = fileRepository.getFile(fileId).getFileType();
        return fileType.accept(new FileTypeVisitor<byte[]>() {
            @Override
            public byte[] visitYes() {
                return downloadProcessingRowsForUpi(fileRows,fileFormat);
            }

            @Override
            public byte[] visitApp() {
                return visitYes();
            }

            @Override
            public byte[] visitIcp() {
                return visitYes();
            }

            @Override
            public byte[] visitPgFirstLevel() {
                final var fileHistorySummaries = fileRows.stream()
                    .map(TransformationUtils::toPgFirstLevelFileHistoryPreArbProcessingSummaryRow)
                    .collect(Collectors.toList());
                return fileFormat.accept(pgFirstLevelFileHistoryProcessingSummaryFileFormatVisitor, fileHistorySummaries);
            }

            @Override
            public byte[] visitPgPreArb() {
                final var fileHistorySummaries = fileRows.stream()
                    .map(TransformationUtils::toPgPreArbFileHistoryFirstLevelProcessingSummaryRow)
                    .collect(Collectors.toList());
                return fileFormat.accept(pgPreArbFileHistoryProcessingSummaryFileFormatVisitor, fileHistorySummaries);
            }

            @Override
            public byte[] visitEdcFirstLevel() {
                final var fileHistorySummaries = fileRows.stream()
                    .map(TransformationUtils::toEdcFileHistoryProcessingSummaryRow)
                    .collect(Collectors.toList());
                return fileFormat.accept(edcFileHistoryProcessingSummaryFileFormatVisitor, fileHistorySummaries);
            }

            @Override
            public byte[] visitNetBankingFirstLevel() {
                final var fileHistorySummaries = fileRows.stream()
                    .map(TransformationUtils::toNBFileHistoryProcessingSummaryRow)
                    .collect(Collectors.toList());
                return fileFormat.accept(nbFileHistoryProcessingSummaryFileFormatVisitor, fileHistorySummaries);
            }

            @Override
            public byte[] visitPhp(){
                return visitYes();
            }

            @Override
            public byte[] visitBbpsToa() {
                return new byte[0]; //TODD: Handle this properly
            }

            @Override
            public byte[] visitPreChargeback() {
                return new byte[0]; // TODO check this
            }
        });
    }

    @Override
    public Response processPrimusFileSignal(EligibilityRequest primusFileSignal) {

        try{

            String[] disputeAndFileType = StringUtils.getDisputeTypeAndFileType(
                primusFileSignal.getConfigId());
            DisputeType disputeType = DisputeType.valueOf(disputeAndFileType[0]);
            FileType fileType = FileType.valueOf(disputeAndFileType[1]);
            String fileName = primusFileSignal.getFileName();
            String sourceId = primusFileSignal.getFileId();

            FileUtils.idempotentFileNameCheck(fileName, fileRepository);
            acquireUploadLock(fileName);

            File file = File.builder()
                .disputeType(disputeType)
                .fileType(fileType)
                .fileId(primusFileSignal.getFileId())
                .fileName(fileName)
                .rowCount((int) primusFileSignal.getRowCount())
                .fileState(FileState.PROCESSING)
                .userType(UserType.SYSTEM)
                .gandalfUserId("PRIMUS")
                .key(StorageUtils.primaryKey())
                .build();

            fileRepository.saveFile(sourceId, file);
            return Response.ok(PrimusSOFResponse.builder().canIngest(true).build()).build();
        } catch (DisputeException stratosError) {
            if (ErrorUtils.canIgnoreActorError(stratosError, ActionType.FILE_PROCESSOR,
                actorIgnoreErrorCodes)) {
                return Response.ok(PrimusSOFResponse.builder().canIngest(true).build()).build();
            } else {
                log.error("Failed to save file for file id: {} name: {}, due to {}",
                    primusFileSignal.getFileId(), primusFileSignal.getFileName(),
                    stratosError.getMessage());
                return Response.status(Integer.parseInt(stratosError.getErrorCode().getKey()))
                    .entity(Map.of("message",stratosError.getMessage()))
                    .build();
            }
        } catch (Exception e) {
            log.error("Exception occurred while processing file signal: {}, {}",primusFileSignal, e.getMessage());
            return Response.status(400)
                .entity(Map.of("success",false,"message",e.getMessage()))
                .build();
        }
    }

    @Override
    public Response processPrimusEOFSignal(ApiClientCallbackRequest primusEOFSignal) {

        return primusEOFSignal.getWorkflowState().visitor(
            new PrimusWorkflowStateVisitor<>() {
                @Override
                public Response visitCreated() {
                    return null;
                }

                @Override
                public Response visitSuccess() {

                    File file = fileRepository.getFile(primusEOFSignal.getFileId());
                    eventIngester.generateEvent(
                        FoxtrotEventUtils.toEOFEvent(file, "File processing completed by Primus", PrimusFileState.PRIMUS_PROCESSED));
                    return Response.ok().build();

                }

                @Override
                public Response visitFailure() {

                    File file = fileRepository.getFile(primusEOFSignal.getFileId());
                    file.setFileState(FileState.FAILED);
                    eventIngester.generateEvent(
                        FoxtrotEventUtils.toEOFEvent(file, "File processing failed by Primus", PrimusFileState.PRIMUS_FAILED));
                    return Response.ok().build();
                }

                @Override
                public Response visitPending() {
                    return null;
                }

                @Override
                public Response visitBlocked() {
                    return null;
                }

                @Override
                public Response visitError() {

                    File file = fileRepository.getFile(primusEOFSignal.getFileId());
                    file.setFileState(FileState.FAILED);
                    eventIngester.generateEvent(
                        FoxtrotEventUtils.toEOFEvent(file, "Error processing file by Primus", PrimusFileState.PRIMUS_ERROR));
                    return Response.ok().build();
                }

                @Override
                public Response visitPendingRetry() {
                    return null;
                }

                @Override
                public Response visitComputeEndState() {
                    return null;
                }

                @Override
                public Response visitEligibilityState() {
                    return null;
                }
            }
        );
    }

    byte[] downloadProcessingRowsForUpi( List<FileRow> fileRows, FileFormat fileFormat){
        final var fileHistorySummaries = fileRows.stream()
            .map(TransformationUtils::toUpiFileHistoryProcessingSummaryRow)
            .collect(Collectors.toList());
        return fileFormat.accept(upiFileHistoryProcessingSummaryFileFormatVisitor, fileHistorySummaries);
    }

}
