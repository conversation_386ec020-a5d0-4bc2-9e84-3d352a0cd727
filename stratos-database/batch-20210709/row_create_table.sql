CREATE TABLE `row` (
    `id` bigint NOT NULL AUTO_INCREMENT,
    `partition_id` int NOT NULL,
    `row_id` varchar(32) NOT NULL,
    `source_type` int NOT NULL,
    `source_id` varchar(32) NOT NULL,
    `row_state` int NOT NULL,
    `row_context` blob,
    `created_at` datetime NOT NULL DEFAULT current_timestamp,
    `updated_at` datetime NOT NULL DEFAULT current_timestamp ON UPDATE current_timestamp,
    PRIMARY KEY (`id`,`partition_id`),
    UNIQUE KEY `idx_unq_row_id` (`row_id`,`partition_id`),
    KEY `idx_row_state` (`row_state`,`created_at`),
    KEY `idx_source_id` (`created_at`,`source_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
 PARTITION BY RANGE (`partition_id`)
(
    PARTITION `p202106` VALUES LESS THAN (202107) ENGINE = InnoDB,
    PARTITION `p202107` VALUES LESS THAN (202108) ENGINE = InnoDB,
    PARTITION `p202108` VALUES LESS THAN (202109) ENGINE = InnoDB,
    PARTITION `p202109` VALUES LESS THAN (202110) ENGINE = InnoDB,
    PARTITION `p202110` VALUES LESS THAN (202111) ENGINE = InnoDB,
    PARTITION `p202111` VALUES LESS THAN (202112) ENGINE = InnoDB,
    PARTITION `p202112` VALUES LESS THAN (202113) ENGINE = InnoDB,
    PARTITION `p202201` VALUES LESS THAN (202202) ENGINE = InnoDB,
    PARTITION `p202202` VALUES LESS THAN (202203) ENGINE = InnoDB,
    PARTITION `p202203` VALUES LESS THAN (202204) ENGINE = InnoDB,
    PARTITION `p202204` VALUES LESS THAN (202205) ENGINE = InnoDB,
    PARTITION `p202205` VALUES LESS THAN (202206) ENGINE = InnoDB,
    PARTITION `p202206` VALUES LESS THAN (202207) ENGINE = InnoDB,
    PARTITION `p202207` VALUES LESS THAN (202208) ENGINE = InnoDB,
    PARTITION `p202208` VALUES LESS THAN (202209) ENGINE = InnoDB,
    PARTITION `p202209` VALUES LESS THAN (202210) ENGINE = InnoDB,
    PARTITION `p202210` VALUES LESS THAN (202211) ENGINE = InnoDB,
    PARTITION `p202211` VALUES LESS THAN (202212) ENGINE = InnoDB,
    PARTITION `p202212` VALUES LESS THAN (202213) ENGINE = InnoDB,
    PARTITION `p202301` VALUES LESS THAN (202302) ENGINE = InnoDB,
    PARTITION `p202302` VALUES LESS THAN (202303) ENGINE = InnoDB,
    PARTITION `p202303` VALUES LESS THAN (202304) ENGINE = InnoDB,
    PARTITION `p202304` VALUES LESS THAN (202305) ENGINE = InnoDB,
    PARTITION `p202305` VALUES LESS THAN (202306) ENGINE = InnoDB,
    PARTITION `p202306` VALUES LESS THAN (202307) ENGINE = InnoDB,
    PARTITION `p202307` VALUES LESS THAN (202308) ENGINE = InnoDB,
    PARTITION `p202308` VALUES LESS THAN (202309) ENGINE = InnoDB,
    PARTITION `p202309` VALUES LESS THAN (202310) ENGINE = InnoDB,
    PARTITION `p202310` VALUES LESS THAN (202311) ENGINE = InnoDB,
    PARTITION `p202311` VALUES LESS THAN (202312) ENGINE = InnoDB,
    PARTITION `p202312` VALUES LESS THAN (202313) ENGINE = InnoDB,
    PARTITION `p202401` VALUES LESS THAN (202402) ENGINE = InnoDB,
    PARTITION `p202402` VALUES LESS THAN (202403) ENGINE = InnoDB,
    PARTITION `p202403` VALUES LESS THAN (202404) ENGINE = InnoDB,
    PARTITION `p202404` VALUES LESS THAN (202405) ENGINE = InnoDB,
    PARTITION `p202405` VALUES LESS THAN (202406) ENGINE = InnoDB,
    PARTITION `p202406` VALUES LESS THAN (202407) ENGINE = InnoDB,
    PARTITION `p202407` VALUES LESS THAN (202408) ENGINE = InnoDB,
    PARTITION `p202408` VALUES LESS THAN (202409) ENGINE = InnoDB,
    PARTITION `p202409` VALUES LESS THAN (202410) ENGINE = InnoDB,
    PARTITION `p202410` VALUES LESS THAN (202411) ENGINE = InnoDB,
    PARTITION `p202411` VALUES LESS THAN (202412) ENGINE = InnoDB,
    PARTITION `p202412` VALUES LESS THAN (202413) ENGINE = InnoDB,
    PARTITION `p202501` VALUES LESS THAN (202502) ENGINE = InnoDB,
    PARTITION `p202502` VALUES LESS THAN (202503) ENGINE = InnoDB,
    PARTITION `p202503` VALUES LESS THAN (202504) ENGINE = InnoDB,
    PARTITION `p202504` VALUES LESS THAN (202505) ENGINE = InnoDB,
    PARTITION `p202505` VALUES LESS THAN (202506) ENGINE = InnoDB,
    PARTITION `p202506` VALUES LESS THAN (202507) ENGINE = InnoDB,
    PARTITION `p202507` VALUES LESS THAN (202508) ENGINE = InnoDB,
    PARTITION `p202508` VALUES LESS THAN (202509) ENGINE = InnoDB,
    PARTITION `p202509` VALUES LESS THAN (202510) ENGINE = InnoDB,
    PARTITION `p202510` VALUES LESS THAN (202511) ENGINE = InnoDB,
    PARTITION `p202511` VALUES LESS THAN (202512) ENGINE = InnoDB,
    PARTITION `p202512` VALUES LESS THAN (202513) ENGINE = InnoDB,
    PARTITION `p202601` VALUES LESS THAN (202602) ENGINE = InnoDB,
    PARTITION `p202602` VALUES LESS THAN (202603) ENGINE = InnoDB,
    PARTITION `p202603` VALUES LESS THAN (202604) ENGINE = InnoDB,
    PARTITION `p202604` VALUES LESS THAN (202605) ENGINE = InnoDB,
    PARTITION `p202605` VALUES LESS THAN (202606) ENGINE = InnoDB,
    PARTITION `p202606` VALUES LESS THAN (202607) ENGINE = InnoDB,
    PARTITION `p202607` VALUES LESS THAN (202608) ENGINE = InnoDB,
    PARTITION `p202608` VALUES LESS THAN (202609) ENGINE = InnoDB,
    PARTITION `p202609` VALUES LESS THAN (202610) ENGINE = InnoDB,
    PARTITION `p202610` VALUES LESS THAN (202611) ENGINE = InnoDB,
    PARTITION `p202611` VALUES LESS THAN (202612) ENGINE = InnoDB,
    PARTITION `p202612` VALUES LESS THAN (202613) ENGINE = InnoDB,
    PARTITION `p202701` VALUES LESS THAN (202702) ENGINE = InnoDB,
    PARTITION `p202702` VALUES LESS THAN (202703) ENGINE = InnoDB,
    PARTITION `p202703` VALUES LESS THAN (202704) ENGINE = InnoDB,
    PARTITION `p202704` VALUES LESS THAN (202705) ENGINE = InnoDB,
    PARTITION `p202705` VALUES LESS THAN (202706) ENGINE = InnoDB,
    PARTITION `p202706` VALUES LESS THAN (202707) ENGINE = InnoDB,
    PARTITION `p202707` VALUES LESS THAN (202708) ENGINE = InnoDB,
    PARTITION `p202708` VALUES LESS THAN (202709) ENGINE = InnoDB,
    PARTITION `p202709` VALUES LESS THAN (202710) ENGINE = InnoDB,
    PARTITION `p202710` VALUES LESS THAN (202711) ENGINE = InnoDB,
    PARTITION `p202711` VALUES LESS THAN (202712) ENGINE = InnoDB,
    PARTITION `p202712` VALUES LESS THAN (202713) ENGINE = InnoDB,
    PARTITION `p202801` VALUES LESS THAN (202802) ENGINE = InnoDB,
    PARTITION `p202802` VALUES LESS THAN (202803) ENGINE = InnoDB,
    PARTITION `p202803` VALUES LESS THAN (202804) ENGINE = InnoDB,
    PARTITION `p202804` VALUES LESS THAN (202805) ENGINE = InnoDB,
    PARTITION `p202805` VALUES LESS THAN (202806) ENGINE = InnoDB,
    PARTITION `p202806` VALUES LESS THAN (202807) ENGINE = InnoDB,
    PARTITION `p202807` VALUES LESS THAN (202808) ENGINE = InnoDB,
    PARTITION `p202808` VALUES LESS THAN (202809) ENGINE = InnoDB,
    PARTITION `p202809` VALUES LESS THAN (202810) ENGINE = InnoDB,
    PARTITION `p202810` VALUES LESS THAN (202811) ENGINE = InnoDB,
    PARTITION `p202811` VALUES LESS THAN (202812) ENGINE = InnoDB,
    PARTITION `p202812` VALUES LESS THAN (202813) ENGINE = InnoDB,
    PARTITION `p202901` VALUES LESS THAN (202902) ENGINE = InnoDB,
    PARTITION `p202902` VALUES LESS THAN (202903) ENGINE = InnoDB,
    PARTITION `p202903` VALUES LESS THAN (202904) ENGINE = InnoDB,
    PARTITION `p202904` VALUES LESS THAN (202905) ENGINE = InnoDB,
    PARTITION `p202905` VALUES LESS THAN (202906) ENGINE = InnoDB,
    PARTITION `p202906` VALUES LESS THAN (202907) ENGINE = InnoDB,
    PARTITION `p202907` VALUES LESS THAN (202908) ENGINE = InnoDB,
    PARTITION `p202908` VALUES LESS THAN (202909) ENGINE = InnoDB,
    PARTITION `p202909` VALUES LESS THAN (202910) ENGINE = InnoDB,
    PARTITION `p202910` VALUES LESS THAN (202911) ENGINE = InnoDB,
    PARTITION `p202911` VALUES LESS THAN (202912) ENGINE = InnoDB,
    PARTITION `p202912` VALUES LESS THAN (202913) ENGINE = InnoDB,
    PARTITION `p203001` VALUES LESS THAN (203002) ENGINE = InnoDB,
    PARTITION `p203002` VALUES LESS THAN (203003) ENGINE = InnoDB,
    PARTITION `p203003` VALUES LESS THAN (203004) ENGINE = InnoDB,
    PARTITION `p203004` VALUES LESS THAN (203005) ENGINE = InnoDB,
    PARTITION `p203005` VALUES LESS THAN (203006) ENGINE = InnoDB,
    PARTITION `p203006` VALUES LESS THAN (203007) ENGINE = InnoDB,
    PARTITION `p203007` VALUES LESS THAN (203008) ENGINE = InnoDB,
    PARTITION `p203008` VALUES LESS THAN (203009) ENGINE = InnoDB,
    PARTITION `p203009` VALUES LESS THAN (203010) ENGINE = InnoDB,
    PARTITION `p203010` VALUES LESS THAN (203011) ENGINE = InnoDB,
    PARTITION `p203011` VALUES LESS THAN (203012) ENGINE = InnoDB,
    PARTITION `p203012` VALUES LESS THAN (203013) ENGINE = InnoDB
);