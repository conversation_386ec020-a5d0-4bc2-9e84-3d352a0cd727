package com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.CrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import io.appform.dropwizard.sharding.DBShardingBundle;
import java.util.List;
import java.util.Optional;

import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class DisputeWorkflowRepository extends CrudRepository<DisputeWorkflow> {

    @Inject
    public DisputeWorkflowRepository(
        final DBShardingBundle<StratosConfiguration> dbShardingBundle) {
        super(
            dbShardingBundle.createRelatedObjectDao(DisputeWorkflow.class),
            null
        );
    }

    public Optional<DisputeWorkflow> select(
        final String transactionReferenceId,
        final String disputeWorkflowId) {
        final var detachedCriteria = getDetachedCriteria(transactionReferenceId, disputeWorkflowId);
        return select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public Optional<DisputeWorkflow> select(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage) {
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeType, disputeType))
            .add(Restrictions.eq(Fields.disputeStage, disputeStage))
            .add(Restrictions.not(
                Restrictions.in(Fields.currentState, Constants.CANCELLED_DISPUTES)));
        return select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public List<DisputeWorkflow> selectAll(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage) {
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeType, disputeType))
            .add(Restrictions.eq(Fields.disputeStage, disputeStage))
            .add(Restrictions.not(
                Restrictions.in(Fields.currentState, Constants.CANCELLED_DISPUTES)));
        return select(transactionReferenceId, detachedCriteria);
    }

    public List<DisputeWorkflow> select( final String transactionReferenceId){
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .addOrder(Order.desc(Fields.createdAt));
        return select(transactionReferenceId, detachedCriteria);
    }

    public List<DisputeWorkflow> selectByWorkflowId( final String disputeWorkflowId){
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
                .add(Restrictions.eq(Fields.disputeWorkflowId, disputeWorkflowId));
        return scatterGather(detachedCriteria);
    }

    public List<DisputeWorkflow> selectByWorkflowState(
        final DetachedCriteria detachedCriteria) {
        return scatterGather(detachedCriteria);
    }

    public boolean updateState(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final DisputeWorkflowState disputeWorkflowState,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final String gandalfUserId,
        final UserType userType) {
        final var detachedCriteria = getDetachedCriteria(transactionReferenceId, disputeWorkflowId);
        return update(transactionReferenceId, detachedCriteria, disputeWorkflow -> {
            disputeWorkflow.setCurrentState(disputeWorkflowState);
            disputeWorkflow.setCurrentEvent(disputeWorkflowEvent);
            disputeWorkflow.setGandalfUserId(gandalfUserId);
            disputeWorkflow.setUserType(userType);
            return disputeWorkflow;
        });
    }

    public void updateCommunicationId(
        final String transactionReferenceId,
        final String disputeWorkflowId,
        final String communicationId,
        final String gandalfUserId,
        final UserType userType) {
        final var detachedCriteria = getDetachedCriteria(transactionReferenceId, disputeWorkflowId);
        update(transactionReferenceId, detachedCriteria, disputeWorkflow -> {
            disputeWorkflow.setCommunicationId(communicationId);
            disputeWorkflow.setGandalfUserId(gandalfUserId);
            disputeWorkflow.setUserType(userType);
            return disputeWorkflow;
        });
    }
    public List<DisputeWorkflow> scatterGatherByCommunicationId(String communicationId) {
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.eq(Fields.communicationId,communicationId))
            .add(Restrictions.not(
                Restrictions.in(Fields.currentState, Constants.CANCELLED_DISPUTES)));
        return scatterGather(detachedCriteria);
    }


    public Optional<DisputeWorkflow> select(final DisputeType disputeType, final String disputeReferenceId) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class, "disputeWorkflow")
                .createAlias("disputeWorkflow.dispute", "dispute")
                .add(Restrictions.eq("dispute.disputeType", disputeType))
                .add(Restrictions.eq("dispute.disputeReferenceId", disputeReferenceId))
                .addOrder(Order.desc("dispute.createdAt"));
        return scatterGather(detachedCriteria).stream().findFirst();
    }

    public boolean updateAcceptedAmount(
            final String transactionId,
            final String workflowId,
            long acceptedAmount
    ) {
        final var criteria = getDetachedCriteria(transactionId,workflowId);
        return update(transactionId,criteria,d->{
            FinancialDisputeWorkflow dw = DisputeWorkflowUtils.getFinancialDisputeWorkflow(d);
            dw.setAcceptedAmount(acceptedAmount);
            return dw;
        });
    }

    public List<DisputeWorkflow> selectByDisputeId(final String disputeId) {
        DetachedCriteria detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class, "disputeWorkflow")
                .createAlias("disputeWorkflow.dispute", "dispute")
                .add(Restrictions.eq("dispute.disputeId", disputeId))
                .addOrder(Order.desc(Fields.createdAt));
        return scatterGather(detachedCriteria);
    }

    private DetachedCriteria getDetachedCriteria(final String transactionReferenceId,
        final String disputeWorkflowId) {
        return DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeWorkflowId, disputeWorkflowId));
    }
}
