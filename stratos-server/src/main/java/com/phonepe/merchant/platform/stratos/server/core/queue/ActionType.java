package com.phonepe.merchant.platform.stratos.server.core.queue;

public enum ActionType {
    FILE_PROCESSOR,
    FILE_ROW_PROCESSOR,
    UPI_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER,
    RAISE_CHARGEBACK_RECOVERY_ACCOUNTING_EVENT,
    CH<PERSON><PERSON>_CHARGEBACK_RECOVERY_ACCOUNTING_EVENT_STATUS,
    CHAR<PERSON><PERSON>CK_RECOVERY_FEED_PUBLISHER,
    RAISE_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT,
    CHECK_CHARGEBACK_RECOVERY_REVERSAL_ACCOUNTING_EVENT_STATUS,
    <PERSON><PERSON><PERSON>BAC<PERSON>_RECOVERY_REVERSAL_FEED_PUBLISHER,
    PG_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER,
    PG_MIS_ROW_PROCESSOR,
    TOA_PROCESSOR_HANDLER,
    P2PM_TOA_CREATION_HANDLER,
    P2PM_TOA_PAY_STATUS_CHECK_HANDLER,
    P2PM_TOA_PAY_RETRY_HANDLER,
    EDC_CHARGEBACK_CREATION_HANDLER,
    EDC_ROW_PROCESSOR,
    KRATOS_WORKFLOW_PROCESSOR,
    NETBANKING_FIRST_LEVEL_CHARGEBACK_CREATION_HANDLER,
    STATE_CHANGE_HANDLER,
    CREATE_TOA_HANDLER,
    TOA_PAY_STATUS_CHECK_HANDLER,
    TOA_PAY_RETRY_HANDLER,
    AUTO_APPROVAL_HANDLER,
    CALLBACK_HANDLER,
    KAIZEN_CALLBACK,
    ESCALATED_NOTIFICATION_HANDLER,
    RAISE_HOLD_RECOVERY_ACCOUNTING_HANDLER,
    CHECK_HOLD_RECOVERY_ACCOUNTING_STATUS_HANDLER,
    RAISE_HOLD_RECOVERY_REVERSAL_ACCOUNTING_HANDLER,
    CHECK_HOLD_RECOVERY_REVERSAL_ACCOUNTING_STATUS_HANDLER,
    FRA_PRE_CHARGEBACK_CREATION_HANDLER,
    DISPUTE_ENTITY_FEED_PUBLISHER;

}
