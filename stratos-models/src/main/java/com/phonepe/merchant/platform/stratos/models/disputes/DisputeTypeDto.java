package com.phonepe.merchant.platform.stratos.models.disputes;

import lombok.experimental.UtilityClass;
/* Dto of DisputeType Class. Please Update this whenever there is any change in DisputeType class */
public enum DisputeTypeDto {
    UPI_CHARGEBACK,
    PG_CHARGEBACK,
    UDIR_OUTGOING_COMPLAINT,
    UDIR_INCOMING_COMPLAINT,
    P2PM_TOA,
    EDC_CHARGEBACK,
    NB_CHARGEBACK,
    NOTIONAL_CREDIT_TOA,
    WALLET_CHARGEBACK,
    BBPS_TAT_BREACH_TOA,
    FRA_FRAUD,
    ;

    @UtilityClass
    public static final class Names {

        public static final String UDIR_OUTGOING_COMPLAINT = "UDIR_OUTGOING_COMPLAINT";
        public static final String WALLET_CHARGEBACK = "WALLET_CHARGEBACK";
    }
}