package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.CreditDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.actions.UpiChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.actions.UpiUnBlockAndCreditReceivedAction;
import java.util.Set;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Slf4j
@Singleton
public class UpiFirstLevelChargebackStateMachine extends UpiChargebackStateMachine {

    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction;
    private final CreditDisputeAction creditDisputeAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final UpiUnBlockAndCreditReceivedAction upiUnBlockAndCreditReceivedAction;

    @Inject
    @SuppressWarnings("java:S107")
    public UpiFirstLevelChargebackStateMachine(
        final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final RaiseChargebackRecoveryReversalAccountingEventAction raiseReversalAccountingEventAction,
        final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateAction,
        final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateAction,
        final ApproveRecoverChargebackAction approveRecoverChargebackAction,
        final UpiChargebackCreateEntryAction createEntryAction,
        final AcceptPartialDisputeAction acceptPartialDisputeAction,
        final CreditDisputeAction creditDisputeAction,
        final ResetChargebackAction resetChargebackAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final FraudCheckDisputeAction fraudCheckUpdateDisputeAction, final AcceptDisputeAction acceptDisputeAction,
        final BlockRefundAction blockRefundAction,
        final UnblockRefundAction unblockRefundAction,
        final UpiUnBlockAndCreditReceivedAction upiUnBlockAndCreditReceivedAction) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, createEntryAction,
            mandatoryCommentUpdateAction, optionalCommentUpdateAction, updateDisputeStateAction,
            fraudCheckUpdateDisputeAction,acceptDisputeAction,acceptPartialDisputeAction,
            raiseReversalAccountingEventAction, approveRecoveryReversalChargebackAction,
            raiseAccountingEventAction,approveRecoverChargebackAction,
            blockRefundAction, unblockRefundAction,resetChargebackAction
            );
        this.mandatoryCommentUpdateAction = mandatoryCommentUpdateAction;
        this.optionalCommentUpdateAction = optionalCommentUpdateAction;
        this.creditDisputeAction = creditDisputeAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.upiUnBlockAndCreditReceivedAction = upiUnBlockAndCreditReceivedAction;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.UPI_CHARGEBACK)
            .disputeStage(DisputeStage.FIRST_LEVEL)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .build();
    }

    @Override
    protected Set<DisputeWorkflowState> endStates() {
        return Set.of(DisputeWorkflowState.CREDIT_RECEIVED, DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED);
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        baseTransitions(transitions)
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED)
            .event(DisputeWorkflowEvent.REQUEST_RGCS_ACCEPTANCE)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RGCS_ACCEPTANCE_REQUIRED)
            .target(DisputeWorkflowState.RGCS_ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_RGCS_ACCEPTANCE)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CREDIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_CREDIT)
            .action(creditDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.CREDIT_RECEIVED)
            .event(DisputeWorkflowEvent.REPRESENTMENT_REQUIRED_TO_CREDIT_RECEIVED)
            .action(upiUnBlockAndCreditReceivedAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.CREDIT_RECEIVED)
            .event(DisputeWorkflowEvent.FULFILMENT_DOCUMENTS_RECEIVED_TO_CREDIT_RECEIVED)
            .action(upiUnBlockAndCreditReceivedAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT)
            .action(creditDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_CREDIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.NPCI_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(optionalCommentUpdateAction)

        ;
    }
}
