package com.phonepe.central.stratos.penalty.request.penalty;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PenaltySearchType {
    BASIC_SEARCH(PenaltySearchType.BASIC_SEARCH_TEXT),
    DATE_RANGE_SEARCH(PenaltySearchType.DATE_RANGE_SEARCH_TEXT);
    public static final String BASIC_SEARCH_TEXT = "BASIC_SEARCH";
    public static final String DATE_RANGE_SEARCH_TEXT = "DATE_RANGE_SEARCH";

    @Getter
    private final String value;
}
