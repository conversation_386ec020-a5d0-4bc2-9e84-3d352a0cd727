package com.phonepe.central.stratos.penalty.meta;

import com.phonepe.growth.mustang.criteria.Criteria;
import java.time.Duration;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyCriteria {

    @Valid
    @NotNull
    private Criteria trigger;
    @Valid
    @NotNull
    private Duration leeway;
    @Valid
    @NotNull
    private Criteria disQualifier;

}
