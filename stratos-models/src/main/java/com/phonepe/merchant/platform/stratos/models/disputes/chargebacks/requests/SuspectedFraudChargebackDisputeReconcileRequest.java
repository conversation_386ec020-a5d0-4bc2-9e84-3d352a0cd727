package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileTypeVisitor;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SuspectedFraudChargebackDisputeReconcileRequest extends DisputeReconcileRequest {

    public SuspectedFraudChargebackDisputeReconcileRequest() {
        super(ReconcileType.SUSPECTED_FRAUD_RECONCILE);
    }

    @Override
    public <T> T accept(ReconcileTypeVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
