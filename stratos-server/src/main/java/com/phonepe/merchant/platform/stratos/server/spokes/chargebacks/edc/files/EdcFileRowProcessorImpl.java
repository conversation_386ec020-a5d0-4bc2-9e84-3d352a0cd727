package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.files;

import com.google.inject.Inject;
import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.files.models.EdcFileRowMeta;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;

@Slf4j
public class EdcFileRowProcessorImpl extends BaseFileRowProcessor<EdcTransactionDetailsResponse> {

    private final EdcService edcService;
    private final IdHelper idHelper;
    private final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap;


    @Inject
    public EdcFileRowProcessorImpl(
        final HandleBarsService handleBarsService,
        final DisputeService disputeService,
        final MerchantMandateService merchantMandateService,
        final EdcService edcService, IdHelper idHelper,
        final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.edcService = edcService;
        this.idHelper = idHelper;
        this.disputeWorkflowVersionMap = disputeWorkflowVersionMap;
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(
        final EdcTransactionDetailsResponse transactionDetails,
        final FinancialDisputeWorkflow disputeWorkflowFilePojo, final String fileId,
        final DisputeType disputeType,
        final FileConfig fileConfig, String disputeId) {

        final var ttlInDays = Optional.ofNullable(disputeWorkflowFilePojo.getDisputeStage())
            .map(disputeStage -> fileConfig.getDisputeStageTTLDaysMap()
                .get(disputeStage))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of(Constants.MESSAGE,
                    "Invalid TTL config for disputeStage: "
                        + disputeWorkflowFilePojo.getDisputeStage())));

        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeWorkflowId(idHelper.disputeWorkflowId(transactionDetails.getPaymentReferenceId()))
            .disputeWorkflowVersion(disputeWorkflowVersionMap.get(disputeType))
            .disputeSourceType(SourceType.FILE)
            .disputeSourceId(fileId)
            .transactionReferenceId(transactionDetails.getPaymentReferenceId())
            .disputeType(disputeType)
            .disputeStage(disputeWorkflowFilePojo.getDisputeStage())
            .currentEvent(disputeWorkflowFilePojo.getCurrentEvent())
            .currentState(disputeWorkflowFilePojo.getCurrentState())
            .disputedAmount(disputeWorkflowFilePojo.getDisputedAmount())
            .raisedAt(disputeWorkflowFilePojo.getRaisedAt())
            .respondBy(disputeWorkflowFilePojo.getRaisedAt().plusDays(ttlInDays))
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .userType(UserType.SYSTEM)
            .disputeId(disputeId)
            .build();
    }

    @Override
    public EdcTransactionDetailsResponse getTransactionDetails(final String instrumentTransactionId,
        final FileRowMeta fileRowMeta) {
        return edcService.getEdcTransactionDetails(getTenant((EdcFileRowMeta) fileRowMeta),
            getMerchantId((EdcFileRowMeta) fileRowMeta), getTerminal((EdcFileRowMeta) fileRowMeta),
            getUtr((EdcFileRowMeta) fileRowMeta));
    }

    @Override
    public Dispute getDispute(final DisputeWorkflow disputeWorkflow,
        final EdcTransactionDetailsResponse transactionDetail,
        final Dispute disputeFilePojo, final DisputeType disputeType,
        final String instrumentTransactionId) {
        return disputeWorkflow.getDisputeStage().accept(new DisputeStageVisitor<Dispute>() {
            @SneakyThrows
            @Override
            public Dispute visitFirstLevel() {
                return enrichDisputeDetails(transactionDetail, disputeFilePojo, disputeType);
            }

            @Override
            public Dispute visitPreArbitration() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                    Constants.MESSAGE, "EDC chargeback cannot be raised for second level"));
            }

            @Override
            public Dispute visitPreChargeback() {
                return null;
            }
        });
    }

    private Dispute enrichDisputeDetails(final EdcTransactionDetailsResponse transactionDetail,
        final Dispute disputeFilePojo, final DisputeType disputeType) {
        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeId(idHelper.disputeId(transactionDetail.getPaymentReferenceId()))
            .transactionReferenceId(transactionDetail.getPaymentReferenceId())
            .disputeType(disputeType)
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .merchantId(transactionDetail.getMerchantId())
            .merchantTransactionId(transactionDetail.getMerchantTransactionId())
            .disputeReferenceId(StringUtils.join(disputeFilePojo.getRrn(),
                disputeFilePojo.getInstrumentTransactionId()))
            .transactionAmount(Long.parseLong(transactionDetail.getAmount()))
            .rrn(disputeFilePojo.getRrn())
            .disputeCategory(disputeFilePojo.getDisputeCategory())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .instrumentTransactionId(transactionDetail.getTerminalId())
            .build();
    }
    private String getUtr(final EdcFileRowMeta edcFileRowMeta) {
        return edcFileRowMeta.getUtr().replace("'", ""); // Hack To be removed
    }

    private String getTenant(final EdcFileRowMeta edcFileRowMeta) {
        return edcFileRowMeta.getTenant();
    }

    private String getTerminal(final EdcFileRowMeta edcFileRowMeta) {
        return edcFileRowMeta.getTerminalId();
    }

    private String getMerchantId(final EdcFileRowMeta edcFileRowMeta) {
        return edcFileRowMeta.getMerchantId();
    }
}
