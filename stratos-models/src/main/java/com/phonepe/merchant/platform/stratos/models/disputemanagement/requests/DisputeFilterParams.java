package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeFilterParams {
    @NotBlank
    private String merchantId;
    @Valid
    private DateRangeParam dateRange;
    private DisputeStatus status;
    private int limit;
    private int offset;
}
