package com.phonepe.merchant.platform.stratos.models.udir.requests;


import static com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto.Names.UDIR_OUTGOING_COMPLAINT;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "disputeType", visible = true)
@JsonSubTypes(value = {
    @Type(value = UdirOutgoingRaiseComplaintRequest.class, name = UDIR_OUTGOING_COMPLAINT)
})
public abstract class UdirRaiseComplaintRequest {

    private UdirDisputeTypeDto disputeType;

    public abstract <T> T accept(
        UdirRaiseComplaintRequestVisitor<T> udirRaiseComplaintRequestVisitor);
}
