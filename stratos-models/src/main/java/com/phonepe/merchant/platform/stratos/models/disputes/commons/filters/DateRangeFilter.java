package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeCategoryDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeIssuerDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class DateRangeFilter extends DisputeFilter {

    @Valid
    @NotNull
    private DateRange dateRange;

    @Valid
    private Set<DisputeTypeDto> disputeTypes;
    @Valid
    private Set<DisputeStageDto> disputeStages;

    @Valid
    private Set<DisputeCategoryDto> disputeCategories;
    @Valid
    private Set<DisputeIssuerDto> disputeIssuers;

    public DateRangeFilter() {
        super(DisputeFilterType.DATE_RANGE);
    }

    @Builder
    public DateRangeFilter(final DateRange dateRange,
        final Set<DisputeStageDto> disputeStages, final Set<DisputeTypeDto> disputeTypes,
        final Set<DisputeCategoryDto> disputeCategories, final Set<DisputeIssuerDto> disputeIssuers) {
        this();
        this.dateRange = dateRange;
        this.disputeStages = disputeStages;
        this.disputeTypes = disputeTypes;
        this.disputeCategories = disputeCategories;
        this.disputeIssuers = disputeIssuers;
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
