package com.phonepe.central.stratos.notification;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.central.stratos.notification.impl.PenaltyCreatedNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyDisbursedNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyDueNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyGrowingNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyProbableNotification;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(name = NotificationType.PENALTY_PROBABLE_IDENTIFIED_TEXT, value = PenaltyProbableNotification.class),
        @JsonSubTypes.Type(name = NotificationType.PENALTY_DUE_SHORTLY_TEXT, value = PenaltyDueNotification.class),
        @JsonSubTypes.Type(name = NotificationType.PENALTY_CREATED_TEXT, value = PenaltyCreatedNotification.class),
        @JsonSubTypes.Type(name = NotificationType.PENALTY_GROWING_TEXT, value = PenaltyGrowingNotification.class),
        @JsonSubTypes.Type(name = NotificationType.PENALTY_DISBURSED_TEXT, value = PenaltyDisbursedNotification.class), })
public abstract class ChangeNotitification {
    private NotificationType type;

    public abstract <T> T accept(ChangeNotificationVisitor<T> visitor);

}
