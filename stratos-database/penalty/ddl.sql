CREATE TABLE `penalty_class` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                 `name` varchar(250) NOT NULL,
                                 `penalty_class_id` varchar(64) NOT NULL,
                                 `description` varchar(1024) NOT NULL,
                                 `state` varchar(256) NOT NULL,
                                 `version` int(3) NOT NULL DEFAULT 1,
                                 `tenant_name` varchar(256) NOT NULL,
                                 `tenant_subcategory_name` varchar(256) NOT NULL,
                                 `created_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                                 `updated_at` datetime(3) NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                                 PRIMARY KEY (`id`),
                                 UNIQUE KEY `UK_PENALTY_CLASS_ID` (`penalty_class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


CREATE TABLE `penalty_class_details` (
                                         `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                         `penalty_class_id` varchar(64) NOT NULL,
                                         `label` varchar(256) NOT NULL,
                                         `penalty_cap` int NOT NULL,
                                         `criteria_config_data` JSON NOT NULL,
                                         `growth_config_data` JSON NOT NULL,
                                         `created_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                                         `updated_at` datetime(3) NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                                         PRIMARY KEY (`id`),
                                         KEY `PENALTY_CLASS_DETAIL_ID` (`penalty_class_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE `escalations` (
                               `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                               `escalation_type` varchar(200) NOT NULL,
                               `escalation_mapping_id` varchar(64) NOT NULL,
                               `escalation_level` varchar(50) NOT NULL,
                               `tenant_name` varchar(256) NOT NULL,
                               `tenant_subcategory_name` varchar(256) NOT NULL,
                               `email_dl` varchar(1024) NOT NULL,
                               `created_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                               `updated_at` datetime(3) NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                               `partition_id` int(3) unsigned NOT NULL,
                               PRIMARY KEY (`id`,`partition_id`),
                               UNIQUE KEY `UK_ESCALATION_ID` (`partition_id`,`escalation_mapping_id`,`escalation_level`),
                               KEY `PENALTY_CLASS_DETAIL_IDX` (`tenant_name`,`escalation_mapping_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
 PARTITION BY HASH (`partition_id`)
PARTITIONS 120;


CREATE TABLE `penalties` (
                             `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                             `penalty_id` varchar(64) NOT NULL,
                             `penalty_class_id` varchar(64) NOT NULL,
                             `penalty_probable_id` varchar(64) NOT NULL,
                             `beneficiary_id` varchar(256) NOT NULL,
                             `beneficiary_type` varchar(256) NOT NULL,
                             `transaction_id` varchar(256) NOT NULL,
                             `transaction_amount` int NOT NULL,
                             `initial_penalty_amount` int NOT NULL,
                             `final_penalty_amount` int NOT NULL,
                             `status` varchar(256) NOT NULL,
                             `trigger_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                             `qualified_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                             `closed_at` datetime(3)  NULL ,
                             `created_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                             `updated_at` datetime(3) NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                             `partition_id` int(3) unsigned NOT NULL,
                             PRIMARY KEY (`id`,`partition_id`),
                             UNIQUE KEY `UK_PENALTY_ID` (`penalty_id`,`partition_id`),
                             KEY `PENALTY_CLASS_DETAIL_ID` (`penalty_class_id`,`status`),
                             KEY `penalty_transaction_id_idx` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
 PARTITION BY HASH (`partition_id`)
PARTITIONS 120;


CREATE TABLE `penalty_disbursements` (
                                         `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
                                         `penalty_class_id` varchar(64) NOT NULL,
                                         `penalty_id` varchar(64) NOT NULL,
                                         `beneficiary_id` varchar(256) NOT NULL,
                                         `transaction_id` varchar(256) NOT NULL,
                                         `disbursement_amount` int NOT NULL,
                                         `disbursement_id` varchar(256) NOT NULL,
                                         `disbursement_txn_id` varchar(256) NULL,
                                         `status` varchar(256) NOT NULL,
                                         `disbursement_mode` varchar(256) NOT NULL,
                                         `created_at` datetime(3) NOT NULL DEFAULT current_timestamp(),
                                         `updated_at` datetime(3) NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                                         `partition_id` int(3) unsigned NOT NULL,
                                         PRIMARY KEY (`id`,`partition_id`),
                                         KEY `penalty_idx` (`penalty_id`),
                                         KEY `penalty_class_idx` (`penalty_class_id`,`penalty_id`),
                                         KEY `penalty_transaction_id_idx` (`transaction_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8
 PARTITION BY HASH (`partition_id`)
PARTITIONS 120;





