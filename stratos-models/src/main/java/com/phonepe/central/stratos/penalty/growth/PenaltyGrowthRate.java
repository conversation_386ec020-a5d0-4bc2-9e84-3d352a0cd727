package com.phonepe.central.stratos.penalty.growth;

import com.phonepe.central.stratos.penalty.growth.calculation.Calculation;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyGrowthRate {

    @Valid
    @NotNull
    private Calculation calculation;
    @NotNull
    private GrowthUnit unit;
}
