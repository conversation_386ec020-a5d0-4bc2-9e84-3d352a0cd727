package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.EvidenceStatusDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvidenceFilterParams {
    @NotBlank
    private String merchantId;
    @NotBlank
    private String disputeId;
    private List<String> disputeWorkflowIds;
    private EvidenceStatusDto state;
}
