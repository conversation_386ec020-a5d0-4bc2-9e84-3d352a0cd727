package com.phonepe.central.stratos.penalty.response;

import com.phonepe.central.stratos.penalty.PenaltyStatus;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class PenaltyStatusTest {

    @Test
    public void testEnumValues() {
        assertEquals(PenaltyStatus.ACTIVE, PenaltyStatus.valueOf("ACTIVE"));
        assertEquals(PenaltyStatus.DISBURSED, PenaltyStatus.valueOf("DISBURSED"));
        assertEquals(PenaltyStatus.REJECTED, PenaltyStatus.valueOf("REJECTED"));
    }

    @Test
    public void testEnumSize() {
        PenaltyStatus[] statuses = PenaltyStatus.values();
        assertEquals(4, statuses.length);
    }

    @Test
    public void testEnumOrder() {
        PenaltyStatus[] statuses = PenaltyStatus.values();
        assertArrayEquals(new PenaltyStatus[]{PenaltyStatus.ACTIVE, PenaltyStatus.DISBURSED,PenaltyStatus.FAILED, PenaltyStatus.REJECTED}, statuses);
    }
}
