package com.phonepe.merchant.platform.stratos.server.core.events;

public enum EventType {
    EXCEPTION,
    CHAR<PERSON>BA<PERSON>K_ACCOUNTING_EVENT,
    DISPUTE_ACTION_SUCCESS_EVENT,
    DISPUTE_ACTION_FAILURE_EVENT,
    UDIR_OUTGOING_COMPLAINT_EVENT,
    UDIR_OUTGOING_CALLBACK_EVENT,
    FILE_UPLOAD_EVENT,
    FILE_PROCESS_STATE_EVENT,
    P2PM_TOA_PULSE_EVENT,
    TOA_RETRY_EVENT,
    P2PM_TOA_BREACH_EVENT,
    SIDELINE_SKIPPED_EVENT,
    PRIMUS_EOF_EVENT,
    KRATOS_RECOMMENDED_ACTION_EVENT,
    REFUND_ACTION_EVENT,
    TOA_STATUS_EVENT,
    TOA_BREACH_EVENT,
    PARTIAL_REJECTED_CHARGEBACK,
    CALL<PERSON><PERSON>K_EVENT,
    DISPUTE_SIGNAL_EVENT,
    NOTIFICATION_EVENT,
    PENALTY_CLASS_EVENT,
    PENALTY_PROBABLE_EVENT,
    PENALTY_INSTANCE_EVENT,

    PENALTY_INSTANCE_GROWTH_EVENT,
    PENALTY_DISBURSEMENT_EVENT,
    PENALTY_RECOVERY_SUCCESS_EVENT,
    PENALTY_RECOVERY_FAILURE_EVENT,
    ESCALATION_EVENT
}
