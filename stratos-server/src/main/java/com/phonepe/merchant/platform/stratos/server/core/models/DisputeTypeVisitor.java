package com.phonepe.merchant.platform.stratos.server.core.models;

public interface DisputeTypeVisitor<T> {

    T visitUpiChargeback();

    T visitPgChargeback();

    T visitUdirOutgoingComplaint();

    T visitUdirIncomingComplaint();

    T visitP2PMToa();

    T visitEdcChargeback();
    T visitNetBankingChargeback();
    T visitNotionalCreditToa();
    T visitBbpsTatBreachToa();
    T visitWalletChargeback();
    T visitFraFraud();
}
