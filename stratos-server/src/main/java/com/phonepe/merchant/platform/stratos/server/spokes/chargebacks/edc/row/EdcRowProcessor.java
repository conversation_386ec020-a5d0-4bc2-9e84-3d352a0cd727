package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.row;


import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.SourceType;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.row.RowTransactionTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.RowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.EdcRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.row.RowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

@Slf4j
public class EdcRowProcessor extends RowProcessor {

    private final DisputeService disputeService;

    private final EventIngester eventIngester;
    private final Map<DisputeType,Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig;

    @Inject
    public EdcRowProcessor(
            final RowRepository rowRepository,
            final DisputeService disputeService,
            final EventIngester eventIngester,
            final Map<DisputeType, Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig) {
        super(rowRepository);
        this.disputeService = disputeService;
        this.eventIngester=eventIngester;
        this.ignoreSignalStateConfig = ignoreSignalStateConfig;
    }

    public boolean process(final EdcRowMessage edcRowMessage) {
        log.info("edcRowMessage: {}", edcRowMessage);

        var rowId = edcRowMessage.getRowId();
        var paymentsTransactionId = edcRowMessage.getTransactionId();
        final var sourceId = edcRowMessage.getSourceId();
        final BigDecimal netAmount = edcRowMessage.getChargebackAcceptedAmount();
        var isProcessed = saveOrUpdateRow(sourceId, rowId, RowState.UNDER_PROCESS,
            RowContext.builder()
                .content(MapperUtils.serializeToString(edcRowMessage)).build(),
            RowType.EDC_MIS_ROW);
        if (isProcessed) {
            return true;
        }
        try {
            // get payment transactionId from EDC
            // get dispute
            var dispute = disputeService.getDispute(paymentsTransactionId, DisputeType.EDC_CHARGEBACK);
            // get DisputeWorkflow
            var disputeWorkflow = disputeService.getDisputeWorkflow(
                paymentsTransactionId,
                DisputeType.EDC_CHARGEBACK, dispute.getCurrentDisputeStage());
            var transactionType = edcRowMessage.getTransactionType();
            eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(disputeWorkflow,DisputeWorkflowEvent.RECEIVE_DEBIT, DisputeSignalEvent.SignalType.DEBIT));

            log.info(
                "Processing Signal for paymentsTransactionId:{}, dispute: {}, disputeWorkflow: {}",
                paymentsTransactionId, dispute, disputeWorkflow);
            transactionType.accept(new RowTransactionTypeVisitor<Void>() {
                @Override
                public Void visitChargeback() {
                    if(DisputeWorkflowUtils.ignoreSignal(ignoreSignalStateConfig, disputeWorkflow)) {
                        log.info("Ignored debit Signal for disputeWorkflowId : {}, current state : {}",
                            disputeWorkflow.getDisputeWorkflowId(), disputeWorkflow.getCurrentState());
                        return null;
                    }
                    DisputeWorkflowEvent disputeWorkflowEvent = Optional.ofNullable(
                        disputeWorkflow.getCurrentState().accept(
                            new DisputeWorkflowStateNonMandatoryVisitor<DisputeWorkflowEvent>() {
                                @Override
                                public DisputeWorkflowEvent visitAcceptanceCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_DEBIT;
                                }

                                @Override
                                public DisputeWorkflowEvent visitPartialRepresentmentCompleted() {
                                    return DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT;
                                }

                                @Override
                                public DisputeWorkflowEvent visitFraudRepresentmentCompleted(){
                                    return DisputeWorkflowEvent.RECEIVE_DEBIT;
                                }
                            })).orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                        Map.of(Constants.MESSAGE,
                            String.format("Invalid Debit transformation for %s from %s",
                                disputeWorkflow.getDisputeWorkflowId(),
                                disputeWorkflow.getCurrentState()))));

                    disputeService.triggerEvent(
                        Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                        paymentsTransactionId,
                        disputeWorkflow.getDisputeWorkflowId(),
                        disputeWorkflowEvent,
                        InstitutionalDebitTransitionContext.builder()
                            .debitAmount(netAmount.longValue())
                            .debitSourceType(SourceType.FILE)
                            .debitSourceId(sourceId)
                            .build()
                    );
                    saveOrUpdateRow(sourceId, rowId, RowState.PROCESSED, RowContext.builder()
                            .content(MapperUtils.serializeToString(edcRowMessage)).build(),
                        RowType.EDC_MIS_ROW);
                    return null;
                }

                // For Transaction Type Chargeback_Reversed we are marking it as Failed Directly because credit row is not expected
                @Override
                public Void visitChargebackReversed() {
                    saveOrUpdateRow(sourceId, edcRowMessage.getRowId(), RowState.FAILED,
                        RowContext.builder()
                            .content(MapperUtils.serializeToString(edcRowMessage))
                            .build(), RowType.EDC_MIS_ROW);
                    return null;
                }
            });
            // netAmount

        } catch (Exception e) {
            log.error("Error in processing Signal: {}", e);
            var error = StratosErrorCodeKey.INTERNAL_SERVER_ERROR;
            if (e instanceof DisputeException) { //NOSONAR
                log.error("Edc Mis Row Error Context {}",
                    MapperUtils.serializeToString(((DisputeException) e).getContext()));
                error = ((DisputeException) e).getErrorCode();
            }
            saveOrUpdateRow(sourceId, edcRowMessage.getRowId(), RowState.FAILED,
                RowContext.builder()
                    .content(MapperUtils.serializeToString(edcRowMessage))
                    .code(error.name())
                    .build(),
                RowType.EDC_MIS_ROW);
            throw e;
        }
        return true;
    }
}
