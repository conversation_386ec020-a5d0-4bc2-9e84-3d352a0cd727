package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@EqualsAndHashCode(callSuper = true)
public class CommunicationIdFilter extends DisputeFilter {

    @NotEmpty
    private List<String> communicationIds;

    public CommunicationIdFilter() {
        super(DisputeFilterType.COMMUNICATION_ID);
    }

    @Builder
    public CommunicationIdFilter(@Singular final List<String> communicationIds) {
        this();
        this.communicationIds = communicationIds;
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
