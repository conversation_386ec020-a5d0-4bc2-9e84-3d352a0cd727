package com.phonepe.merchant.platform.stratos.server.core.models;

import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@NoArgsConstructor
public class DisputeStateMapper implements DisputeWorkflowStateVisitorWithConxtext<DisputeStatus, DisputeWorkflow> {
    @Override
    public DisputeStatus visitReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRgcsAcceptanceRequired(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRgcsAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.ACCEPTED;
    }

    @Override
    public DisputeStatus visitRepresentmentRequired(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitNPCIRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCreditReceived(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitRefundBlocked(DisputeWorkflow dw) {
        return DisputeStatus.NEEDS_ACTION;
    }

    @Override
    public DisputeStatus visitFulfilmentDocumentsReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPartialFulfilmentDocumentsReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitNPCIPartialRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPartialCreditReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitMerchantNotRespondedWithinTTL(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitMerchantAcceptedChargeback(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitNPCIAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitAbsorbChargebackRequested(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitAbsorbChargebackRejected(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitAbsorbChargebackApproved(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitChargebackAbsorbed(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitChargebackAbsorbedReversed(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackRequested(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackRejected(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackApproved(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackEventRaised(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackEventAccepted(DisputeWorkflow dw) {
        long acceptedAmount = 0;
        long disputedAmount = dw.getDisputedAmount();
        if(dw instanceof FinancialDisputeWorkflow)
            acceptedAmount = ((FinancialDisputeWorkflow) dw).getAcceptedAmount();
        if(acceptedAmount != disputedAmount){
            return DisputeStatus.REJECTED;
        }
        return DisputeStatus.ACCEPTED;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredChargebackRequested(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredChargebackApproved(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredChargebackEventRaised(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredChargebackEventAccepted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitDebitReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPartialDebitReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPgRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitPgAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPgPartialRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitUdirComplaintRejected(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitFailure(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitUdirComplaintAccepted(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitUdirResponseReceived(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitUdirResponseNotReceived(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitInternalMerchantRepresentmentRequired(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitChargebackCancelled(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitToaBlockedDueToKs(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaInitiated(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaInitiationFailed(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaCompleted(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaFailed(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaPending(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaCompletedExternally(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitP2pmToaFailedAfterMaxAutoRetry(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitRepresentedCompleted(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.ACCEPTED;
    }

    @Override
    public DisputeStatus visitPartialRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitFraudRejected(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitSuspectedFraud(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCBRefundCreated(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCBRefundInitated(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCBRefundAccepted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCBRefundFailed(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitToaOpened(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaClosed(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaInitiated(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaInitiationFailed(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaCompleted(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaFailed(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaPending(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitToaFailedAfterMaxAutoRetry(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitFraudRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitHold(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitEnd(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitRecoverHoldEventRaised(DisputeWorkflow context) {
        return null;
    }

    @Override
    public DisputeStatus visitRecoverHoldEventAccepted(DisputeWorkflow context) {
        return null;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredHoldEventRaised(DisputeWorkflow context) {
        return null;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredHoldEventAccepted(DisputeWorkflow context) {
        return null;
    }
    @Override
    public DisputeStatus visitNpciAckChargeback(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitNpciRejectedChargeback(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPartialRejectedChargeback(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPartialAcceptedChargeback(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitFullyAcceptedChargeback(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitAcceptedChargeback(DisputeWorkflow context) {
        return DisputeStatus.ACCEPTED;
    }

    @Override
    public DisputeStatus visitRejectedChargeback(DisputeWorkflow context) {
        return DisputeStatus.REJECTED;
    }

    @Override
    public DisputeStatus visitCbRefundInitiationCompleted(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCbRefundProcessedExternally(DisputeWorkflow context) {
        return DisputeStatus.ACCEPTED;
    }

    @Override
    public DisputeStatus visitToaUnableToProcess(DisputeWorkflow dw) {
        return null;
    }

    @Override
    public DisputeStatus visitMerchantActionRequested(DisputeWorkflow context) {
        return DisputeStatus.NEEDS_ACTION;
    }
    @Override
    public DisputeStatus visitRecoverPartialHoldEventRaised(DisputeWorkflow context) {
        return null;
    }

    @Override
    public DisputeStatus visitRecoverPartialHoldEventAccepted(DisputeWorkflow context) {
        return null;
    }

    @Override
    public DisputeStatus visitInvalidEvidences(DisputeWorkflow context) {
        return null;
    }
}
