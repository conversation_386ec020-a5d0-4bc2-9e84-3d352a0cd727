package com.phonepe.merchant.platform.stratos.server.v2.statemachines.fraud;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.StateChangeListener;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.TerminalStateListener;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions.FullRefundCreationAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ChargebackRecoveryEligibilityAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.HoldReversalAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.fraud.FraudCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.CancelWorkflowConfigurator;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.MerchantCommunicationConfigurator;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.ProactiveHoldWorkflowConfigurator;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.RefundWorkflowConfigurator;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

import java.util.EnumSet;

@Singleton
public class SuspectedFraudStateMachine extends DisputeStateMachine {
    private final MerchantCommunicationConfigurator merchantCommunicationConfigurator;
    private final RefundWorkflowConfigurator refundWorkflowConfigurator;
    private final FraudCreateEntryAction createEntryAction;
    private final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator;
    private final CancelWorkflowConfigurator cancelWorkflowConfigurator;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final FullRefundCreationAction fullRefundCreationAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final UnblockRefundAction unblockRefundAction;
    private final HoldReversalAction holdReversalAction;

    private final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction;
    private final ChargebackRecoveryEligibilityAction chargebackRecoveryEligibilityAction;
    private final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener;
    private final StateChangeListener<DisputeWorkflowState, DisputeWorkflowEvent> stateChangeListener;

    @Inject
    public SuspectedFraudStateMachine(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final MerchantCommunicationConfigurator merchantCommunicationConfigurator,
            final RefundWorkflowConfigurator refundWorkflowConfigurator,
            final FraudCreateEntryAction fraudCreateEntryAction,
            final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator,
            final CancelWorkflowConfigurator cancelWorkflowConfigurator,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final FullRefundCreationAction fullRefundCreationAction,
            final AcceptDisputeAction acceptDisputeAction,
            final UnblockRefundAction unblockRefundAction,
            final HoldReversalAction holdReversalAction,
            final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction,
            final ChargebackRecoveryEligibilityAction chargebackRecoveryEligibilityAction,
            final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener,
            final StateChangeListener<DisputeWorkflowState, DisputeWorkflowEvent> stateChangeListener) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.merchantCommunicationConfigurator = merchantCommunicationConfigurator;
        this.refundWorkflowConfigurator = refundWorkflowConfigurator;
        this.createEntryAction = fraudCreateEntryAction;
        this.proactiveHoldWorkflowConfigurator = proactiveHoldWorkflowConfigurator;
        this.cancelWorkflowConfigurator = cancelWorkflowConfigurator;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.fullRefundCreationAction = fullRefundCreationAction;
        this.acceptDisputeAction = acceptDisputeAction;
        this.unblockRefundAction = unblockRefundAction;
        this.holdReversalAction = holdReversalAction;
        this.raiseHoldRecoveryReversalAccountingEventAction = raiseHoldRecoveryReversalAccountingEventAction;
        this.chargebackRecoveryEligibilityAction = chargebackRecoveryEligibilityAction;
        this.terminalStateListener = terminalStateListener;
        this.stateChangeListener = stateChangeListener;
    }

    @Override
    @SneakyThrows
    protected void configure(StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }
    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.END);
    }

    @Override
    @SneakyThrows
    protected void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        merchantCommunicationConfigurator.configure(transitions);
        refundWorkflowConfigurator.configure(transitions);
        proactiveHoldWorkflowConfigurator.configure(transitions);
        cancelWorkflowConfigurator.configure(transitions);

        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.CREATE_ENTRY)
                .action(createEntryAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE)
                .action(acceptDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
                .target(DisputeWorkflowState.HOLD)
                .event(DisputeWorkflowEvent.HOLD)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.HOLD)
                .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.HOLD)
                .target(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_REPRESENTMENT)
                .action(unblockRefundAction) //need to evaluate
                .and()


                .withExternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.CB_REFUND_CREATED)
                .event(DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND)
                .action(fullRefundCreationAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD)
                .action(holdReversalAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT)
                .action(raiseHoldRecoveryReversalAccountingEventAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
                .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED)
                .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_HOLD_EVENT)
                .action(chargebackRecoveryEligibilityAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_ACCEPTED)
                .target(DisputeWorkflowState.END)
                .event(DisputeWorkflowEvent.END_WORKFLOW)
                .action(updateDisputeStateAction)
                .and()
        ;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
            .disputeType(DisputeType.FRA_FRAUD)
            .disputeStage(DisputeStage.PRE_CHARGEBACK)
            .disputeWorkflowVersion(DisputeWorkflowVersion.V2)
            .build();
    }

    @Override
    protected void addStateListeners(StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> stateMachine) {
        stateMachine.addStateListener(eventNotAcceptedListener);
        stateMachine.addStateListener(terminalStateListener);
        stateMachine.addStateListener(stateChangeListener);
    }
}
