package com.phonepe.merchant.platform.stratos.server;

import static com.phonepe.merchant.platform.stratos.server.StratosApplication.APP_NAME;

import com.aerospike.client.policy.BatchPolicy;
import com.aerospike.client.policy.Policy;
import com.aerospike.client.policy.WritePolicy;
import com.google.common.base.Strings;
import com.google.inject.Injector;
import com.google.inject.Stage;
import com.phonepe.data.provider.rosey.bundle.RoseyConfigProviderBundle;
import com.phonepe.dataplatform.EventIngestorClient;
import com.phonepe.gandalf.client.GandalfBundle;
import com.phonepe.gandalf.models.client.GandalfClientConfig;
import com.phonepe.growth.neuron.NeuronBundle;
import com.phonepe.growth.neuron.model.NeuronPulseHandler;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeEntityFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.server.core.guice.AerospikeModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.BindingModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.ConfigModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.CoreModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.DBModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.OlympusIMModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.RabbitmqModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.ServiceDiscoveryModule;
import com.phonepe.merchant.platform.stratos.server.core.guice.TstoreClientModule;
import com.phonepe.merchant.platform.stratos.server.core.neuron.NotionalCreditPulseHandler;
import com.phonepe.merchant.platform.stratos.server.core.neuron.P2pmToaPulseHandler;
import com.phonepe.merchants.platform.notificationbundle.NotificationModule;
import com.phonepe.merchants.platform.notificationbundle.config.NotificationClientConfig;
import com.phonepe.metrics.MetricIngestionBundle;
import com.phonepe.metrics.config.ReporterConfig;
import com.phonepe.olympus.im.client.OlympusIMBundle;
import com.phonepe.olympus.im.client.config.OlympusIMClientConfig;
import com.phonepe.platform.aerospike.bundle.AerospikeBundle;
import com.phonepe.platform.aerospike.config.AerospikeConfiguration;
import com.phonepe.platform.executors.ExecutorUtils;
import com.phonepe.platform.http.v2.common.HttpConfiguration;
import com.phonepe.platform.http.v2.common.hub.RangerHubConfiguration;
import com.phonepe.platform.http.v2.discovery.HttpDiscoveryBundle;
import com.phonepe.platform.http.v2.discovery.ServiceEndpointProviderFactory;
import com.phonepe.platform.requestinfo.bundle.RequestInfoBundle;
import com.phonepe.shadow.ShadowModule;
import com.phonepe.tstore.client.bundle.TstoreClientBundle;
import com.phonepe.verified.kaizen.guice.OncallOpsModule;
import com.phonepe.verified.kaizen.guice.StateMachineModule;
import com.platform.validation.ValidationBundle;
import com.platform.validation.ValidationConfig;
import in.vectorpro.dropwizard.swagger.SwaggerBundle;
import in.vectorpro.dropwizard.swagger.SwaggerBundleConfiguration;
import io.appform.dropwizard.actors.ExecutorServiceProvider;
import io.appform.dropwizard.actors.RabbitmqActorBundle;
import io.appform.dropwizard.actors.TtlConfig;
import io.appform.dropwizard.actors.config.RMQConfig;
import io.appform.dropwizard.sharding.DBShardingBundle;
import io.appform.dropwizard.sharding.config.ShardedHibernateFactory;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryBundle;
import io.appform.ranger.discovery.bundle.ServiceDiscoveryConfiguration;
import io.dropwizard.Application;
import io.dropwizard.Configuration;
import io.dropwizard.oor.OorBundle;
import io.dropwizard.primer.model.PrimerBundleConfiguration;
import io.dropwizard.setup.Bootstrap;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;
import org.apache.curator.framework.CuratorFramework;
import org.zapodot.hystrix.bundle.HystrixBundle;
import ru.vyarus.dropwizard.guice.GuiceBundle;

abstract class BaseApplication<T extends Configuration> extends Application<T> {

    protected GuiceBundle guiceBundle;

    HystrixBundle hystrixBundle() {
        return HystrixBundle.builder()
            .disableStreamServletInAdminContext()
            .withApplicationStreamPath("/hystrix.stream")
            .build();
    }

    SwaggerBundle<StratosConfiguration> swaggerBundle() {
        return new SwaggerBundle<>() {
            @Override
            protected SwaggerBundleConfiguration getSwaggerBundleConfiguration(
                final StratosConfiguration configuration) {
                return configuration.getSwagger();
            }
        };
    }

    MetricIngestionBundle<StratosConfiguration> metricIngestionBundle(
            final OlympusIMBundle<StratosConfiguration> olympusIMBundle,
            final HttpDiscoveryBundle<StratosConfiguration> httpDiscoveryBundle) {

        return new MetricIngestionBundle<>() {
            @Override
            public ReporterConfig reporterConfig(final StratosConfiguration config) {
                return config.getReporterConfig();
            }

            @Override
            public Supplier<String> authTokenSupplier(StratosConfiguration configuration) {
                return () -> olympusIMBundle.getOlympusIMClient().getSystemAuthHeader();
            }

            @Override
            public boolean registerMicrometer() {
                return false;
            }

            @Override
            public ServiceEndpointProviderFactory getServiceEndpointProviderFactory(StratosConfiguration stratosConfiguration) {
                return httpDiscoveryBundle.getEndpointProviderFactory();
            }
        };
    }

    ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle(final String serviceName) {

        return new ServiceDiscoveryBundle<>() {
            @Override
            protected ServiceDiscoveryConfiguration getRangerConfiguration(
                final StratosConfiguration appConfig) {
                return appConfig.getDiscovery();
            }

            @Override
            protected String getServiceName(final StratosConfiguration config) {
                return serviceName;
            }
        };
    }

    OorBundle<StratosConfiguration> oorBundle() {
        return new OorBundle<>() {
            @Override
            public boolean withOor() {
                return false;
            }
        };
    }

    @SuppressWarnings("java:S107")
    GuiceBundle guiceBundle(
        final RabbitmqActorBundle<StratosConfiguration> rabbitmqActorBundle,
        final ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle,
        final RoseyConfigProviderBundle<StratosConfiguration> roseyConfigProviderBundle,
        final DBShardingBundle<StratosConfiguration> dbShardingBundle,
        final AerospikeBundle<StratosConfiguration> aerospikeBundle,
        final TstoreClientBundle<StratosConfiguration> tstoreClientBundle,
        final Bootstrap<StratosConfiguration> bootstrap,
        final OlympusIMBundle<StratosConfiguration> olympusIMBundle,
        final HttpDiscoveryBundle<StratosConfiguration> httpDiscoveryBundle) {

        final var packageNameList = Arrays.asList(
                getClass().getPackage().getName(),
                "com.phonepe.central.stratos.penalty",
                "com.phonepe.stratos.kaizen",
                "com.phonepe.verified",
                "com.phonepe.shadow"
        );

        return GuiceBundle.<StratosConfiguration>builder()
            .enableAutoConfig(packageNameList.toArray(new String[0]))
            .modules(
                new CoreModule(roseyConfigProviderBundle,
                    bootstrap.getObjectMapper(), bootstrap.getMetricRegistry(), httpDiscoveryBundle),
                new ServiceDiscoveryModule(serviceDiscoveryBundle,httpDiscoveryBundle),
                new TstoreClientModule(tstoreClientBundle),
                new RabbitmqModule(rabbitmqActorBundle),
                new AerospikeModule(aerospikeBundle),
                new DBModule(dbShardingBundle),
                new OlympusIMModule(olympusIMBundle),
                new BindingModule(),
                new ConfigModule(),

//              Kaizen modules
                new com.phonepe.verified.kaizen.guice.BindingModule(),
                new OncallOpsModule(),
                new StateMachineModule(),
                new ShadowModule<>(dbShardingBundle) {},
                getNotificationBundle()
                )
            .build(Stage.PRODUCTION);
    }

    RoseyConfigProviderBundle<StratosConfiguration> appConfigProviderBundle() {

        return new RoseyConfigProviderBundle<>() {
            @Override
            public String getRoseyConfigPath(final StratosConfiguration userServiceConfiguration) {
                return "/rosey/config.yml";
            }

            @Override
            public String getRoseyTeamId(final StratosConfiguration userServiceConfiguration) {
                return StratosApplication.ROSEY_TEAM_NAME;
            }

            @Override
            public String getRoseyConfigName(final StratosConfiguration userServiceConfiguration) {
                return APP_NAME;
            }
        };
    }

    DBShardingBundle<StratosConfiguration> dbShardingBundle() {
        return new DBShardingBundle<>(
            APP_NAME,
            List.of("com.phonepe.merchant.platform.stratos.server.core.mariadb.entities",
                    "com.phonepe.central.stratos.penalty.server.mariadb.entities",
                    "com.phonepe.verified.kaizen.storage.mariadb.entities",
                    "com.phonepe.stratos.kaizen.storage.mariadb.entities",
                    "com.phonepe.shadow.storage")) {

            @Override
            protected ShardedHibernateFactory getConfig(final StratosConfiguration config) {
                return config.getDatabase();
            }
        };
    }

    ValidationBundle<StratosConfiguration> validationBundle() {
        return new ValidationBundle<>() {
            @Override
            public ValidationConfig getValidationConfig(final StratosConfiguration appConfig) {
                return appConfig.getValidationConfig();
            }
        };
    }

    RabbitmqActorBundle<StratosConfiguration> rabbitmqActorBundle(
        final Bootstrap<StratosConfiguration> bootstrap) {

        return new RabbitmqActorBundle<>() {
            @Override
            protected TtlConfig ttlConfig() {
                return TtlConfig.builder().build();
            }

            @Override
            protected RMQConfig getConfig(final StratosConfiguration config) {
                return config.getRmqConfig();
            }

            @Override
            protected ExecutorServiceProvider getExecutorServiceProvider(
                final StratosConfiguration configuration) {

                return (name, nThreads) ->
                    ExecutorUtils.newFixedThreadPool(name, nThreads, bootstrap.getMetricRegistry());
            }
        };
    }

    OlympusIMBundle<StratosConfiguration> olympusIMBundle(
            final ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle) {
        return new OlympusIMBundle<StratosConfiguration>() {
            @Override
            protected CuratorFramework getCuratorFramework() {
                return serviceDiscoveryBundle.getCurator();
            }

            @Override
            protected OlympusIMClientConfig getOlympusIMClientConfig(StratosConfiguration stratosConfiguration) {
                return stratosConfiguration.getOlympusIMClientConfig();
            }

            @Override
            protected Supplier<Injector> getGuiceInjector() {
                //setting null here due to circular dependency.
                return guiceBundle::getInjector;
            }

            @Override
            protected Supplier<EventIngestorClient> getEventIngestorClient() {
                return ()-> getGuiceInjector().get().getInstance(EventIngestorClient.class);
            }
        };
    }

    AerospikeBundle<StratosConfiguration> aerospikeBundle(
        final Bootstrap<StratosConfiguration> bootstrap) {

        return new AerospikeBundle<>() {
            @Override
            protected AerospikeConfiguration configuration(final StratosConfiguration config) {
                return config.getAerospikeConfig().getAerospikeBundleConfig();
            }

            @Override
            protected Policy readPolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getReadPolicy();
            }

            @Override
            protected WritePolicy writePolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getWritePolicy();
            }

            @Override
            protected BatchPolicy batchPolicy(final StratosConfiguration config) {
                return config.getAerospikeConfig().getBatchPolicy();
            }

            @Override
            public ExecutorService threadPool(final StratosConfiguration config) {
                return ExecutorUtils.newFixedThreadPool(
                    "aerospike",
                    config.getAerospikeConfig().getAerospikeBundleConfig().getThreadPoolSize(),
                    bootstrap.getMetricRegistry());
            }
        };
    }

    TstoreClientBundle<StratosConfiguration> tstoreClientBundle(
        final OlympusIMBundle<StratosConfiguration> olympusIMBundle,
        final HttpDiscoveryBundle<StratosConfiguration> httpDiscoveryBundle) {

        return new TstoreClientBundle<>(DisputeFeed.class, DisputeEntityFeed.class) {
            @Override
            public HttpConfiguration tStoreHttpClientConfig(
                final StratosConfiguration stratosConfiguration) {
                return stratosConfiguration.getTstoreClientConfig().getTstoreConfiguration();
            }

            @Override
            public HttpConfiguration tstoreBcpHttpClientConfig(
                final StratosConfiguration stratosConfiguration) {
                return stratosConfiguration.getTstoreClientConfig().getTstoreBcpConfiguration();
            }

            @Override
            public HttpConfiguration schemaHttpClientConfig(
                final StratosConfiguration stratosConfiguration) {
                return stratosConfiguration.getTstoreClientConfig().getSchemaConfiguration();
            }

            @Override
            public ServiceEndpointProviderFactory serviceEndpointProviderFactory(StratosConfiguration stratosConfiguration) {
                return httpDiscoveryBundle.getEndpointProviderFactory();
            }

            @Override
            public Supplier<String> authTokenSupplier(StratosConfiguration stratosConfiguration) {
                return () -> olympusIMBundle.getOlympusIMClient().getSystemAuthHeader();
            }
        };
    }

    NeuronBundle<StratosConfiguration> getNeuronBundle(Injector injector){
        return new NeuronBundle<StratosConfiguration>() {
            @Override
            public List<NeuronPulseHandler> getRegisteredPulseHandlers() {
                return List.of(injector.getInstance(P2pmToaPulseHandler.class),
                    injector.getInstance(NotionalCreditPulseHandler.class));
            }
        };
    }

    NotificationModule<StratosConfiguration> getNotificationBundle() {
        return new NotificationModule<StratosConfiguration>() {
                @Override
                public NotificationClientConfig provideNotificationClientConfig(
                    StratosConfiguration stratosConfiguration) {
                    return stratosConfiguration.getNotificationClientConfig();
                }
            };
    }

    ServiceDiscoveryBundle<StratosConfiguration> getServiceDiscoveryBundle() {
        return new ServiceDiscoveryBundle<>() {
            @Override
            protected ServiceDiscoveryConfiguration getRangerConfiguration(final StratosConfiguration appConfig) {
                return appConfig.getDiscovery();
            }

            @Override
            protected String getServiceName(final StratosConfiguration appConfig) {
                var serviceNameFromEnv = System.getenv("SERVICE_NAME");
                return Strings.isNullOrEmpty(serviceNameFromEnv) ? APP_NAME : serviceNameFromEnv;
            }

            @Override
            protected int getPort(final StratosConfiguration appConfig) {
                return appConfig.getDiscovery().getPublishedPort();
            }

        };
    }

    HttpDiscoveryBundle<StratosConfiguration> getHttpDiscoveryBundle(
            final ServiceDiscoveryBundle<StratosConfiguration> serviceDiscoveryBundle) {
        return new HttpDiscoveryBundle<>() {
            @Override
            protected CuratorFramework getCuratorFramework(final StratosConfiguration configuration) {
                return serviceDiscoveryBundle.getCurator();
            }

            @Override
            protected RangerHubConfiguration getHubConfiguration(final StratosConfiguration configuration) {
                return configuration.getRangerHubConfiguration();
            }
        };
    }

    GandalfBundle<StratosConfiguration> gandalfBundle(final HttpDiscoveryBundle<StratosConfiguration> httpDiscoveryBundle) {
        return new GandalfBundle<>() {
            @Override
            protected ServiceEndpointProviderFactory getEndpointProviderFactory() {
                return httpDiscoveryBundle.getEndpointProviderFactory();            }

            @Override
            protected GandalfClientConfig getGandalfClientConfig(final StratosConfiguration config) {
                return config.getGandalfClientConfig();
            }

            @Override
            protected PrimerBundleConfiguration getGandalfPrimerConfig(final StratosConfiguration config) {
                return config.getPrimerBundleConfig();
            }
        };
    }

    protected RequestInfoBundle<StratosConfiguration> getRequestInfoBundle() {
        return new RequestInfoBundle<>(){
        };
    }

}
