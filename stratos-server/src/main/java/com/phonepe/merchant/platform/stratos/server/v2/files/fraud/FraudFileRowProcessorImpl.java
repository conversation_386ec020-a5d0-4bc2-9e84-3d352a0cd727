package com.phonepe.merchant.platform.stratos.server.v2.files.fraud;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PaymentsTxnlClient;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.SettlementOnHoldMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.SOHMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.models.payments.pay.TransactionDetail;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class FraudFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {
    private final PaymentsTxnlClient paymentsTxnlClient;
    private final IdHelper idHelper;
    private final DisputeMetadataHelper disputeMetadataHelper;
    private final SOHMetadataRepository sohMetadataRepository;


    @Inject
    public FraudFileRowProcessorImpl(
            HandleBarsService handleBarsService,
            DisputeService disputeService,
            MerchantMandateService merchantMandateService,
            PaymentsTxnlClient paymentsTxnlClient,
            IdHelper idHelper,
            DisputeMetadataHelper disputeMetadataHelper,
            SOHMetadataRepository sohMetadataRepository) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.paymentsTxnlClient = paymentsTxnlClient;
        this.idHelper = idHelper;
        this.disputeMetadataHelper = disputeMetadataHelper;
        this.sohMetadataRepository = sohMetadataRepository;
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(
            TransactionDetail transactionDetails,
            FinancialDisputeWorkflow disputeWorkflow,
            String fileId,
            DisputeType disputeType,
            FileConfig fileConfig,
            String disputeId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final var ttlInDays = Optional.ofNullable(disputeWorkflow.getDisputeStage())
                .map(disputeStage -> fileConfig.getDisputeStageTTLDaysMap()
                        .get(disputeStage))
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                        Map.of(Constants.MESSAGE,
                                "Invalid TTL config for disputeStage: " + disputeWorkflow.getDisputeStage())));

        return FinancialDisputeWorkflow.builder()
                .key(StorageUtils.primaryKey())
                .disputeType(disputeType)
                .disputeSourceId(fileId)
                .disputeSourceType(
                        com.phonepe.merchant.platform.stratos.server.core.models.SourceType.FILE)
                .disputeWorkflowId(idHelper.disputeWorkflowId(receivedPayment.getTransactionId()))
                .disputeWorkflowVersion(DisputeWorkflowVersion.V2)
                .transactionReferenceId(receivedPayment.getTransactionId())
                .respondBy(LocalDateTime.now().plusDays(ttlInDays))
                .userType(UserType.SYSTEM)
                .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
                .disputeStage(disputeWorkflow.getDisputeStage())
                .currentState(disputeWorkflow.getCurrentState())
                .currentEvent(disputeWorkflow.getCurrentEvent())
                .raisedAt(LocalDateTime.now())
                .disputedAmount(disputeWorkflow.getDisputedAmount())
                .penaltyAmount(disputeWorkflow.getPenaltyAmount())
                .disputeId(disputeId)
                .build();
    }

    @Override
    public TransactionDetail getTransactionDetails(
            String transactionId,
            FileRowMeta fileRowMeta) {
        final var transactionDetail = paymentsTxnlClient.getTransactionDetails(transactionId);

        if (!transactionDetail.isSuccess()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAYMENTS_ID,
                    Map.of(Constants.MESSAGE, "Invalid payments Id " + transactionId,
                            Constants.SERVICE_NAME, "PaymentsService"));
        }

        log.info("Transaction detail from payment is {}", transactionDetail.getData());
        return transactionDetail.getData();
    }

    @SneakyThrows
    @Override
    public Dispute getDispute(
            DisputeWorkflow disputeWorkflow,
            TransactionDetail transactionDetails,
            Dispute disputeFilePojo,
            DisputeType disputeType,
            String instrumentTransactionId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();
        final var sentPayment = transactionDetails.getSentPayment();

        final String merchantId = getMerchantIdByTransactionDetails(transactionDetails);

        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeId(idHelper.disputeId(receivedPayment.getTransactionId()))
            .transactionReferenceId(receivedPayment.getTransactionId())
            .disputeReferenceId(StringUtils.join(disputeFilePojo.getRrn(),
                disputeFilePojo.getInstrumentTransactionId()))
            .merchantId(merchantId)
            .merchantTransactionId(receivedPayment.getContext()
                .visit(new PaymentContextMerchantTransactionIdVisitor()))
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .instrumentTransactionId(sentPayment.getContext().visit(new PaymentContextMerchantTransactionIdVisitor()))
            .transactionAmount(transactionDetails.getReceivedPayment().getAmount())
            .rrn(disputeFilePojo.getRrn())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .disputeCategory(disputeFilePojo.getDisputeCategory())
            .build();
    }
    @Override
    public void processMetadata(
            FileRowMeta fileRowMeta,
            Dispute dispute,
            DisputeWorkflow disputeWorkflow){
        if(!(fileRowMeta instanceof FraudMeta)){
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                    Map.of(Constants.MESSAGE, "Invalid file meta type"));
        }
        SettlementOnHoldMetadata metadata = disputeMetadataHelper
                .toSohMetadata(disputeWorkflow,(FraudMeta) fileRowMeta);
        sohMetadataRepository.save(metadata);
    }
}
