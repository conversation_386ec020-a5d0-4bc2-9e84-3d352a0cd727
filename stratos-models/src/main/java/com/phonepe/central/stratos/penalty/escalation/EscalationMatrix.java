package com.phonepe.central.stratos.penalty.escalation;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.dropwizard.validation.ValidationMethod;
import java.util.Objects;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EscalationMatrix {

    @NotNull
    private EscalationType escalationType;

    @Valid
    @NotEmpty
    private Set<EscalationLevelConfig> levelConfig;

    @JsonIgnore
    @ValidationMethod(message = "Level configs are not mentioned for all levels")
    public boolean isValid() {
        return Objects.nonNull(levelConfig) && levelConfig.size() == EscalationLevel.values().length;
    }

}
