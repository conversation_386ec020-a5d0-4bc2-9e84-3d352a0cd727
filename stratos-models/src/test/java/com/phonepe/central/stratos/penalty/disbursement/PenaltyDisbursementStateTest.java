package com.phonepe.central.stratos.penalty.disbursement;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class PenaltyDisbursementStateTest {

    @Test
    public void testEnumValues() {
        PenaltyDisbursementState[] expectedValues = PenaltyDisbursementState.values();
        assertEquals(5, expectedValues.length);
        assertEquals(PenaltyDisbursementState.CREATED, expectedValues[0]);
        assertEquals(PenaltyDisbursementState.PROCESSING, expectedValues[1]);
        assertEquals(PenaltyDisbursementState.COMPLETED, expectedValues[2]);
        assertEquals(PenaltyDisbursementState.FAILED, expectedValues[3]);
        assertEquals(PenaltyDisbursementState.REJECTED, expectedValues[4]);
    }

    @Test
    public void testValueOf() {
        assertEquals(PenaltyDisbursementState.CREATED, PenaltyDisbursementState.valueOf("CREATED"));
        assertEquals(PenaltyDisbursementState.PROCESSING, PenaltyDisbursementState.valueOf("PROCESSING"));
        assertEquals(PenaltyDisbursementState.COMPLETED, PenaltyDisbursementState.valueOf("COMPLETED"));
        assertEquals(PenaltyDisbursementState.FAILED, PenaltyDisbursementState.valueOf("FAILED"));
        assertEquals(PenaltyDisbursementState.REJECTED, PenaltyDisbursementState.valueOf("REJECTED"));
    }
}
