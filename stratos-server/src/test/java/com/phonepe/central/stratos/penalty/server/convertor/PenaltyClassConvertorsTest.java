package com.phonepe.central.stratos.penalty.server.convertor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.central.stratos.penalty.growth.PenaltyGrowthRate;
import com.phonepe.central.stratos.penalty.meta.PenaltyClass;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassDetail;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import com.phonepe.central.stratos.penalty.meta.PenaltyCriteria;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.request.penaltyclass.PenaltyClassCreateRequest;
import com.phonepe.central.stratos.penalty.server.PenaltyTestObjectUtil;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassDetailEntity;
import com.phonepe.central.stratos.penalty.server.mariadb.entities.PenaltyClassEntity;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import io.appform.ranger.discovery.bundle.id.Id;
import io.appform.ranger.discovery.bundle.id.IdGenerator;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 */
public class PenaltyClassConvertorsTest {

    @Test
    public void testConvertPenaltyClassDetailToEntityWithValidInput() {
        PenaltyClassDetail source = PenaltyClassDetail.builder()
                .criteria(PenaltyCriteria.builder()
                        .build())
                .growthRate(PenaltyGrowthRate.builder()
                        .build())
                .label("label")
                .penaltyCap(100L)
                .build();
        MapperUtils.init(new ObjectMapper());

        PenaltyClassDetailEntity result = PenaltyClassConvertors.convert(source);

        assertNotNull(result.getCriteriaConfigData());
        assertNotNull(result.getGrowthConfigData());
        assertEquals("label", result.getLabel());
        assertEquals(100L, result.getPenaltyCap());
    }

    @Test
    public void testConvertPenaltyClassDetailToEntityWithNullInput() {
        PenaltyClassDetail source = null;

        PenaltyClassDetailEntity result = PenaltyClassConvertors.convert(source);

        assertNull(result);
    }

    @Test
    public void testConvertPenaltyClassCreateRequestToEntityWithValidInput() {
        PenaltyClassCreateRequest source = PenaltyClassCreateRequest.builder()
                .name("name")
                .description("description")
                .tenant(TenantInfo.builder()
                        .name("tenantName")
                        .subCategory("subCategory")
                        .build())
                .build();

        try (MockedStatic<IdGenerator> mockedIdGenerator = Mockito.mockStatic(IdGenerator.class)) {
            mockedIdGenerator.when(() -> IdGenerator.generate(PenaltyClassConvertors.PENALTY_CLASS_ID_PREFIX))
                    .thenReturn(Id.builder()
                            .id("generatedId")
                            .build());

            PenaltyClassEntity result = PenaltyClassConvertors.convert(source);

            assertEquals("generatedId", result.getPenaltyClassId());
            assertEquals("name", result.getName());
            assertEquals("description", result.getDescription());
            assertEquals("tenantName", result.getTenant_name());
            assertEquals("subCategory", result.getTenant_subcategory_name());
            assertEquals(PenaltyClassState.CREATED, result.getState());
        }
    }

    @Test
    public void testConvertPenaltyClassCreateRequestToEntityWithNullInput() {
        PenaltyClassCreateRequest source = null;

        PenaltyClassEntity result = PenaltyClassConvertors.convert(source);

        assertNull(result);
    }

    @Test
    public void testConvertPenaltyClassDetailEntityToDetailWithValidInput() {
        PenaltyClassDetailEntity source = PenaltyClassDetailEntity.builder()
                .label("label")
                .penaltyCap(100L)
                .build();

        source.setCriteriaConfigData(PenaltyCriteria.builder()
                .build());
        source.setGrowthConfigData(PenaltyGrowthRate.builder()
                .build());

        MapperUtils.init(new ObjectMapper());
        PenaltyClassDetail result = PenaltyClassConvertors.convert(source);

        assertNotNull(result.getCriteria());
        assertNotNull(result.getGrowthRate());
        assertEquals("label", result.getLabel());
        assertEquals(100L, result.getPenaltyCap());
    }

    @Test
    public void testConvertPenaltyClassDetailEntityToDetailWithNullInput() {
        PenaltyClassDetailEntity source = null;

        PenaltyClassDetail result = PenaltyClassConvertors.convert(source);

        assertNull(result);
    }

    @Test
    public void testConvertPenaltyClassEntityToClassWithValidInput() {
        PenaltyClassEntity source = PenaltyTestObjectUtil.getPenaltyClassEntity();

        PenaltyClass result = PenaltyClassConvertors.convert(source);

        assertEquals("classId", result.getId());
        assertEquals("name", result.getName());
        assertEquals(1, result.getVersion());
        assertEquals("description", result.getDescription());
        assertEquals("name", result.getTenant()
                .getName());
        assertEquals("subCategory", result.getTenant()
                .getSubCategory());
        assertEquals(PenaltyClassState.CREATED, result.getState());
    }

    @Test
    public void testConvertPenaltyClassEntityToClassWithNullInput() {
        PenaltyClassEntity source = null;

        PenaltyClass result = PenaltyClassConvertors.convert(source);

        assertNull(result);
    }

    @Test
    public void testClonePenaltyClassEntityWithValidInputNoVersionShouldBeUpgraded() {
        PenaltyClassEntity source = PenaltyTestObjectUtil.getPenaltyClassEntity();

        try (MockedStatic<IdGenerator> mockedIdGenerator = Mockito.mockStatic(IdGenerator.class)) {
            mockedIdGenerator.when(() -> IdGenerator.generate(PenaltyClassConvertors.PENALTY_CLASS_ID_PREFIX))
                    .thenReturn(Id.builder()
                            .id("newGeneratedId")
                            .build());

            PenaltyClassEntity result = PenaltyClassConvertors.clone(source);

            assertEquals("classId", result.getPenaltyClassId());
            assertEquals("name", result.getName());
            assertEquals(1, result.getVersion());
            assertEquals("description", result.getDescription());
            assertEquals("name", result.getTenant_name());
            assertEquals("subCategory", result.getTenant_subcategory_name());
            assertEquals(PenaltyClassState.CREATED, result.getState());
        }
    }

    @Test
    public void testClonePenaltyClassEntityWithNullInput() {
        PenaltyClassEntity source = null;

        PenaltyClassEntity result = PenaltyClassConvertors.clone(source);

        assertNull(result);
    }
}
