package com.phonepe.central.stratos.penalty.growth.calculation;

import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public abstract class FixedAmountCalculation extends Calculation {

    @Min(0)
    @NotNull
    private BigDecimal amount;

    public FixedAmountCalculation(CalculationType type, String amountPath, boolean isDefault, BigDecimal amount) {
        super(type, amountPath, isDefault);
        this.amount = amount;
    }
}
