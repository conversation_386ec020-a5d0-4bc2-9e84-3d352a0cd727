package com.phonepe.central.stratos.notification;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyDueShortlyEmailContext{

    @NotBlank
    private String tenantName;
    @NotBlank
    private String probableCount;
    @NotBlank
    private String penaltyClassName;
    @NotBlank
    private String penaltyClassId;
    @NotBlank
    private String toDate;
    @NotBlank
    private String fromDate;
}
