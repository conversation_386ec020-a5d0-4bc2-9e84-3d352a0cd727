package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.raiseevents;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.phonepe.data.provider.core.DataProvider;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import javax.inject.Singleton;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class RaiseHoldRecoveryAccountingEventActor extends
        Actor<ActionType, DisputeWorkflowMessage> {

    private final DisputeService disputeService;

    private final MerchantService merchantService;

    private final DataProvider<StratosConfiguration> configDataProvider;

    @Inject
    @SuppressWarnings("java:S107")
    public RaiseHoldRecoveryAccountingEventActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory,
            final DisputeService disputeService,
            final MerchantService merchantService,
            final DataProvider<StratosConfiguration> configDataProvider) {

        super(ActionType.RAISE_HOLD_RECOVERY_ACCOUNTING_HANDLER,
                actorConfigMap.get(ActionType.RAISE_HOLD_RECOVERY_ACCOUNTING_HANDLER),
                connectionRegistry, mapper, retryStrategyFactory,
                exceptionHandlingFactory, DisputeWorkflowMessage.class,
                Set.of(JsonProcessingException.class));
        this.disputeService = disputeService;
        this.merchantService = merchantService;
        this.configDataProvider = configDataProvider;
    }

    @Override
    protected boolean handle(
            final DisputeWorkflowMessage disputeWorkflowMessage,
            final MessageMetadata messageMetadata) {

        final var transactionReferenceId = disputeWorkflowMessage.getTransactionReferenceId();
        final var disputeWorkflowId = disputeWorkflowMessage.getDisputeWorkflowId();

        final var disputeWorkflow = disputeService
                .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);
        final var dispute = disputeWorkflow.getDispute();

        final var mcc = merchantService.getMcc(dispute.getMerchantId());

        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                disputeWorkflow);

        final var holdAmount = AccountingEventUtils.getHoldAmount(financialDisputeWorkflow);
        final var eventGenerationType = AccountingEventUtils.getHoldGenerationType(dispute);

        final var recoveryEvent = AccountingEventUtils.toHoldAccountingEvent(
                configDataProvider.getData(),
                dispute, disputeWorkflow,
                eventGenerationType,
                transactionReferenceId, mcc, holdAmount);

        final Map<Object, Object> transitionContext = new HashMap<>();
        transitionContext.put(Constants.HOLD_RECOVERY_EVENT, recoveryEvent);

        disputeService
            .triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS, transactionReferenceId, disputeWorkflowId,
                DisputeWorkflowEvent.RAISE_RECOVER_HOLD_EVENT, transitionContext);

        return true;
    }
}
