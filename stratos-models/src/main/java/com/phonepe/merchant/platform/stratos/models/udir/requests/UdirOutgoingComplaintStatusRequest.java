package com.phonepe.merchant.platform.stratos.models.udir.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequestVisitor;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UdirOutgoingComplaintStatusRequest extends CheckStatusRequest {

    @NotNull
    private String complaintId;

    public UdirOutgoingComplaintStatusRequest() {
        super(DisputeTypeDto.UDIR_OUTGOING_COMPLAINT);
    }

    @Builder
    public UdirOutgoingComplaintStatusRequest(final String transactionId,
        final String complaintId) {

        super(DisputeTypeDto.UDIR_OUTGOING_COMPLAINT, transactionId);
        this.complaintId = complaintId;
    }

    @Override
    public <T> T accept(final CheckStatusRequestVisitor<T> checkStatusRequestVisitor) {
        return checkStatusRequestVisitor.visit(this);
    }
}
