# Contribution Guidelines

1. Package name should be kept in plural
2. Class name should be kept in singular
3. Use lombok annotations wherever possible to reduce boilerplate code
    1. For classes with final fields and guice injection use below annotation instead of constructor
       ```java
       @RequiredArgsConstructor(onConstructor = @__({@Inject}))
       ```
4. Sort annotation in order of their length from short to long for better cosmetic view of the code
5. Use `final var` for all variable declaration
6. Use Java 9, 10, 11 features wherever possible instead of using Guava or Apache Commons
    1. `List.of(e1, e2, e3, ...)`
    2. `Map.of(k1, v1, k2, v2, ...)`  
    3. `Objects.requireNonNullElse()`
    4. `Objects.requireNonNullElseGet()`
    5. `String.isBlank()`   
    6. `Predicate.not()`
    7. etc
7. Use only annotation and method based bindings and provide for Guice (Refer to guice package)
8. Code which can be used across dispute types should be added in com/phonepe/merchant/platform/stratos/server/core package
    1. Interfaces which needs to be implemented by spokes
    2. Infra component clients
    3. Generic logic which can be used across all dispute types
9. Any logic specific to any dispute type should go to com/phonepe/merchant/platform/stratos/server/spokes/disputeType packages
    1. API specific to console of a dispute type
    2. State machine, transition definition and dispute type specific handling
    3. Implementation of dispute type specific implementations of generic interfaces
10. Enums used under entities is persisted with its Ordinal in Database hence only Additions are allowed in entity Enums and no removal or modifications in sequence are allowed for the same (No Exception to this Rule)
11. Database table name should be singular
12. Database index names should start from `idx_` for non-unique index and `idx_unq_` for unique index

Follow all the above mentioned guidelines until there is exceptional case with proper explanation to break the same.