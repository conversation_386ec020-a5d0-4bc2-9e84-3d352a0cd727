package com.phonepe.merchant.platform.stratos.models.row.requests;

import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RowDownloadRequest {

    @NotEmpty
    private List<RowDownloadData> rowList;

    @NotNull
    private RowTypeDto rowType;

    @NotNull
    private FileFormat fileFormat;
}
