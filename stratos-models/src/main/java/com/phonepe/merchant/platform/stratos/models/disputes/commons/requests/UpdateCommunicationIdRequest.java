package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;

import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCommunicationIdRequest {

    @Valid
    @NotEmpty
    private List<DisputeWorkflowKey> disputeWorkflowKeys;

    @NotEmpty
    private String communicationId;
}
