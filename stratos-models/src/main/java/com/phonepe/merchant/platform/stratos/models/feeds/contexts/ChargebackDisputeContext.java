package com.phonepe.merchant.platform.stratos.models.feeds.contexts;

import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargebackDisputeContext extends DisputeContext {

    private String instrumentTransactionId;

    private String disputeReferenceId;

    private String rrn;

    private String chargebackSourceType;

    private String chargebackSourceId;

    private long acceptedAmount;

    public ChargebackDisputeContext() {
        super(DisputeContextType.CHARGEBACK);
    }

    @Builder
    public ChargebackDisputeContext(
        final String instrumentTransactionId,
        final String disputeReferenceId,
        final String rrn,
        final String chargebackSourceType,
        final String chargebackSourceId,
        final long acceptedAmount) {
        this();
        this.instrumentTransactionId = instrumentTransactionId;
        this.disputeReferenceId = disputeReferenceId;
        this.rrn = rrn;
        this.chargebackSourceType = chargebackSourceType;
        this.chargebackSourceId = chargebackSourceId;
        this.acceptedAmount = acceptedAmount;
    }
}
