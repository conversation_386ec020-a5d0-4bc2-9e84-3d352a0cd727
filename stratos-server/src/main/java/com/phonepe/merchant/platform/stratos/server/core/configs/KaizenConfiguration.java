package com.phonepe.merchant.platform.stratos.server.core.configs;

import com.phonepe.merchant.platform.stratos.models.kaizen.KaizenProfile;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KaizenConfiguration {

    @NonNull
    private String organization;

    @NonNull
    private String namespace;

    @NonNull
    private Map<String, Map<String,KaizenProfile>> disputeKaizenConfigMap;

    public KaizenProfile getKaizenProfile(@NonNull DisputeType disputeType, @NonNull DisputeStage disputeStage, @NonNull String disputeCommunicationType) {
        return disputeKaizenConfigMap.get(String.format("%s_%s", disputeType, disputeStage)).get(disputeCommunicationType);
    }
}