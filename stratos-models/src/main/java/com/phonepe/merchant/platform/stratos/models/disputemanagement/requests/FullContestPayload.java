package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FullContestPayload extends ContestPayload{

    public FullContestPayload(){
        this.setContestType(ContestType.FULL_CONTEST);
    }
    @Builder
    public FullContestPayload(
            final String merchantId,
            final String disputeId) {
        super(ContestType.FULL_CONTEST, merchantId, disputeId);
    }
}
