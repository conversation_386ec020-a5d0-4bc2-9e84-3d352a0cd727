package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class StateDateRangeFilter extends DisputeFilter {

    @NotEmpty
    private List<String> states;

    @Valid
    @NotNull
    private DateRange dateRange;

    @NotEmpty
    private Set<DisputeTypeDto> disputeTypes;

    public StateDateRangeFilter() {
        super(DisputeFilterType.STATE_DATE_RANGE);
    }

    @Builder
    public StateDateRangeFilter(
        @Singular final List<String> states,
        final DateRange dateRange,
        final Set<DisputeTypeDto> disputeTypes) {
        this();
        this.states = states;
        this.dateRange = dateRange;
        this.disputeTypes = disputeTypes;
    }

    @JsonProperty("state")
    public void setState(final String state) {
        this.states = Collections.singletonList(state);
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
