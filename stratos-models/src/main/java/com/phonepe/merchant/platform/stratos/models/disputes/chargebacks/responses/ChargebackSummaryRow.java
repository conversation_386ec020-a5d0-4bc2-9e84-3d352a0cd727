package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "File Id",
    "Chargeback Id",
    "Chargeback Workflow Id",
    "Chargeback Workflow Version",
    "Chargeback Type",
    "Chargeback Stage",
    "Payment Reference Id",
    "Merchant Id",
    "Merchant Transaction Id",
    "Merchant Order Id",
    "Merchant Name",
    "UPI/PG Transaction ID",
    "Dispute Reference ID",
    "RRN",
    "FreshDesk Ticket ID",
    "Original Transaction Amount",
    "Original Transaction Date",
    "Original Transaction State",
    "Reversal Transaction Ids",
    "Reversal UTR Ids",
    "Reversal States",
    "Reversal Dates",
    "Reversal Amounts",
    "Chargeback Amount",
    "Accepted Amount",
    "Penalty Amount",
    "Current State",
    "Chargeback Issuer",
    "Merchant Type",
    "Raised At",
    "Respond By",
    "Chargeback Category",
    "Area",
    "City",
    "State",
    "Pin Code",
    "Error Message",
    "Network"
})
public class ChargebackSummaryRow {

    @JsonProperty("Chargeback Id")
    private String disputeId;

    @JsonProperty("File Id")
    private String disputeFileId;

    @JsonProperty("Chargeback Workflow Id")
    private String disputeWorkflowId;

    @JsonProperty("Chargeback Workflow Version")
    private String disputeWorkflowVersion;

    @JsonProperty("Chargeback Type")
    private String disputeType;

    @JsonProperty("Chargeback Stage")
    private String disputeStage;

    @JsonProperty("Payment Reference Id")
    private String transactionReferenceId;

    @JsonProperty("Merchant Id")
    private String merchantId;

    @JsonProperty("Merchant Transaction Id")
    private String merchantTransactionId;

    @JsonProperty("Merchant Order Id")
    private String merchantOrderId;

    @JsonProperty("Merchant Name")
    private String merchantName;

    @JsonProperty("UPI/PG Transaction ID")
    private String instrumentTransactionId;

    @JsonProperty("Dispute Reference ID")
    private String disputeReferenceId;

    @JsonProperty("RRN")
    private String rrn;

    @JsonProperty("FreshDesk Ticket ID")
    private String communicationId;

    @JsonProperty("Original Transaction Amount")
    private double transactionAmount;

    @JsonProperty("Original Transaction Date")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss a")
    private LocalDateTime originalTransactionDate;

    @JsonProperty("Original Transaction State")
    private String originalTransactionState;

    @JsonProperty("Reversal Transaction Ids")
    private String reversalTransactionIds;

    @JsonProperty("Reversal UTR Ids")
    private String reversalUtrIds;

    @JsonProperty("Reversal States")
    private String reversalStates;

    @JsonProperty("Reversal Dates")
    private String reversalDates;

    @JsonProperty("Reversal Amounts")
    private String reversalAmounts;

    @JsonProperty("Chargeback Amount")
    private long disputedAmount;

    @JsonProperty("Accepted Amount")
    private long acceptedAmount;

    @JsonProperty("Penalty Amount")
    private long penaltyAmount;

    @JsonProperty("Current State")
    private String currentState;

    @JsonProperty("Chargeback Issuer")
    private String disputeIssuer;

    @JsonProperty("Merchant Type")
    private String merchantType;

    @JsonProperty("Raised At")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss a")
    private LocalDateTime raisedAt;

    @JsonProperty("Respond By")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss a")
    private LocalDateTime respondBy;


    @JsonProperty("Chargeback Category")
    private String disputeCategory;

    @JsonProperty("Area")
    private String area;

    @JsonProperty("City")
    private String city;

    @JsonProperty("State")
    private String state;

    @JsonProperty("Pin Code")
    private String pinCode;

    @JsonProperty("Error Message")
    private String errorMessage;

    @JsonProperty("Network")
    private String network;
}
