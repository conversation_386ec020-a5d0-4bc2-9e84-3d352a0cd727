package com.phonepe.merchant.platform.stratos.models.callbacks.responses;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CallbackResponse {
    private String disputeId;
    private DisputeTypeDto disputeType;
    private DisputeStageDto disputeStage;
    private String merchantId;
    private DisputeStatus previousState;
    private DisputeStatus currentState;
    private LocalDateTime eventTime;

}
