package com.phonepe.central.stratos.notification;

import com.phonepe.central.stratos.notification.impl.PenaltyCreatedNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyDisbursedNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyDueNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyGrowingNotification;
import com.phonepe.central.stratos.notification.impl.PenaltyProbableNotification;

public interface ChangeNotificationVisitor<T> {

    T visit(PenaltyProbableNotification notification);

    T visit(PenaltyDueNotification notification);

    T visit(PenaltyCreatedNotification notification);

    T visit(PenaltyGrowingNotification notification);

    T visit(PenaltyDisbursedNotification notification);

}
