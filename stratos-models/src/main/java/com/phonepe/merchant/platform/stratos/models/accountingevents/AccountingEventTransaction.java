package com.phonepe.merchant.platform.stratos.models.accountingevents;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingEventTransaction {

    @NotNull
    @NonNull
    private AccountingTransactionType type;

    @Min(1L)
    private long amount;

    @NonNull
    @NotEmpty
    private String mcc;

    @NonNull
    @NotEmpty
    private String merchant;

    @NonNull
    @NotEmpty
    private String originalTransactionId;
}
