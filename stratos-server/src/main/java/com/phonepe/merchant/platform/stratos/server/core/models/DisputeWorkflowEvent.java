package com.phonepe.merchant.platform.stratos.server.core.models;

/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */
public enum DisputeWorkflowEvent {

    CREATE_ENTRY,

    REQUEST_RGCS_ACCEPTANCE,
    COMPLETE_RGCS_ACCEPTANCE,

    REQUEST_REPRESENTMENT,
    COMPLETE_NPCI_REPRESENTMENT,
    RECEIVE_CREDIT,

    BLOCK_REFUND,

    RECEIVE_FULFILMENT_DOCUMENTS,

    RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
    COMPLETE_NPCI_PARTIAL_REPRESENTMENT,
    RECEIVE_PARTIAL_CREDIT,

    NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL,

    MERCHANT_ACCEPT_CHARGEBACK,
    COMPLETE_NPCI_ACCEPTANCE,

    REQUEST_ABSORB_CHARGEBACK,
    REJECT_ABSORB_CHAR<PERSON>BACK,
    APPROVE_ABSORB_CHARGEBACK,
    <PERSON>ORB_CHARGEBACK,
    REVERSE_ABSORB_CHARGEBACK,

    REQUEST_RECOVER_CHARGEBACK,
    REJECT_RECOVER_CHARGEBACK,
    APPROVE_RECOVER_CHARGEBACK,
    RAISE_RECOVER_CHARGEBACK_EVENT,
    ACCEPT_RECOVER_CHARGEBACK_EVENT,

    REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK,
    REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK,
    APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK,
    RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,
    ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT,

    RECEIVE_DEBIT,
    RECEIVE_PARTIAL_DEBIT,
    COMPLETE_PG_REPRESENTMENT,
    COMPLETE_PG_ACCEPTANCE,
    COMPLETE_PG_PARTIAL_REPRESENTMENT,
    NPCI_REJECTED_COMPLAINT,
    PAYMENT_CALL_FAILED,
    PAYMENT_CALL_TIMEOUT,
    NPCI_ACCEPTED_COMPLAINT,
    UDIR_NPCI_RESPONSE_RECEIVED,
    UDIR_NPCI_RESPONSE_NOT_RECEIVED,
    INTERNAL_MID_REQUEST_REPRESENTMENT,
    RESET_CHARGEBACK,

    KS_ENABLED,

    //TOA specific events
    INITIATE_TOA,
    TOA_INITIATED,
    TOA_INITIATION_FAILED_AFTER_MAX_AUTO_RETRY,
    TOA_BLOCKED_TO_EXTERNAL_PROCESSED,
    RE_INITIATE_TOA,
    INITIATION_FAILED_TO_EXTERNAL_PROCESSED,
    INITIATED_TO_PENDING_AFTER_MAX_AUTO_RETRY,
    PENDING_TO_COMPLETED,
    PENDING_TO_FAILED,
    INITIATED_TO_FAILED,
    INITIATED_TO_COMPLETED,
    PAY_FAILED_TO_RECEIVED,
    PAY_FAILED_TO_MAX_RETRY,
    PAY_FAILED_TO_INITIATED,
    RETRY_FAILED_TOA,
    MAX_RETRY_TO_PROCESSED_EXTERNALLY,
    TOA_BLOCKED_TO_RECEIVED,
    REPRESENTMENT_REQUIRED_TO_CREDIT_RECEIVED,
    FULFILMENT_DOCUMENTS_RECEIVED_TO_CREDIT_RECEIVED,
    MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED,
    COMPLETE_REPRESENTMENT,
    COMPLETE_ACCEPTANCE,
    COMPLETE_PARTIAL_REPRESENTMENT,
    FRAUD_REJECT, // Called during reconcile or manual action by fra ops
    FRAUD_REJECTED_TO_REPRESENTMENT,
    FRAUD_REJECTED_TO_ACCEPTANCE,
    FRAUD_REJECTED_TO_FULFILMENT,
    FRAUD_REJECTED_TO_PARTIAL_FULFILMENT,
    SUSPECTED_FRAUD,
    SUSPECTED_FRAUD_TO_ACCEPTANCE, // Called during reconcile or manual action by fra ops
    CREATE_CHARGEBACK_REFUND,
    INITIATE_CHARGEBACK_REFUND,
    CHARGEBACK_REFUND_ACCEPTED,
    TOA_RECEIVED_TO_OPENED,
    TOA_OPENED_TO_CLOSED,
    TOA_OPENED_TO_INITIATED,
    FRAUD_REPRESENTMENT_COMPLETED,
    NPCI_ACK_CHARGEBACK,
    PARTIAL_ACCEPT_CHARGEBACK,
    FULLY_ACCEPT_CHARGEBACK,
    REJECTED_CHARGEBACK,
    ACCEPTED_CHARGEBACK,
    INITIATION_COMPLETED,
    MAX_RETRY_TO_UNABLE_TO_PROCESS,
    SEND_MERCHANT_COMMUNICATION,
    COLLECT_MERCHANT_RESPONSE,
    HOLD,
    END_WORKFLOW,
    RAISE_RECOVER_HOLD_EVENT,
    ACCEPT_RECOVER_HOLD_EVENT,
    RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT,
    ACCEPT_REVERSAL_OF_RECOVERED_HOLD_EVENT,
    APPROVE_REVERSAL_OF_RECOVERED_HOLD,
    REQUEST_MERCHANT_ACTION,
    REJECT_EVIDENCE;
}
