package com.phonepe.merchant.platform.stratos.models.files.filters;

import com.phonepe.merchant.platform.stratos.models.files.FileFilterType;
import com.phonepe.merchant.platform.stratos.models.files.FileFilter;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FileNameFileFilter extends FileFilter {

    @NotEmpty
    private String fileName;

    public FileNameFileFilter() {
        super(FileFilterType.FILE_NAME);
    }

    @Builder
    public FileNameFileFilter(final String fileName) {
        this();
        this.fileName = fileName;
    }


    @Override
    public <T> T accept(final FIleFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
