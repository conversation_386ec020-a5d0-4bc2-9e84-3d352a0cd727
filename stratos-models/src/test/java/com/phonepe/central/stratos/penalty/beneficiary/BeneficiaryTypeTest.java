package com.phonepe.central.stratos.penalty.beneficiary;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class BeneficiaryTypeTest {

    @Test
    public void testEnumValues() {
        BeneficiaryType[] expectedValues = BeneficiaryType.values();
        assertEquals(2, expectedValues.length);
        assertEquals(BeneficiaryType.USER, expectedValues[0]);
        assertEquals(BeneficiaryType.MERCHANT, expectedValues[1]);
    }

    @Test
    public void testValueOf() {
        assertEquals(BeneficiaryType.USER, BeneficiaryType.valueOf("USER"));
        assertEquals(BeneficiaryType.MERCHANT, BeneficiaryType.valueOf("MERCHANT"));
    }
}
