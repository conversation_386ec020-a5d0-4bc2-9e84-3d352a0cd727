package com.phonepe.merchant.platform.stratos.models.files;

import static com.phonepe.merchant.platform.stratos.models.files.FileFilterType.Names.DISPUTE_FILE_TYPE_DATE_RANGE;
import static com.phonepe.merchant.platform.stratos.models.files.FileFilterType.Names.FILE_NAME;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.files.filters.DisputeFileTypeDateRange;
import com.phonepe.merchant.platform.stratos.models.files.filters.FileNameFileFilter;
import com.phonepe.merchant.platform.stratos.models.files.filters.FIleFilterVisitor;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "filterType", visible = true)
@JsonSubTypes(value = {
    @Type(value = FileNameFileFilter.class, name = FILE_NAME),
    @Type(value = DisputeFileTypeDateRange.class, name = DISPUTE_FILE_TYPE_DATE_RANGE)
})
public abstract class FileFilter {

    @NotNull
    private FileFilterType fileFilterType;

    public abstract <T> T accept(FIleFilterVisitor<T> filterVisitor);
}
