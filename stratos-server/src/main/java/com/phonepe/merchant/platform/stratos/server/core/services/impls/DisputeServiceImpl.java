package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.aerospike.client.AerospikeException;
import com.google.inject.Inject;
import com.google.inject.Provider;
import com.google.inject.Singleton;
import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.kratos.base.ChargeBackAcceptanceCheck;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalCreditTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.PaginationDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.ContestPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DateRangeParam;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeWorkflowFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.PartialContestPayload;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeData;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeDetails;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeEvent;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummaries;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeSummary;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeWorkflowSummaries;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.FraudRepresentmentDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.IndefiniteHoldReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundInitiationReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.SuspectedFraudChargebackDisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileTypeVisitor;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.RefundAction;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CheckStatusRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DownloadReportRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.UpdateCommunicationIdRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CheckStatusResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.DisputeCreationCommand;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.DisputeCreationLockKey;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.keys.TransitionLockKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction.KratosRecommendedActionVisitor;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.creator.dispute.DisputeCreator;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.StratosErrorEvent;
import com.phonepe.merchant.platform.stratos.server.core.guice.bindings.Configs;
import com.phonepe.merchant.platform.stratos.server.core.helpers.fra.EdcBaseContext;
import com.phonepe.merchant.platform.stratos.server.core.helpers.fra.EdcPayload;
import com.phonepe.merchant.platform.stratos.server.core.helpers.fra.Payload;
import com.phonepe.merchant.platform.stratos.server.core.helpers.fra.PaymentsBaseContext;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.DisbursementDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.FraudActionDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.NetBankingDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.SettlementOnHoldMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersionVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.EnumClass;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.KratosProcessorActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeCreatorRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.resources.housekeeping.requests.DisputeWorkflowUpdateRequest;
import com.phonepe.merchant.platform.stratos.server.core.services.AccountingEventService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.KaizenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.AccountingEventUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.AuthorizationUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeStateMachineGraphService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.KratosService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.FileUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.core.validator.ValidationService;
import com.phonepe.merchant.platform.stratos.server.core.visitors.RefundEligibilityStateVisitor;
import com.phonepe.merchant.platform.stratos.server.core.visitors.RefundEligibilityStateVisitorV2;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.TransformationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.DownloadReportVisitorImpl;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.NetBankingChargebackService;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.UdirCheckStatusProcessor;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.olympus.im.client.OlympusIMClient;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.ruleengine.model.integration.FraudAction;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequest;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequestMerchant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.ws.rs.core.Response;

import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Restrictions;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.StateMachine;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.*;

@Slf4j
@Singleton
public class DisputeServiceImpl implements DisputeService {

    private final DisputeRepository disputeRepository;
    private final AccountingEventService accountingEventService;
    private final OlympusIMClient olympusIMClient;
    private final DisputeWorkflowRepository disputeWorkflowRepository;

    private final DisputeStateMachineGraphService disputeStateMachineGraphService;

    private final Provider<DisputeStateMachineRegistry> disputeStateMachineRegistryProvider;
    private final Provider<KratosProcessorActor> kratosProcessorActorProvider;

    private final DisputeMetadataRepository disputeMetadataRepository;
    private final DisputeMetadataHelper disputeMetadataHelper;
    private final KaizenService kaizenService;

    private final MerchantService merchantService;
    private final EdcService edcService;
    private final PaymentsService paymentsService;
    private final KratosService kratosService;
    private final EventIngester eventIngester;
    private final RefundService refundService;
    private final Map<String, FileConfig> fileConfigs;
    private final Map<DisputeWorkflowState, Long> disputeWorkflowStateTTLDaysMap;
    private final String docstoreBaseUrl;

    private static final String TRANSACTION_REFERENCE_ID = "transactionReferenceId";
    private static final String DISPUTE_TYPE = "disputeType";

    private static final String ERROR_MESSAGE_INVALID_PAYLOAD = "Invalid build Chargeback kratos Payload for dispute type ";

    private static final String ERROR_MESSAGE_DISPUTE_WORKFLOW_NOT_FOUND = "Unable to find dispute workflow for given inputs";

    private static final Set<DisputeWorkflowState> SUSPECTED_RECONCILABLE_STATES = Set.of(
        DisputeWorkflowState.SUSPECTED_FRAUD, DisputeWorkflowState.REFUND_BLOCKED,
        DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED);
    public static final Set<DisputeMetadataType> DISPUTE_WITH_REFUND = new LinkedHashSet<>( //NOSONAR
            Arrays.asList(DisputeMetadataType.NETBANKING_DISPUTE_METADATA, DisputeMetadataType.SOH_METADATA));


    private final UdirCheckStatusProcessor udirCheckStatusProcessor;

    private final DownloadReportVisitorImpl downloadReportVisitor;

    private final ValidationService validationService;

    private final DisputeCreatorRegistry disputeCreatorRegistry;
    private final Map<DisputeType, Long> holdTtlConfig;
    private final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap;

    private final DisputeCreationCommand disputeCreationCommand;

    @Inject
    @SuppressWarnings("java:S107")
    public DisputeServiceImpl(
            DisputeRepository disputeRepository,
            AccountingEventService accountingEventService, OlympusIMClient olympusIMClient,
            DisputeWorkflowRepository disputeWorkflowRepository,
            DisputeStateMachineGraphService disputeStateMachineGraphService,
            Provider<DisputeStateMachineRegistry> disputeStateMachineRegistryProvider,
            Provider<KratosProcessorActor> kratosProcessorActorProvider,
            DisputeMetadataRepository disputeMetadataRepository,
            DisputeMetadataHelper disputeMetadataHelper,
            KaizenService kaizenService, MerchantService merchantService,
            EdcService edcService,
            PaymentsService paymentsService,
            KratosService kratosService,
            EventIngester eventIngester,
            RefundService refundService, Map<String,
            FileConfig> fileConfigs,
            Map<DisputeWorkflowState, Long> disputeWorkflowStateTTLDaysMap,
            @Configs.DocstoreBaseUrl
            String docstoreBaseUrl,
            UdirCheckStatusProcessor udirCheckStatusProcessor,
            DownloadReportVisitorImpl downloadReportVisitor, ValidationService validationService,
            DisputeCreatorRegistry disputeCreatorRegistry,
            Map<DisputeType, Long> holdTtlConfig,
            Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap,
            DisputeCreationCommand disputeCreationCommand) {
        this.disputeRepository = disputeRepository;
        this.accountingEventService = accountingEventService;
        this.olympusIMClient = olympusIMClient;
        this.disputeWorkflowRepository = disputeWorkflowRepository;
        this.disputeStateMachineGraphService = disputeStateMachineGraphService;
        this.disputeStateMachineRegistryProvider = disputeStateMachineRegistryProvider;
        this.kratosProcessorActorProvider = kratosProcessorActorProvider;
        this.disputeMetadataRepository = disputeMetadataRepository;
        this.disputeMetadataHelper = disputeMetadataHelper;
        this.kaizenService = kaizenService;
        this.merchantService = merchantService;
        this.edcService = edcService;
        this.paymentsService = paymentsService;
        this.kratosService = kratosService;
        this.eventIngester = eventIngester;
        this.refundService = refundService;
        this.fileConfigs = fileConfigs;
        this.disputeWorkflowStateTTLDaysMap = disputeWorkflowStateTTLDaysMap;
        this.docstoreBaseUrl = docstoreBaseUrl;
        this.udirCheckStatusProcessor = udirCheckStatusProcessor;
        this.downloadReportVisitor = downloadReportVisitor;
        this.validationService = validationService;
        this.disputeCreatorRegistry = disputeCreatorRegistry;
        this.holdTtlConfig = holdTtlConfig;
        this.disputeWorkflowVersionMap = disputeWorkflowVersionMap;
        this.disputeCreationCommand = disputeCreationCommand;
    }

    @Override
    public Set<DisputeWorkflowEvent> getAuthorizedUpcomingEvents(
        final UserAuthDetails userAuthDetails,
        final DisputeType disputeType, final DisputeStage disputeStage,
        final DisputeWorkflowVersion disputeWorkflowVersion,
        final DisputeWorkflowState disputeWorkflowState) {

        final var transitions = disputeStateMachineRegistryProvider.get()
            .getInternalStateMachine(DisputeStateMachineRegistryKey.builder()
                .disputeType(disputeType)
                .disputeStage(disputeStage)
                .disputeWorkflowVersion(disputeWorkflowVersion)
                .build())
            .getTransitions();

        if (Objects.isNull(transitions)) {
            return Collections.emptySet();
        }

        return transitions.stream()
            .filter(t -> disputeWorkflowState == t.getSource().getId())
            .map(t -> t.getTrigger().getEvent())
            .filter(
                e -> AuthorizationUtils.isAuthorizedToTriggerEvent(olympusIMClient, userAuthDetails,
                    e))
            .collect(Collectors.toSet());
    }

    private StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerSystemUserEvent(
        DisputeWorkflow disputeWorkflow, DisputeWorkflowEvent event) {
        return triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeWorkflowId(),
            event, Constants.EMPTY_TRANSITION_CONTEXT);
    }

    @Override
    public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerEvent(
        final UserAuthDetails userAuthDetails,
        final String transactionReferenceId, final String disputeWorkflowId,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final TransitionContext requestTransitionContext) {

        final Map<Object, Object> stateMachineTransitionContext = new HashMap<>();
        stateMachineTransitionContext.put(TransitionContext.class, requestTransitionContext);
        stateMachineTransitionContext.put(Fields.transactionReferenceId, transactionReferenceId);
        stateMachineTransitionContext.put(Fields.disputeWorkflowId, disputeWorkflowId);
        stateMachineTransitionContext.put(UserAuthDetails.class, userAuthDetails);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
            .transitionKey(disputeWorkflowId)
            .build());

        return triggerEventWithContext(transactionReferenceId,
            disputeWorkflowId, disputeWorkflowEvent, stateMachineTransitionContext);
    }

    @Override
    public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerEvent(
        final UserAuthDetails userAuthDetails, final String transactionReferenceId,
        final String disputeWorkflowId, final DisputeWorkflowEvent disputeWorkflowEvent,
        final Map<Object, Object> requestTransitionContext) {

        final Map<Object, Object> stateMachineTransitionContext = new HashMap<>(
            requestTransitionContext);
        stateMachineTransitionContext.put(Fields.transactionReferenceId, transactionReferenceId);
        stateMachineTransitionContext.put(Fields.disputeWorkflowId, disputeWorkflowId);
        stateMachineTransitionContext.put(UserAuthDetails.class, userAuthDetails);
        stateMachineTransitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
            .transitionKey(disputeWorkflowId)
            .build());

        return triggerEventWithContext(transactionReferenceId, disputeWorkflowId,
            disputeWorkflowEvent, stateMachineTransitionContext);
    }

    @Override
    public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> createEntry(
        final Dispute dispute, final DisputeWorkflow disputeWorkflow) {

        ValidationUtils.validateEquals(dispute.getTransactionReferenceId(),
            disputeWorkflow.getTransactionReferenceId());
        ValidationUtils.validateEquals(dispute.getDisputeType(), disputeWorkflow.getDisputeType());
        ValidationUtils.validateEquals(dispute.getCurrentDisputeStage(),
            disputeWorkflow.getDisputeStage());
        Objects.requireNonNull(disputeWorkflow.getDisputeWorkflowId());

        final var transitionContext = new HashMap<>();
        transitionContext.put(Fields.transactionReferenceId,
            disputeWorkflow.getTransactionReferenceId());
        transitionContext.put(Fields.disputeWorkflowId,
            disputeWorkflow.getDisputeWorkflowId());
        transitionContext.put(Dispute.class, dispute);
        transitionContext.put(DisputeWorkflow.class, disputeWorkflow);
        transitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
            .transitionKey(disputeWorkflow.getDisputeWorkflowId())
            .build());

        final var disputeStateMachine = disputeStateMachineRegistryProvider.get()
            .get(DisputeStateMachineRegistryKey.builder()
                .disputeType(disputeWorkflow.getDisputeType())
                .disputeStage(disputeWorkflow.getDisputeStage())
                .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
                .build());

        return disputeStateMachine
            .sendEvent(DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.CREATE_ENTRY,
                transitionContext);
    }

    @Override
    public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerNpciAck(
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow) {

        ValidationUtils.validateEquals(dispute.getTransactionReferenceId(),
            disputeWorkflow.getTransactionReferenceId());
        ValidationUtils.validateEquals(dispute.getDisputeType(), disputeWorkflow.getDisputeType());
        ValidationUtils.validateEquals(dispute.getCurrentDisputeStage(),
            disputeWorkflow.getDisputeStage());
        Objects.requireNonNull(disputeWorkflow.getDisputeWorkflowId());

        final var transitionContext = new HashMap<>();
        transitionContext.put(Fields.transactionReferenceId,
            disputeWorkflow.getTransactionReferenceId());
        transitionContext.put(Fields.disputeWorkflowId,
            disputeWorkflow.getDisputeWorkflowId());
        transitionContext.put(Dispute.class, dispute);
        transitionContext.put(DisputeWorkflow.class, disputeWorkflow);
        transitionContext.put(UserAuthDetails.class, Constants.STRATOS_SYSTEM_USER_OLYMPUS);
        transitionContext.put(TransitionLockKey.class, TransitionLockKey.builder()
            .transitionKey(disputeWorkflow.getDisputeWorkflowId())
            .build());

        final var disputeStateMachine = disputeStateMachineRegistryProvider.get()
            .get(DisputeStateMachineRegistryKey.builder()
                .disputeType(disputeWorkflow.getDisputeType())
                .disputeStage(disputeWorkflow.getDisputeStage())
                .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
                .build());

        return disputeStateMachine
            .sendEvent(DisputeWorkflowState.RECEIVED, DisputeWorkflowEvent.NPCI_ACK_CHARGEBACK,
                transitionContext);
    }

    @Override
    public DisputeWorkflow validateAndGetDisputeWorkflow(
        final String transactionReferenceId,
        final String disputeWorkflowId) {

        return disputeWorkflowRepository
            .select(transactionReferenceId, disputeWorkflowId)
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND, Map.of(
                    Constants.MESSAGE, ERROR_MESSAGE_DISPUTE_WORKFLOW_NOT_FOUND,
                    Fields.transactionReferenceId, transactionReferenceId,
                    Fields.disputeWorkflowId, disputeWorkflowId
                )));
    }

    @Override
    public String getStateMachineSvgGraph(final DisputeType disputeType,
        final DisputeStage disputeStage, final DisputeWorkflowVersion disputeWorkflowVersion) {

        return disputeStateMachineGraphService
            .getSvgGraph(DisputeStateMachineRegistryKey.builder()
                .disputeType(disputeType)
                .disputeStage(disputeStage)
                .disputeWorkflowVersion(disputeWorkflowVersion)
                .build());
    }

    @Override
    public DisputeWorkflow getDisputeWorkflow(final String transactionReferenceId,
        final DisputeType disputeType, final DisputeStage disputeStage) {
        return disputeWorkflowRepository.select(transactionReferenceId, disputeType, disputeStage)
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND,
                    Map.of(
                        TRANSACTION_REFERENCE_ID, transactionReferenceId,
                        DISPUTE_TYPE, disputeType,
                        "disputeStage", disputeStage
                    ))
            );
    }

    @Override
    public List<DisputeWorkflow> getDisputeWorkflows(
        final DisputeStateMachineRegistryKey disputeStateMachineRegistryKey,
        final Set<DisputeWorkflowState> disputeWorkflowStates,
        final DateRange dateRange) {

        return disputeWorkflowRepository.scatterGather(
            DetachedCriteria.forClass(DisputeWorkflow.class)
                .add(Restrictions.eq(Fields.disputeType,
                    disputeStateMachineRegistryKey.getDisputeType()))
                .add(Restrictions.eq(Fields.disputeStage,
                    disputeStateMachineRegistryKey.getDisputeStage()))
                .add(Restrictions.eq(Fields.disputeWorkflowVersion,
                    disputeStateMachineRegistryKey.getDisputeWorkflowVersion()))
                .add(Restrictions.in(Fields.currentState, disputeWorkflowStates))
                .add(Restrictions.between(Fields.respondBy,
                    dateRange.getStartDate().atStartOfDay(),
                    dateRange.getEndDate().atTime(LocalTime.MAX))));
    }

    @Override
    public Dispute getDispute(final String transactionReferenceId, final DisputeType disputeType,
        final DisputeStage disputeStage) {
        return getDisputeOptional(transactionReferenceId, disputeType, disputeStage)
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    Map.of(
                        TRANSACTION_REFERENCE_ID, transactionReferenceId,
                        DISPUTE_TYPE, disputeType,
                        "disputeStage", disputeStage
                    ))
            );
    }

    @Override
    public Optional<Dispute> getDisputeOptional(String transactionReferenceId,
        DisputeType disputeType, DisputeStage disputeStage) {
        return disputeRepository.select(transactionReferenceId, disputeType, disputeStage);
    }

    public Dispute getDispute(String transactionReferenceId,
        String disputeId) {
        return disputeRepository.select(disputeId, transactionReferenceId).orElseThrow(
            () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                Map.of(
                    TRANSACTION_REFERENCE_ID, transactionReferenceId,
                    disputeId, disputeId
                ))
        );
    }

    @Override
    public void updateCommunicationIdInDisputeWorkflow(
        final UpdateCommunicationIdRequest updateCommunicationIdRequest,
        final String gandalfUserId, final UserType userType) {

        updateCommunicationIdRequest.getDisputeWorkflowKeys().forEach(key ->
            disputeWorkflowRepository.updateCommunicationId(
                key.getTransactionReferenceId(),
                key.getDisputeWorkflowId(),
                updateCommunicationIdRequest.getCommunicationId(),
                gandalfUserId, userType));
    }

    @Override
    public Dispute getDispute(final String transactionReferenceId, final DisputeType disputeType) {
        return disputeRepository.select(transactionReferenceId, disputeType)
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    Map.of(
                        TRANSACTION_REFERENCE_ID, transactionReferenceId,
                        DISPUTE_TYPE, disputeType
                    ))
            );
    }

    @Override
    public List<Dispute> getDispute(String transactionReferenceId) {
        return disputeRepository.select(transactionReferenceId);
    }

    @Override
    public CheckStatusResponse checkStatus(CheckStatusRequest checkStatusRequest) {
        var disputeType = DtoUtils.disputeDtoToType(checkStatusRequest.getDisputeType());

        return checkStatusRequest.accept(
            udirOutgoingComplaintStatusRequest -> udirCheckStatusProcessor.process(disputeType,
                udirOutgoingComplaintStatusRequest.getTransactionId(),
                udirOutgoingComplaintStatusRequest.getComplaintId()));
    }

    private StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> triggerEventWithContext(
        final String transactionReferenceId, final String disputeWorkflowId,
        final DisputeWorkflowEvent disputeWorkflowEvent,
        final Map<Object, Object> stateMachineTransitionContext) {

        final var disputeWorkflow =
            validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        final var disputeStateMachine = disputeStateMachineRegistryProvider.get()
            .get(DisputeStateMachineRegistryKey.builder()
                .disputeType(disputeWorkflow.getDisputeType())
                .disputeStage(disputeWorkflow.getDisputeStage())
                .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
                .build());
        log.info("triggering event : {} with context : {} for dispute workflow: {}",
                disputeWorkflowEvent, stateMachineTransitionContext, disputeWorkflow);

        return disputeStateMachine
            .sendEvent(disputeWorkflow.getCurrentState(), disputeWorkflowEvent,
                stateMachineTransitionContext);
    }

    @Override
    public Optional<DisputeWorkflow> getDisputeWorkflowOptional(
        final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage) {
        return disputeWorkflowRepository.select(transactionReferenceId, disputeType, disputeStage);
    }


    @Override
    public List<DisputeWorkflow> getAllDisputeWorkflows(String transactionReferenceId,
        DisputeType disputeType, DisputeStage disputeStage) {
        return disputeWorkflowRepository.selectAll(transactionReferenceId, disputeType,
            disputeStage);
    }

    public DisputeWorkflow getDisputeWorkflow(String disputeWorkflowId,
        DisputeType disputeType) {
        return disputeWorkflowRepository.select(disputeWorkflowId,
                DetachedCriteria.forClass(DisputeWorkflow.class, "disputeWorkflow")
                    .add(Restrictions.eq(Fields.disputeType, disputeType))
                    .add(Restrictions.eq(Fields.disputeWorkflowId, disputeWorkflowId))).stream()
            .findFirst()
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND, Map.of(
                    Constants.MESSAGE, ERROR_MESSAGE_DISPUTE_WORKFLOW_NOT_FOUND,
                    Fields.disputeType, disputeType,
                    Fields.disputeWorkflowId, disputeWorkflowId
                )));
    }

    @Override
    public Optional<DisputeMetadata> getDisputeMetadata(String transactionId,
        String disputeWorkflowId,
        DisputeMetadataType disputeMetadataType) {

        return disputeMetadataRepository.select(transactionId,
            DetachedCriteria.forClass(DisputeMetadata.class)
                .add(Restrictions.eq(DisputeMetadata.Fields.disputeWorkflowId, disputeWorkflowId))
                .add(Restrictions.eq(DisputeMetadata.Fields.disputeMetadataType,
                    disputeMetadataType))).stream().findFirst();
    }

    @Override
    public Optional<DisbursementDisputeMetadata> getDisbursementMetadata(final String disbursementId){
        return disputeMetadataRepository.getLatestDisbursementMetaData(disbursementId);
    }

    @Override
    public List<DisputeMetadata> getDisputeMetadata(String disputeWorkflowId,
        Set<DisputeMetadataType> types) {
        return disputeMetadataRepository.select(disputeWorkflowId, types);
    }

    public Map<String, Map<Integer, String>> getAllEnumsByEnumClassName(List<EnumClass> enumClass) {

        if (enumClass.isEmpty()) {
            enumClass = new ArrayList<>(Arrays.asList(EnumClass.values()));
        }

        return enumClass.stream()
            .collect(Collectors.toMap(
                Enum::name,
                enumClass1 -> getMapFromEnumClass(enumClass1.getEnumClassName())
            ));
    }

    @Override
    public Response updateDisputeWorkflows(DisputeWorkflowUpdateRequest updateReq) {
        DisputeType disputeType = DtoUtils.disputeDtoToType(updateReq.getDisputeType());
        FileType fileType = DtoUtils.fileDtoToType(updateReq.getFileType());
        final var config = fileConfigs.get(FileUtils.fileIdentifier(disputeType, fileType));
        List<DisputeWorkflow> disputeWorkflows = disputeWorkflowRepository
            .select(updateReq.getTransactionId());
        if (disputeWorkflows.isEmpty()) {
            DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION, Map.of(
                Constants.MESSAGE, "Unable to update dispute workflow for given inputs",
                Fields.transactionReferenceId, updateReq.getTransactionId()));
        }
        try {
            for (DisputeWorkflow dw : disputeWorkflows) {
                Long ttl = config.getDisputeStageTTLDaysMap().get(dw.getDisputeStage());
                final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class)
                    .add(Restrictions.eq(Fields.transactionReferenceId,
                        updateReq.getTransactionId()))
                    .add(Restrictions.eq(Fields.disputeWorkflowId, dw.getDisputeWorkflowId()));
                disputeWorkflowRepository.update(updateReq.getTransactionId(), detachedCriteria,
                    d -> {
                        dw.setRaisedAt(updateReq.getRaisedAt().atStartOfDay());
                        dw.setRespondBy(updateReq.getRaisedAt().atStartOfDay().plusDays(ttl));
                        return dw;
                    });
            }
        } catch (Exception e) {
            DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION, Map.of(
                Constants.MESSAGE, "Unable to update dispute workflow for given inputs",
                Fields.transactionReferenceId, updateReq.getTransactionId()
            ));
        }
        return Response.ok().build();
    }

    @Override
    public byte[] download(final DownloadReportRequest downloadReportRequest) {
        return downloadReportRequest.getReportType()
            .accept(downloadReportVisitor, downloadReportRequest);
    }

    public void creditDisputeAction(DisputeWorkflow disputeWorkflow,
        StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);
        final var refundCreditContext = stateContext.getExtendedState()
            .get(TransitionContext.class, InstitutionalCreditTransitionContext.class);
        Objects.requireNonNull(refundCreditContext);
        final var expectedRefundAmount = disputeWorkflow.getDisputedAmount() - financialDisputeWorkflow.getAcceptedAmount();

        if (expectedRefundAmount != refundCreditContext.getCreditAmount()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_CREDIT_AMOUNT, Map.of(
                Constants.MESSAGE, "Refund credit amount is not matching expected value",
                "expectedRefundAmount", expectedRefundAmount,
                "actualRefundAmount", refundCreditContext.getCreditAmount()));
        }

        disputeMetadataRepository.save(disputeMetadataHelper
            .toCreditDisputeMetadata(financialDisputeWorkflow, refundCreditContext));

    }

    @Override
    public void debitDisputeAction(DisputeWorkflow disputeWorkflow,
        StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);
        final var debitContext = stateContext.getExtendedState()
            .get(TransitionContext.class, InstitutionalDebitTransitionContext.class);
        Objects.requireNonNull(debitContext);

        if (financialDisputeWorkflow.getAcceptedAmount() != debitContext.getDebitAmount()) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DEBIT_AMOUNT, Map.of(
                Constants.MESSAGE, "Debit amount is not matching expected value",
                "expectedDebitAmount", financialDisputeWorkflow.getAcceptedAmount(),
                "actualDebitAmount", debitContext.getDebitAmount()));
        }

        disputeMetadataRepository.save(disputeMetadataHelper
            .toDebitDisputeMetadata(financialDisputeWorkflow, debitContext));
    }

    public ChargeBackAcceptanceCheck buildChargebackPayloadFromPayment(
        DisputeWorkflow disputeWorkflow,
        MerchantProfile merchantProfile) {
        return new Payload(disputeWorkflow, new PaymentsBaseContext(paymentsService
            .transactionDetailFromOriginalTransactionId(
                disputeWorkflow.getTransactionReferenceId()),
            disputeWorkflow, merchantProfile)).createChargeBackAcceptanceCheck();
    }

    public ChargeBackAcceptanceCheck buildChargebackPayloadFromEdc(DisputeWorkflow disputeWorkflow,
        MerchantProfile merchantProfile) {
        EdcTransactionDetailsResponse transactionDetails = edcService.getEdcTransactionDetailsFromTransactionId
            (disputeWorkflow.getDispute().getMerchantTransactionId());
        return new EdcPayload(disputeWorkflow, new EdcBaseContext(transactionDetails,
            disputeWorkflow, merchantProfile),
            transactionDetails).createChargeBackAcceptanceCheck();
    }

    @SneakyThrows
    public ChargeBackAcceptanceCheck buildChargebackPayload(DisputeWorkflow disputeWorkflow) {

        MerchantProfile merchantProfile = merchantService.getProfile(
            disputeWorkflow.getDispute().getMerchantId());

        return disputeWorkflow.getDisputeType()
            .accept(new DisputeTypeVisitor<ChargeBackAcceptanceCheck>() {
                @Override
                public ChargeBackAcceptanceCheck visitUpiChargeback() {
                    return buildChargebackPayloadFromPayment(disputeWorkflow, merchantProfile);
                }

                @Override
                public ChargeBackAcceptanceCheck visitPgChargeback() {
                    return buildChargebackPayloadFromPayment(disputeWorkflow, merchantProfile);
                }

                @Override
                public ChargeBackAcceptanceCheck visitUdirOutgoingComplaint() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD
                            + disputeWorkflow.getDisputeType());
                }

                @Override
                public ChargeBackAcceptanceCheck visitUdirIncomingComplaint() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD + disputeWorkflow.getDisputeType());
                }

                @Override
                public ChargeBackAcceptanceCheck visitP2PMToa() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD
                            + disputeWorkflow.getDisputeType());
                }

                @Override
                public ChargeBackAcceptanceCheck visitEdcChargeback() {
                    return buildChargebackPayloadFromEdc(disputeWorkflow, merchantProfile);
                }

                @Override
                public ChargeBackAcceptanceCheck visitNetBankingChargeback() {
                    return buildChargebackPayloadFromPayment(disputeWorkflow, merchantProfile);
                }

                @Override
                public ChargeBackAcceptanceCheck visitNotionalCreditToa() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD
                            + disputeWorkflow.getDisputeType());
                }

                @Override
                public ChargeBackAcceptanceCheck visitBbpsTatBreachToa() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD
                                + disputeWorkflow.getDisputeType());
                }

                @Override
                public ChargeBackAcceptanceCheck visitWalletChargeback() {
                    throw new UnsupportedOperationException(
                        ERROR_MESSAGE_INVALID_PAYLOAD
                            + disputeWorkflow.getDisputeType());
                }
                @Override
                public ChargeBackAcceptanceCheck visitFraFraud() {
                    throw new UnsupportedOperationException(
                            ERROR_MESSAGE_INVALID_PAYLOAD
                                    + disputeWorkflow.getDisputeType());
                }
            });
    }

    @Override
    public void fraudCheck(DisputeWorkflow disputeWorkflow) {
        disputeWorkflow.getDisputeType().accept(new DisputeTypeVisitor<>() {
            @Override
            public Void visitUpiChargeback() {
                triggerEventForFraudCheck(disputeWorkflow);
                return null;
            }

            @Override
            public Void visitPgChargeback() {
                triggerEventForFraudCheck(disputeWorkflow);
                return null;
            }

            @Override
            public Void visitUdirOutgoingComplaint() {
                return null;
            }

            @Override
            public Void visitUdirIncomingComplaint() {
                return null;
            }

            @Override
            public Void visitP2PMToa() {
                return null;
            }

            @Override
            public Void visitEdcChargeback() {
                visitUpiChargeback();
                return null;
            }

            @Override
            public Object visitNetBankingChargeback() {
                triggerEventForFraudCheck(disputeWorkflow);
                return null;
            }

            @Override
            public Object visitNotionalCreditToa() {
                return null;
            }

            @Override
            public Objects visitWalletChargeback() {
                return null;
            }

            @Override
            public Object visitBbpsTatBreachToa() {
                return null;
            }

            @Override
            public Object visitFraFraud() {
                return null;
            }
        });
    }

    @Override
    public void fraudSilentCheck(DisputeWorkflow disputeWorkflow) {
        try {
            fraudCheck(disputeWorkflow);
        } catch (Exception e) {
            // supress all error and log it in foxtrot
            log.error("fraudCheck failed", e);
            eventIngester.generateExceptionEvent(e);
        }
    }

    private void triggerEventForFraudCheck(DisputeWorkflow disputeWorkflow) {

        ChargeBackAcceptanceCheck payload = buildChargebackPayload(disputeWorkflow);

        String requestId = UUID.randomUUID().toString();

        FraudAction fraudAction = kratosService.chargebackRecommendAction(disputeWorkflow, payload,
            disputeWorkflow.getDisputeWorkflowId(), requestId);
        log.debug("triggerEventBasedOnFraudAction fraudAction: {}", fraudAction);

        KratosRecommendedAction recommendedAction = TransformationUtils.getRecommendedActionFromFraudAction(
            fraudAction);

        if (Constants.KRATOS_SKIP_VALIDATION_STATES.contains(disputeWorkflow.getCurrentState())) {
            // Skip kratos action
            return;
        }

        recommendedAction.accept(new KratosRecommendedActionVisitor<Void>() {
            @Override
            public Void visitNoop() {
                return visitAllow();
            }

            @Override
            public Void visitAllow() {
                if (disputeWorkflow.getCurrentState() == DisputeWorkflowState.SUSPECTED_FRAUD) {
                    triggerSystemUserEvent(disputeWorkflow, SUSPECTED_FRAUD_TO_ACCEPTANCE);
                }
                return null;
            }

            @Override
            public Void visitSuspect() {

                // save dispute metadata
                saveOrUpdateFraudMetadata(fraudAction, disputeWorkflow);
                if (Constants.ACTIONABLE_SUSPECTED_STATES.contains(
                    disputeWorkflow.getCurrentState())) {
                    triggerSystemUserEvent(disputeWorkflow, SUSPECTED_FRAUD);
                }
                return null;
            }

            @Override
            public Void visitBlock() {

                // save dispute metadata
                saveOrUpdateFraudMetadata(fraudAction, disputeWorkflow);
                triggerSystemUserEvent(disputeWorkflow, FRAUD_REJECT);

                return null;
            }
        });

        eventIngester.generateEvent(FoxtrotEventUtils
            .toKratosRecommendedActionEvent(disputeWorkflow, recommendedAction, fraudAction,
                requestId));
    }

    public void saveOrUpdateFraudMetadata(final FraudAction fraudAction,
        final DisputeWorkflow disputeWorkflow) {

        FraudActionDisputeMetadata fraudActionDisputeMetadata = disputeMetadataHelper.toFraActionDisputeMetadata(
            fraudAction, disputeWorkflow);

        final var detachedCriteria = DetachedCriteria.forClass(DisputeMetadata.class)
            .add(Restrictions.eq(DisputeMetadata.Fields.disputeWorkflowId,
                fraudActionDisputeMetadata.getDisputeWorkflowId()))
            .add(Restrictions.eq(DisputeMetadata.Fields.disputeMetadataType,
                fraudActionDisputeMetadata.getDisputeMetadataType()))
            .add(Restrictions.eq(FraudActionDisputeMetadata.Fields.actionType,
                fraudActionDisputeMetadata.getActionType()));
        Optional<DisputeMetadata> metadata = disputeMetadataRepository.select(
                fraudActionDisputeMetadata.getDisputeWorkflowId(), detachedCriteria).stream()
            .findFirst();

        if (metadata.isPresent()) {
            // update
            disputeMetadataRepository.update(metadata.get().getDisputeMetadataId(), value -> {
                if (value.isPresent()
                    && value.get() instanceof FraudActionDisputeMetadata fraudActionMetadata) {
                    fraudActionMetadata.setReasonCode(
                        TransformationUtils.getSafeEnumName(fraudAction.getResponseCode()));
                    return fraudActionMetadata;
                }
                return value.get();
            });
        } else {
            // save
            disputeMetadataRepository.save(fraudActionDisputeMetadata);
        }

    }


    @Override
    public RefundEligibilityResponse getRefundEligibility(final String transactionReferenceId) {
        List<DisputeWorkflow> disputeWorkflowList = disputeWorkflowRepository.select(
            transactionReferenceId);
        Optional<DisputeWorkflow> disputeWorkflow = disputeWorkflowList.stream().findFirst();
        if (disputeWorkflow.isPresent()) {
            return resolveRefundEligibilityForDisputeType(disputeWorkflow.get());
        } else {
            return TransformationUtils.buildResponseForRefundEligibility(RefundAction.PROCESS,
                "No Dispute has been raised for this transaction");
        }
    }

    @Override
    public void reconcile(final DisputeReconcileRequest reconcileRequest) {
        final var detachedCriteria = DetachedCriteria.forClass(DisputeWorkflow.class);
        reconcileRequest.accept(new ReconcileTypeVisitor<>() {
            @Override
            public Void visit(SuspectedFraudChargebackDisputeReconcileRequest request) {

                detachedCriteria.add(Restrictions.in(Fields.currentState, SUSPECTED_RECONCILABLE_STATES));
                detachedCriteria.add(Restrictions.gt(Fields.respondBy, LocalDateTime.now()));
                List<DisputeWorkflow> suspectedWorkflows = disputeWorkflowRepository
                    .selectByWorkflowState(detachedCriteria);

                suspectedWorkflows.forEach(disputeWorkflow -> {
                    try {
                        kratosProcessorActorProvider.get().publish(DisputeWorkflowMessage.builder()
                            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                            .build());
                    } catch (Exception e) {

                        log.error("Suspected Fraud reconcile failed for ", e);
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.RECONCILE_FAILURE, Map.of(
                                Constants.MESSAGE,
                                "Suspected fraud reconcile failure. " + e.getMessage(),
                                Fields.disputeWorkflowId, disputeWorkflow.getDisputeWorkflowId()
                            )
                        );
                    }
                });
                return null;
            }

            @Override
            public Void visit(RefundStatusReconRequest request) {
                detachedCriteria.add(
                    Restrictions.or(Restrictions.eq(Fields.currentState, DisputeWorkflowState.CB_REFUND_INITIATED),
                        Restrictions.eq(Fields.currentState, DisputeWorkflowState.CB_REFUND_INITIATED_COMPLETED)
                    ));
                List<DisputeWorkflow> refundReconWorkFlows = disputeWorkflowRepository.selectByWorkflowState(
                    detachedCriteria);
                refundReconWorkFlows.forEach(
                    DisputeServiceImpl.this::getRefundStatusAndUpdateState);
                return null;

            }

            @Override
            public Void visit(FraudRepresentmentDisputeReconcileRequest request) {
                detachedCriteria.add(Restrictions.in(Fields.currentState, DisputeWorkflowState.FRAUD_REPRESENTMENT_COMPLETED));
                List<DisputeWorkflow> fraudRepresentmentWorkflows = disputeWorkflowRepository
                    .selectByWorkflowState(detachedCriteria);

                fraudRepresentmentWorkflows.forEach(disputeWorkflow -> {
                    try {
                        getFraudStatusAndUpdateState(disputeWorkflow);
                    } catch (Exception e) {
                        log.error("Fraud Representment reconcile failed for ", e);

                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.RECONCILE_FAILURE, Map.of(
                                Constants.MESSAGE,
                                "Fraud Representment reconcile failure. " + e.getMessage(),
                                Fields.disputeWorkflowId, disputeWorkflow.getDisputeWorkflowId()
                            )
                        );
                    }
                });
                return null;
            }

            @Override
            public Object visit(IndefiniteHoldReconcileRequest request) {
                detachedCriteria.add(Restrictions.in(Fields.currentState, DisputeWorkflowState.HOLD));
                List<DisputeWorkflow> indefiniteHoldWorkflows = disputeWorkflowRepository
                        .selectByWorkflowState(detachedCriteria);
                indefiniteHoldWorkflows = indefiniteHoldWorkflows.stream().filter(dw ->
                        dw.getDispute().getCreatedAt().plusDays(
                            holdTtlConfig.get(dw.getDisputeType())
                        ).isBefore(LocalDateTime.now())
                ).toList();
                indefiniteHoldWorkflows.forEach(disputeWorkflow ->
                        triggerSystemUserEvent(disputeWorkflow, APPROVE_REVERSAL_OF_RECOVERED_HOLD));
                return null;
            }
            @Override
            public Void visit(RefundInitiationReconRequest request) {
                detachedCriteria.add(
                    Restrictions.or(Restrictions.eq(Fields.currentState, DisputeWorkflowState.PARTIAL_ACCEPTED_CHARGEBACK),
                        Restrictions.eq(Fields.currentState, DisputeWorkflowState.FULLY_ACCEPTED_CHARGEBACK)
                    ));
                List<DisputeWorkflow> refundReconWorkFlows = disputeWorkflowRepository.selectByWorkflowState(
                    detachedCriteria);
                refundReconWorkFlows.forEach(disputeWorkflow -> {
                    try {
                        triggerRefundInitiationEvent(disputeWorkflow);
                    }
                    catch (Exception exception)
                    {
                        log.error("Exception while reconcile initiation ",exception);
                        eventIngester.generateEvent(StratosErrorEvent.builder()
                            .errorCode(StratosErrorCodeKey.RECONCILE_FAILURE)
                            .message(String.format(
                                "Reconcile Failure for transactionID: %s, transactionType: %s",
                                disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeType()))
                            .build());
                    }
                });
                return null;
            }
        });
    }

    private void triggerRefundInitiationEvent(DisputeWorkflow disputeWorkflow) {
        triggerSystemUserEvent(disputeWorkflow, INITIATE_CHARGEBACK_REFUND);
    }

    @Override
    public DisputeSummaries getDisputes(DisputeFilterParams filterParams) {
        DetachedCriteria criteria = DetachedCriteria.forClass(Dispute.class)
            .add(Restrictions.eq(Dispute.Fields.merchantId, filterParams.getMerchantId()))
            .add(Restrictions.in(Dispute.Fields.disputeType,DisputeType.CHARGEBACKS))
            .addOrder(Order.desc(Dispute.Fields.createdAt));

        DateRangeParam dateRange = filterParams.getDateRange();
        if(dateRange!=null)
            criteria.add(Restrictions.between(Dispute.Fields.createdAt,
                dateRange.getStartDate().atStartOfDay(),
                dateRange.getEndDate().atTime(LocalTime.MAX)));

        List<Dispute> disputes = disputeRepository.scatterGather(criteria);
        //We get summaries for all queried disputes as we need to support filter on Status
        List<DisputeSummary> disputeSummaries = getDisputeSummaries(disputes);
        if(filterParams.getStatus() != null){
            disputeSummaries = disputeSummaries.stream().filter(
                    summary ->
                    summary.getDisputeStatus().equals(filterParams.getStatus()))
                .collect(Collectors.toList());
        }
        PaginationDetails paginationDetails = PaginationDetails.builder()
                .limit(filterParams.getLimit())
                .offset(filterParams.getOffset())
                .total(disputeSummaries.size())
                .build();
        if(!disputeSummaries.isEmpty()){
            if(filterParams.getOffset() >= disputeSummaries.size())
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAGINATION_PARAMETERS,
                        Collections.emptyMap());
            disputeSummaries = disputeSummaries.subList(filterParams.getOffset(),
                Math.min(filterParams.getOffset() + filterParams.getLimit(),
                    disputeSummaries.size()));
        }
        paginationDetails.setCount(disputeSummaries.size());
        return DisputeSummaries.builder()
                .paginationDetails(paginationDetails)
                .disputes(disputeSummaries)
                .merchantId(filterParams.getMerchantId())
                .build();
    }

    @Override
    public DisputeDetails getDisputeDetails(String disputeId, String merchantId) {
        List<DisputeWorkflow> disputeWorkflows = disputeWorkflowRepository.selectByDisputeId(disputeId);
        if(!disputeWorkflows.isEmpty()){
            Dispute dispute = disputeWorkflows.get(0).getDispute();
            DisputeWorkflow dw = disputeWorkflows.get(0);

            if(dispute.getMerchantId()==null || !dispute.getMerchantId().equals(merchantId))
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
                    Map.of("Dispute not found", disputeId));

            return DisputeDetails.builder()
                .disputeId(dispute.getDisputeId())
                .disputeStatus(getDisputeStatus(dw))
                .disputeData(DisputeData.builder()
                    .disputeCategory(DtoUtils.disputeCategoryToCategoryModel(dispute.getDisputeCategory()))
                    .disputeStage(DtoUtils.disputeStagetoStageModel(dispute.getCurrentDisputeStage()))
                    .disputeType(DtoUtils.disputeTypeToTypeModel(dispute.getDisputeType()))
                    .build())
                .transactionId(dispute.getTransactionReferenceId())
                .transactionType(DtoUtils.disputeTypeToTransType(dispute.getDisputeType()))
                .merchantTransactionId(dispute.getMerchantTransactionId())
                .transactionAmount(dispute.getTransactionAmount())
                .respondBy(dw.getRespondBy())
                .createdOn(dispute.getCreatedAt())
                .updatedOn(dw.getUpdatedAt())
                .disputeTimeline(getDisputeTimeLine(dispute.getTransactionReferenceId()))
                .build();
        }
        throw  DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_NOT_FOUND,
            Map.of("Dispute not found", disputeId));
    }

    @Override
    public DisputeDetails acceptDispute(String disputeId, String merchantId) {
        DisputeDetails disputeDetails = validateAndGetDispute(
                disputeId, merchantId, DisputeStatus.NEEDS_ACTION);
        //timeline will never be null or empty
        DisputeEvent disputeEvent = disputeDetails.getDisputeTimeline()
                .get(disputeDetails.getDisputeTimeline().size()-1);
        disputeWorkflowRepository.updateAcceptedAmount(disputeDetails.getTransactionId(),
                disputeEvent.getDisputeWorkflowId(),disputeEvent.getDisputedAmount());
        triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeDetails.getTransactionId(),
                disputeEvent.getDisputeWorkflowId(),
                DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK,
                Constants.EMPTY_TRANSITION_CONTEXT);
        return getDisputeDetails(disputeId, merchantId);
    }

    @Override
    public DisputeDetails contest(ContestPayload payload) {
        String disputeId = payload.getDisputeId();
        String merchantId = payload.getMerchantId();
        long acceptedAmount = 0;
        DisputeDetails disputeDetails = validateAndGetDispute(disputeId, merchantId, DisputeStatus.NEEDS_ACTION);
        DisputeEvent workflow = disputeDetails.getDisputeTimeline()
                .get(disputeDetails.getDisputeTimeline().size()-1);
        DisputeWorkflowEvent disputeEvent = DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;

        if(payload instanceof PartialContestPayload){
            long contestedAmount = ((PartialContestPayload) payload).getContestedAmount();
            if( contestedAmount < 0 || contestedAmount >= workflow.getDisputedAmount())
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_CONTESTED_AMOUNT, new HashMap<>());
            acceptedAmount = workflow.getDisputedAmount() - contestedAmount;
            disputeEvent = DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS;
        }

        disputeWorkflowRepository.updateAcceptedAmount(disputeDetails.getTransactionId(),
                workflow.getDisputeWorkflowId(),acceptedAmount);

        triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            disputeDetails.getTransactionId(),
            workflow.getDisputeWorkflowId(),
            disputeEvent,
            Constants.EMPTY_TRANSITION_CONTEXT);
        return getDisputeDetails(disputeId,merchantId);
    }

    public DisputeDetails validateAndGetDispute(String disputeId, String merchantId, DisputeStatus status){
        DisputeDetails dispute = getDisputeDetails(disputeId, merchantId);
        if(!dispute.getDisputeStatus().equals(status))
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.TRANSITION_NOT_ALLOWED, new HashMap<>());
        return dispute;
    }

    @Override
    public boolean isChargebackRecoveryDone(String disputeWorkflowId) {
        String accountingEventId = AccountingEventUtils.toAccountingEventId(
                disputeWorkflowId, EventGenerationType.CHARGEBACK_RECOVERY);
        try {
            accountingEventService.getAccountingEvent(accountingEventId);
            return true;
        }
        catch (Exception e){
            if (e instanceof DisputeException error && error.getErrorCode().equals(StratosErrorCodeKey.RAISED_ACCOUNTING_EVENT_NOT_FOUND)) {
                return false;
            }
            throw e;
        }
    }

    private List<DisputeEvent> getDisputeTimeLine(String transactionId) {
        List<DisputeWorkflow> dwList = disputeWorkflowRepository
                .select(transactionId);
        List<DisputeEvent> events = new ArrayList<>();
        Collections.reverse(dwList);
        for (DisputeWorkflow disputeWorkflow : dwList){
            events.add(toDisputeEvent(disputeWorkflow));
        }
        return events;
    }

    private DisputeEvent toDisputeEvent(DisputeWorkflow disputeWorkflow) {
        long acceptedAmount = 0L;
        if(disputeWorkflow instanceof FinancialDisputeWorkflow)
            acceptedAmount = ((FinancialDisputeWorkflow) disputeWorkflow).getAcceptedAmount();
        List<StoredDocumentUploadWithMetaDataActionMetadata> evidences = kaizenService.getAllEvidences(disputeWorkflow.getCommunicationId());
        return DisputeEvent.builder()
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .acceptedAmount(acceptedAmount)
            .disputeReason(null)
            .lastUpdated(disputeWorkflow.getUpdatedAt())
            .disputeState(disputeWorkflow.getCurrentState().accept(
                    MapperUtils.getStateMapper(disputeWorkflow.getDisputeWorkflowVersion()),
                    disputeWorkflow))
            .disputeStage(DtoUtils.disputeStageToStageDto(disputeWorkflow.getDisputeStage()))
            .evidenceDetails(evidences.stream()
                    .map(evidence -> TransformationUtils.toEvidenceDetail(evidence,
                            disputeWorkflow.getDisputeWorkflowId(),docstoreBaseUrl))
                    .toList())
            .build();
    }

    private List<DisputeSummary> getDisputeSummaries(List<Dispute> disputes) {
        Map<String,List<DisputeWorkflow>> workflows = getDisputeWorkflows(disputes);
        List<DisputeSummary> disputeSummaries = new ArrayList<>();
        disputes.forEach(dispute -> {
            //this is only for chargebacks so all are of FinancialDisputeWorkflow type
            FinancialDisputeWorkflow dw = (FinancialDisputeWorkflow) workflows.get(dispute.getDisputeId()).get(0);
            disputeSummaries.add(DisputeSummary.builder()
                .disputeId(dispute.getDisputeId())
                .disputeStatus(getDisputeStatus(dw))
                .disputeData(com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeData.builder()
                        .disputeCategory(DtoUtils.disputeCategoryToCategoryModel(dispute.getDisputeCategory()))
                        .disputeStage(DtoUtils.disputeStagetoStageModel(dispute.getCurrentDisputeStage()))
                        .disputeType(DtoUtils.disputeTypeToTypeModel(dispute.getDisputeType()))
                        .build())
                .transactionId(dispute.getTransactionReferenceId())
                .transactionType(DtoUtils.disputeTypeToTransType(dispute.getDisputeType()))
                .merchantTransactionId(dispute.getMerchantTransactionId())
                .transactionAmount(dispute.getTransactionAmount())
                .latestDisputedAmount(dw.getDisputedAmount())
                .latestAcceptedAmount(dw.getAcceptedAmount())
                .respondBy(dw.getRespondBy())
                .createdOn(dispute.getCreatedAt())
                .updatedOn(dw.getUpdatedAt())
            .build());
        });
        return disputeSummaries;
    }

    private DisputeStatus getDisputeStatus(DisputeWorkflow dw) {
        return dw.getCurrentState().accept(MapperUtils.getStateMapper(dw.getDisputeWorkflowVersion()),dw);
    }

    private Map<String, List<DisputeWorkflow>> getDisputeWorkflows(List<Dispute> disputes) {
        // single call to get all dispute workflows to avoid multiple DB calls
        List<DisputeWorkflow> workflows = disputeWorkflowRepository.scatterGather(
            DetachedCriteria.forClass(
                DisputeWorkflow.class,"disputeWorkflow")
            .add(Restrictions.in(Fields.disputeId,
                disputes.stream().map(Dispute::getDisputeId).collect(Collectors.toSet()))
            )
            .addOrder(Order.desc(Fields.createdAt))
        );
        Map<String,List<DisputeWorkflow>> toReturn = new HashMap<>();
        workflows.forEach(disputeWorkflow ->
            toReturn.computeIfAbsent(disputeWorkflow.getDisputeId(), k -> new ArrayList<>())
                .add(disputeWorkflow)
        );
        return toReturn;
    }

    private RefundEligibilityResponse resolveRefundEligibilityForDisputeType(
        final DisputeWorkflow disputeWorkflow) {
        final DisputeType disputeType = disputeWorkflow.getDisputeType();
        final DisputeStage disputeStage = disputeWorkflow.getDisputeStage();
        return disputeType.accept(new DisputeTypeVisitor<RefundEligibilityResponse>() {
            @Override
            public RefundEligibilityResponse visitUpiChargeback() {
                return resolveRefundEligibilityForDisputeWorkflowState(disputeWorkflow);
            }

            @Override
            public RefundEligibilityResponse visitPgChargeback() {
                return resolveRefundEligibilityForDisputeWorkflowState(disputeWorkflow);
            }

            @Override
            public RefundEligibilityResponse visitUdirOutgoingComplaint() {
                return disputeStage.accept(new DisputeStageVisitor<RefundEligibilityResponse>() {
                    @Override
                    public RefundEligibilityResponse visitFirstLevel() {
                        return TransformationUtils.buildResponseForRefundEligibility(
                            RefundAction.PROCESS,
                            "Udir Outgoing Complaint has been raised for this transaction");
                    }

                    @Override
                    public RefundEligibilityResponse visitPreArbitration() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                            Constants.MESSAGE,
                            "Second level Udir Outgoing Complaint is not supported."));
                    }

                    @Override
                    public RefundEligibilityResponse visitPreChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                            Constants.MESSAGE,
                            "Second level Udir Outgoing Complaint is not supported."));
                    }
                });
            }

            @Override
            public RefundEligibilityResponse visitUdirIncomingComplaint() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_DISPUTE_TYPE, Map.of(
                    Constants.MESSAGE, "Udir Incoming Complaints is not supported."));

            }

            @Override
            public RefundEligibilityResponse visitP2PMToa() {
                return disputeStage.accept(new DisputeStageVisitor<RefundEligibilityResponse>() {
                    @Override
                    public RefundEligibilityResponse visitFirstLevel() {
                        return TransformationUtils.buildResponseForRefundEligibility(
                            RefundAction.FAILED,
                            "Refund cannot be triggered for toa.");
                    }

                    @Override
                    public RefundEligibilityResponse visitPreArbitration() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                            Constants.MESSAGE, "Second level Toa is not supported."));
                    }

                    @Override
                    public RefundEligibilityResponse visitPreChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                                Constants.MESSAGE, "Second level Toa is not supported."));
                    }
                });
            }

            @Override
            public RefundEligibilityResponse visitEdcChargeback() {
                return disputeStage.accept(new DisputeStageVisitor<RefundEligibilityResponse>() {
                    @Override
                    public RefundEligibilityResponse visitFirstLevel() {
                        return resolveRefundEligibilityForDisputeWorkflowState(disputeWorkflow);
                    }

                    @Override
                    public RefundEligibilityResponse visitPreArbitration() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                            Constants.MESSAGE, " Second level Edc chargeback is not supported."));
                    }
                    @Override
                    public RefundEligibilityResponse visitPreChargeback() {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_STAGE, Map.of(
                                Constants.MESSAGE, "Second level Toa is not supported."));
                    }
                });
            }

            @Override
            public RefundEligibilityResponse visitNetBankingChargeback() {
                return visitEdcChargeback();
            }

            @Override
            public RefundEligibilityResponse visitNotionalCreditToa() {
                return visitP2PMToa();
            }

            @Override
            public RefundEligibilityResponse visitBbpsTatBreachToa() {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE, Map.of(
                        Constants.MESSAGE, "Refund eligibility not supported for BBPS_TAT_BREACH_TOA"));
            }

            @Override
            public RefundEligibilityResponse visitFraFraud() {
                return resolveRefundEligibilityForDisputeWorkflowState(disputeWorkflow);
            }
            @Override
            public RefundEligibilityResponse visitWalletChargeback() {
                return resolveRefundEligibilityForDisputeWorkflowState(disputeWorkflow);
            }
        });
    }

    private RefundEligibilityResponse resolveRefundEligibilityForDisputeWorkflowState(
        final DisputeWorkflow disputeWorkflow) {
        DisputeWorkflowState disputeWorkflowState = disputeWorkflow.getCurrentState();
        return disputeWorkflowState.accept(getRefundEligibilityVisitor(disputeWorkflow));
    }

    private RefundEligibilityStateVisitor getRefundEligibilityVisitor(
            final DisputeWorkflow disputeWorkflow) {
        return disputeWorkflow.getDisputeWorkflowVersion().accept(new DisputeWorkflowVersionVisitor<RefundEligibilityStateVisitor>() {
            @Override
            public RefundEligibilityStateVisitor visitV1() {
                return new RefundEligibilityStateVisitor();
            }
            @Override
            public RefundEligibilityStateVisitor visitV2() {
                return new RefundEligibilityStateVisitorV2(disputeWorkflow);
            }
        });
    }

    private void getRefundStatusAndUpdateState(DisputeWorkflow disputeWorkflow) {

        List<DisputeMetadata> disputeMetadata =  disputeMetadataRepository.select(
            disputeWorkflow.getDisputeWorkflowId(),DISPUTE_WITH_REFUND);
        if (disputeMetadata == null || disputeMetadata.isEmpty())
            throw DisputeExceptionUtil.error(
                    StratosErrorCodeKey.INVALID_REFUND_ID,
                    Map.of(Constants.MESSAGE, String.format(
                            "Could not find refundId associated with txnId %s and workflowId %s",
                            disputeWorkflow.getTransactionReferenceId(),
                            disputeWorkflow.getDisputeWorkflowId())));

        String disbursementId = getDisbursementId(disputeMetadata.get(0));
        val dispute = disputeWorkflow.getDispute();

        ROStatusRequest request = ROStatusRequestMerchant.builder()
            .merchantId(dispute.getMerchantId())
            .merchantTxnId(disbursementId)
            .build();

        val refundStatus = refundService.getRefundStatus(request);

        if (refundStatus.equals(RefundStatus.ACCEPTED) || refundStatus.equals(
            RefundStatus.COMPLETED)) {
            triggerSystemUserEvent(disputeWorkflow,
                DisputeWorkflowEvent.CHARGEBACK_REFUND_ACCEPTED);
        } else if (refundStatus.equals(RefundStatus.FAILED)) {
            triggerSystemUserEvent(disputeWorkflow, DisputeWorkflowEvent.INITIATED_TO_FAILED);
        }
    }


    private String getDisbursementId(DisputeMetadata disputeMetadata) {
        if(disputeMetadata.getDisputeMetadataType().equals(DisputeMetadataType.SOH_METADATA))
            return ((SettlementOnHoldMetadata)disputeMetadata).getDisbursementTransactionId();
        if(disputeMetadata.getDisputeMetadataType().equals(DisputeMetadataType.NETBANKING_DISPUTE_METADATA))
            return ((NetBankingDisputeMetadata)disputeMetadata).getDisbursementTransactionId();
        throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_DISPUTE_TYPE);
    }

    private void getFraudStatusAndUpdateState(DisputeWorkflow disputeWorkflow) {

        LocalDateTime expiryDate = disputeWorkflow.getUpdatedAt()
            .atZone(ZoneId.systemDefault())
            .toLocalDateTime()
            .plusDays(disputeWorkflowStateTTLDaysMap.get(DisputeWorkflowState.
                FRAUD_REPRESENTMENT_COMPLETED));

        if (LocalDateTime.now().isEqual(expiryDate) || LocalDateTime.now().isAfter(expiryDate)) {
            triggerSystemUserEvent(disputeWorkflow,
                DisputeWorkflowEvent.COMPLETE_REPRESENTMENT);
        }
    }

    public <T extends Enum<T>> Map<Integer, String> getMapFromEnumClass(Class<T> aEnum) {
        Map<Integer, String> map = new HashMap<>();
        for (var e : aEnum.getEnumConstants()) {
            map.put(e.ordinal(), e.name());
        }
        return map;
    }

    public void acquireDisputeCreationLock(String transactionId, TransactionType transactionType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType,
        com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage){
        var lockKey = DisputeCreationLockKey.builder()
            .disputeStage(disputeStage)
            .disputeType(disputeType)
            .transactionType(transactionType)
            .transactionId(transactionId)
            .build();
        try {
            disputeCreationCommand.strictSave(lockKey, lockKey.getKey());
        }
        catch (DisputeException ex) {
            if(ex.getCause() instanceof AerospikeException aerospikeCause &&
                (aerospikeCause.getResultCode() == com.aerospike.client.ResultCode.KEY_EXISTS_ERROR)) {
                    throw DisputeExceptionUtil.error(ex, StratosErrorCodeKey.DISPUTE_CREATION_LOCK,
                Map.of(Constants.MESSAGE,String.format("Dispute for this transactionId %s, "
                    + "transaction type %s is already present", transactionId, transactionType)));

            }
            throw ex;
        }
    }
    public void releaseDisputeCreationLock(
            String transactionId, TransactionType transactionType,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType disputeType,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage disputeStage){
        var lockKey = DisputeCreationLockKey.builder()
                .disputeStage(disputeStage)
                .disputeType(disputeType)
                .transactionType(transactionType)
                .transactionId(transactionId)
                .build();
        try {
            disputeCreationCommand.delete(lockKey);
        }
        catch (DisputeException ex) {
            log.error("Error while releasing dispute creation lock for key {}",
                    lockKey, ex);
        }
    }

    @Override
    public CreateDisputeResponse createDispute(
        CreateDisputeRequest createDisputeRequest, String userID) {
        acquireDisputeCreationLock(createDisputeRequest.getTransactionId(),
            createDisputeRequest.getTransactionType(),
            createDisputeRequest.getDisputeData().getDisputeType(),
            createDisputeRequest.getDisputeData().getDisputeStage());
        if (!validationService.valid(createDisputeRequest)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.VALIDATION_FAILURE);
        }
        return getDisputeCreatorAndCreateDispute(createDisputeRequest, userID);
    }

    @Override
    public void persistDisputeAndDisputeWorkflow(Dispute dispute, DisputeWorkflow disputeWorkflow){
        disputeRepository.save(dispute, storedDispute -> {
            disputeWorkflowRepository.save(disputeWorkflow);
            return storedDispute;
        });
    }
    @Override
    public DisputeWorkflowSummaries getDisputeWorkflowSummary(DisputeWorkflowFilterParams filter) {
        DetachedCriteria criteria = DetachedCriteria.forClass(Dispute.class)
                .add(Restrictions.eq(Dispute.Fields.merchantId, filter.getMerchantId()))
                .add(Restrictions.in(Dispute.Fields.disputeType,DisputeType.CHARGEBACKS))
                .addOrder(Order.desc(Dispute.Fields.createdAt));

        if(filter.getTransactionId() != null && !filter.getTransactionId().isBlank())
            criteria.add(Restrictions.eq(Dispute.Fields.transactionReferenceId, filter.getTransactionId()));

        List<Dispute> disputes = disputeRepository.scatterGather(criteria);
        //We get summaries for all queried disputes as we need to support filter on Status

        DetachedCriteria criteriaDW = DetachedCriteria.forClass(DisputeWorkflow.class)
            .add(Restrictions.in(Fields.disputeId, disputes.stream().map(Dispute::getDisputeId).toList()))
            .addOrder(Order.desc(Dispute.Fields.createdAt));

        if(filter.getStage() != null)
            criteriaDW.add(Restrictions.in(
                Fields.disputeStage,DtoUtils.disputeStageDtoToDisputeStage(filter.getStage())));
        if(filter.getState() != null)
            criteriaDW.add(Restrictions.in(
                Fields.currentState,DisputeWorkflowState.valueOf(filter.getState())));

        DateRangeParam dateRange = filter.getDateRange();
        if(dateRange!=null)
            criteria.add(Restrictions.between(Fields.createdAt,
                dateRange.getStartDate().atStartOfDay(),
                dateRange.getEndDate().atTime(LocalTime.MAX)));

        List<DisputeWorkflow> disputeSummaries = disputeWorkflowRepository.selectByWorkflowState(criteriaDW);

        List<ChargebackSummary> chargebackSummaries = disputeSummaries.stream()
            .map(TransformationUtils::toChargebackSummary)
            .toList();

        PaginationDetails paginationDetails = PaginationDetails.builder()
            .limit(filter.getLimit())
            .offset(filter.getOffset())
            .total(chargebackSummaries.size())
            .build();
        if(!chargebackSummaries.isEmpty()){
            if(filter.getOffset() >= chargebackSummaries.size())
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_PAGINATION_PARAMETERS,
                    Collections.emptyMap());
            chargebackSummaries = chargebackSummaries.subList(filter.getOffset(),
                Math.min(filter.getOffset() + filter.getLimit(),
                    chargebackSummaries.size()));
        }
        paginationDetails.setCount(disputeSummaries.size());
        return DisputeWorkflowSummaries.builder()
                .paginationDetails(paginationDetails)
                .disputes(chargebackSummaries)
                .build();
    }


    public DisputeWorkflow getDisputeWorkflow(final String disputeWorkflowId) {
        return disputeWorkflowRepository.selectByWorkflowId(disputeWorkflowId).stream().findFirst()
                .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND, Map.of(
                        Constants.MESSAGE, ERROR_MESSAGE_DISPUTE_WORKFLOW_NOT_FOUND,
                        Fields.disputeWorkflowId, disputeWorkflowId
                )));
    }

    public CreateDisputeResponse getDisputeCreatorAndCreateDispute(CreateDisputeRequest createDisputeRequest, String userID){
        DisputeCreator disputeCreator = disputeCreatorRegistry.get(
            createDisputeStateMachineRegistryKey(createDisputeRequest));
        return disputeCreator.createDispute(createDisputeRequest, userID);
    }

    DisputeStateMachineRegistryKey createDisputeStateMachineRegistryKey(
        CreateDisputeRequest createDisputeRequest) {
        DisputeType disputeType = DtoUtils.transactionTypeToDisputeType(
                createDisputeRequest.getTransactionType(),
                createDisputeRequest.getDisputeData()
                        .getDisputeType());
        return DisputeStateMachineRegistryKey.builder()
            .disputeStage(
                DtoUtils.disputeDataStageToDisputeStage(
                    createDisputeRequest.getDisputeData().getDisputeStage()))
            .disputeType(disputeType)
            .disputeWorkflowVersion(disputeWorkflowVersionMap.get(disputeType))
            .build();
    }
    @Override
    public DisputeWorkflow getDisputeWorkflowFromCommunicationId(String communicationId) {
        return disputeWorkflowRepository.scatterGatherByCommunicationId(communicationId).stream()
            .findFirst()
            .orElseThrow(
                () -> DisputeExceptionUtil.error(StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND, Map.of(
                        Constants.MESSAGE, ERROR_MESSAGE_DISPUTE_WORKFLOW_NOT_FOUND,
                        Fields.communicationId, communicationId
                    ))
            );
    }

}
