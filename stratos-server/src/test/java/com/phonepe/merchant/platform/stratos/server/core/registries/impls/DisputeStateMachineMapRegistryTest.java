package com.phonepe.merchant.platform.stratos.server.core.registries.impls;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.EdcChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.NetBankingChargebackFirstLevelStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.PgPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.statemachines.UdirComplaintStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.statemachines.UpiPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines.WalletFirstLevelChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.statemachines.WalletPreArbChargebackStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.statemachine.BBPSToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.notionalcredit.statemachine.NotionalCreditStateMachine;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.p2pm.statemachines.P2pmToaStateMachine;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.edc.EDCFirstLevelStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.fraud.SuspectedFraudStateMachine;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.pg.PGFirstLevelStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.pg.PGPreArbStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.upi.UPIFirstLevelChargebackV2;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.upi.UPIPreArbChargebackV2;
import java.net.MalformedURLException;
import java.net.URISyntaxException;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

/**
 * <AUTHOR>
 */
@Slf4j
public class DisputeStateMachineMapRegistryTest extends ErrorConfiguratorBaseTest {

    private DisputeStateMachineMapRegistry disputeStateMachineMapRegistry;
    @Mock
    private UpiFirstLevelChargebackStateMachine upiFirstLevelChargebackStateMachine;
    @Mock
    private UpiPreArbChargebackStateMachine upiPreArbChargebackStateMachine;
    @Mock
    private PgFirstLevelChargebackStateMachine pgFirstLevelChargebackStateMachine;
    @Mock
    private PgPreArbChargebackStateMachine pgPreArbChargebackStateMachine;
    @Mock
    private UdirComplaintStateMachine udirComplaintStateMachine;
    @Mock
    private P2pmToaStateMachine p2pmToaStateMachine;
    @Mock
    private EdcChargebackStateMachine edcChargebackStateMachine;
    @Mock
    private NetBankingChargebackFirstLevelStateMachine netbankingChargebackFirstLevelStateMachine;
    @Mock
    private NotionalCreditStateMachine notionalCreditStateMachine;
    @Mock
    private WalletFirstLevelChargebackStateMachine walletFirstLevelChargebackStateMachine;
    @Mock
    private WalletPreArbChargebackStateMachine walletPreArbChargebackStateMachine;
    @Mock
    private BBPSToaStateMachine bbpsToaStateMachine;
    @Mock
    private UPIFirstLevelChargebackV2 upiFirstLevelChargebackV2;
    @Mock
    private UPIPreArbChargebackV2 upiPreArbChargebackV2;
    @Mock
    private PGFirstLevelStateMachineV2 pgFirstLevelStateMachineV2;
    @Mock
    private PGPreArbStateMachineV2 pgPreArbStateMachineV2;
    @Mock
    private EDCFirstLevelStateMachineV2 edcFirstLevelStateMachineV2;
    @Mock
    private SuspectedFraudStateMachine suspectedFraudStateMachine;

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        MockitoAnnotations.initMocks(this);
        Mockito.when(upiFirstLevelChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.UPI_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(upiPreArbChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.UPI_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(pgFirstLevelChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.PG_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(pgPreArbChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.PG_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(udirComplaintStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.UDIR_INCOMING_COMPLAINT).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(p2pmToaStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.P2PM_TOA).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(edcChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.EDC_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(netbankingChargebackFirstLevelStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.NB_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(notionalCreditStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.NOTIONAL_CREDIT_TOA).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(bbpsToaStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.BBPS_TAT_BREACH_TOA).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        //add support for V2 state machines
        Mockito.when(upiFirstLevelChargebackV2.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.UPI_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(upiPreArbChargebackV2.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.UPI_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(pgFirstLevelStateMachineV2.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.PG_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(pgPreArbStateMachineV2.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.PG_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(edcFirstLevelStateMachineV2.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.EDC_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(suspectedFraudStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.FRA_FRAUD).disputeStage(DisputeStage.PRE_CHARGEBACK).disputeWorkflowVersion(DisputeWorkflowVersion.V2).build());
        Mockito.when(walletFirstLevelChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.WALLET_CHARGEBACK).disputeStage(DisputeStage.FIRST_LEVEL).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());
        Mockito.when(walletPreArbChargebackStateMachine.getRegistryKey()).thenReturn(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.WALLET_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build());

        disputeStateMachineMapRegistry =new DisputeStateMachineMapRegistry(
                upiFirstLevelChargebackStateMachine,
                upiPreArbChargebackStateMachine,
                pgFirstLevelChargebackStateMachine,
                pgPreArbChargebackStateMachine,
                udirComplaintStateMachine,
                p2pmToaStateMachine,
                edcChargebackStateMachine,
                netbankingChargebackFirstLevelStateMachine,
                notionalCreditStateMachine,
                bbpsToaStateMachine,
                upiFirstLevelChargebackV2,
                upiPreArbChargebackV2,
                pgFirstLevelStateMachineV2,
                pgPreArbStateMachineV2,
                edcFirstLevelStateMachineV2,
                suspectedFraudStateMachine,
                walletFirstLevelChargebackStateMachine,
                walletPreArbChargebackStateMachine);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testWhenRegistryIsNotFound(){
        final var stratosError = Assertions.assertThrows(DisputeException.class, () -> disputeStateMachineMapRegistry.get(DisputeStateMachineRegistryKey.builder().disputeType(DisputeType.EDC_CHARGEBACK).disputeStage(DisputeStage.PRE_ARBITRATION).disputeWorkflowVersion(DisputeWorkflowVersion.V1).build()));
        Assertions.assertEquals("Dispute state machine not found",stratosError.getMessage());
    }
}
