package com.phonepe.merchant.platform.stratos.models.disputes;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonFormat.Shape;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Dispute Id",
    "Dispute Workflow Id",
    "Dispute Workflow Version",
    "Dispute Type",
    "Dispute Stage",
    "Payment Reference Id",
    "Merchant Id",
    "Merchant Transaction Id",
    "Merchant Order Id",
    "Dispute Amount",
    "Accepted Amount",
    "Penalty Amount",
    "Current State",
    "Dispute Issuer",
    "Dispute Category",
    "Raised At"
})
public class DisputeSummaryRow {

    @JsonProperty("Dispute Id")
    public String disputeId;

    @JsonProperty("Dispute Workflow Id")
    public String disputeWorkflowId;

    @JsonProperty("Dispute Workflow Version")
    public String disputeWorkflowVersion;

    @JsonProperty("Dispute Type")
    public String disputeType;

    @JsonProperty("Dispute Stage")
    public String disputeStage;

    @JsonProperty("Payment Reference Id")
    public String transactionReferenceId;

    @JsonProperty("Merchant Id")
    public String merchantId;

    @JsonProperty("Merchant Transaction Id")
    public String merchantTransactionId;

    @JsonProperty("Dispute Amount")
    public long disputedAmount;

    @JsonProperty("Accepted Amount")
    public long acceptedAmount;

    @JsonProperty("Penalty Amount")
    public long penaltyAmount;

    @JsonProperty("Current State")
    public String currentState;

    @JsonProperty("Dispute Category")
    public String disputeCategory;

    @JsonProperty("Dispute Issuer")
    public String disputeIssuer;

    @JsonProperty("Raised At")
    @JsonFormat(shape = Shape.STRING, pattern = "yyyy-MM-dd hh:mm:ss a")
    public LocalDateTime raisedAt;
}
