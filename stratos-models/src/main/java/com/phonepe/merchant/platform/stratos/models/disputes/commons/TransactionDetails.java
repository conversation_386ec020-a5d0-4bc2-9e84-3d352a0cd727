package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class TransactionDetails {

    private String paymentState;
    private String transactionId;
    @ToString.Exclude
    private String globalTransactionId;
    private String rrn;
    @ToString.Exclude
    private String instrumentId;
    private Long transactionAmount;
    private String merchantId;
    private String merchantTransactionId;
    private Date sentTime;
}
