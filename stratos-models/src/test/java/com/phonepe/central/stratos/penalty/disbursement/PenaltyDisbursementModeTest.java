package com.phonepe.central.stratos.penalty.disbursement;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

public class PenaltyDisbursementModeTest {

    @Test
    public void testEnumValues() {
        PenaltyDisbursementMode[] expectedValues = PenaltyDisbursementMode.values();
        assertEquals(2, expectedValues.length);
        assertEquals(PenaltyDisbursementMode.REFUND, expectedValues[0]);
        assertEquals(PenaltyDisbursementMode.ToA, expectedValues[1]);
    }

    @Test
    public void testValueOf() {
        assertEquals(PenaltyDisbursementMode.REFUND, PenaltyDisbursementMode.valueOf("REFUND"));
        assertEquals(PenaltyDisbursementMode.ToA, PenaltyDisbursementMode.valueOf("ToA"));
    }
}
