package com.phonepe.central.stratos.penalty.response;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyStateCheckResponse {

    @NotBlank
    private String transactionId;

    @NotNull
    private Boolean isResolved;

    private String message;

}
