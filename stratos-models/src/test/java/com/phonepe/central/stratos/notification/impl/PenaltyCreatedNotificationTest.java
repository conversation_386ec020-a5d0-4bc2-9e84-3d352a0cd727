package com.phonepe.central.stratos.notification.impl;

import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.notification.NotificationType;
import com.phonepe.central.stratos.penalty.Penalty;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PenaltyCreatedNotificationTest {

    @Mock
    private Penalty penalty;

    @Mock
    private ChangeNotificationVisitor<String> visitor;

    @InjectMocks
    private PenaltyCreatedNotification penaltyCreatedNotification;

    @BeforeEach
    public void setUp(){
        MockitoAnnotations.openMocks(this);
        penaltyCreatedNotification=new PenaltyCreatedNotification(penalty);
    }

    @Test
    public void testConstructorAndGetter() {
        assertEquals(penalty, penaltyCreatedNotification.getPenalty());
        assertEquals(NotificationType.PENALTY_CREATED, penaltyCreatedNotification.getType());
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(penaltyCreatedNotification)).thenReturn("Visited");

        assertEquals("Visited", penaltyCreatedNotification.accept(visitor));
        verify(visitor).visit(penaltyCreatedNotification);
    }

}
