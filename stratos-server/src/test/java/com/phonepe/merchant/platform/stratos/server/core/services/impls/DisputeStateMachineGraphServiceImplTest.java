package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Provider;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.utils.ResourceUtils;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import org.springframework.statemachine.state.State;
import org.springframework.statemachine.transition.Transition;
import org.springframework.statemachine.trigger.Trigger;

@ExtendWith(MockitoExtension.class)
class DisputeStateMachineGraphServiceImplTest {

    private static final DisputeStateMachineRegistryKey REGISTRY_KEY = DisputeStateMachineRegistryKey
        .builder()
        .disputeType(DisputeType.UPI_CHARGEBACK)
        .disputeStage(DisputeStage.FIRST_LEVEL)
        .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
        .build();

    @Mock
    private DisputeStateMachineRegistry disputeStateMachineRegistry;

    @Mock
    private State<DisputeWorkflowState, DisputeWorkflowEvent> sourceState;

    @Mock
    private State<DisputeWorkflowState, DisputeWorkflowEvent> targetState;

    @Mock
    private Trigger<DisputeWorkflowState, DisputeWorkflowEvent> trigger;

    @Mock
    private Transition<DisputeWorkflowState, DisputeWorkflowEvent> transition;

    @Mock
    private StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> stateMachine;

    @Mock
    private Provider<DisputeStateMachineRegistry> disputeStateMachineRegistryProvider;

    @InjectMocks
    private DisputeStateMachineGraphServiceImpl disputeStateMachineGraphService;

    @Test
    void testWhenGenerateGraphIsInvokedItReturnsSvgString() {

        Mockito.when(sourceState.getId()).thenReturn(DisputeWorkflowState.RECEIVED);
        Mockito.when(transition.getSource()).thenReturn(sourceState);
        Mockito.when(targetState.getId()).thenReturn(DisputeWorkflowState.REFUND_BLOCKED);
        Mockito.when(transition.getTarget()).thenReturn(targetState);
        Mockito.when(trigger.getEvent()).thenReturn(DisputeWorkflowEvent.BLOCK_REFUND);
        Mockito.when(transition.getTrigger()).thenReturn(trigger);
        Mockito.when(stateMachine.getTransitions()).thenReturn(Set.of(transition));
        Mockito.when(disputeStateMachineRegistry.get(REGISTRY_KEY))
            .thenReturn(new TestDisputeStateMachine(stateMachine));
        Mockito.when(disputeStateMachineRegistryProvider.get())
            .thenReturn(disputeStateMachineRegistry);

        final var resultSvgGraph = disputeStateMachineGraphService.getSvgGraph(REGISTRY_KEY);

        final var expectedSvgGraph = ResourceUtils.getResourceString(
            "fixtures/core/services/graphService/svgGraphForSingleTransition.svg");

        Assertions.assertEquals(expectedSvgGraph, resultSvgGraph);
    }

    static class TestDisputeStateMachine extends DisputeStateMachine {

        private final StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> stateMachine;

        TestDisputeStateMachine(
            final StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> stateMachine) {
            super(null, null);
            this.stateMachine = stateMachine;
        }

        @Override
        public DisputeStateMachineRegistryKey getRegistryKey() {
            return REGISTRY_KEY;
        }

        @Override
        public StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> internalApiGetStateMachine() {
            return stateMachine;
        }

        @Override
        protected void configure(
            final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {

        }

        @Override
        protected void configure(
            final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {

        }

        @Override
        protected void configure(
            final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {

        }
    }
}