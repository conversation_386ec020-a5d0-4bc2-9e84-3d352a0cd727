package com.phonepe.merchant.platform.stratos.models.files.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Row Id",
    "Row State",
    "Code",
    "CB Received Date",
    "Source Of CB",
    "Amount",
    "Transaction ID",
})

public class PgPreArbFileHistoryProcessingSummaryRow {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;

    @JsonProperty("Source Of CB")
    private String source;

    @JsonProperty("Amount")
    private String bankDisputedAmount;

    @JsonProperty("Transaction ID")
    private String transactionId;

}
