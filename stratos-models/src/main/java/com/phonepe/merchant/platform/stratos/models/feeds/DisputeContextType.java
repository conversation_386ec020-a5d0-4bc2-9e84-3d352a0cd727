package com.phonepe.merchant.platform.stratos.models.feeds;

import lombok.experimental.UtilityClass;

public enum DisputeContextType {
    CHARGEBACK,
    CHARGE<PERSON>CK_PENALTY,
    CHAR<PERSON><PERSON><PERSON><PERSON>_REVERSAL;

    @UtilityClass
    public static final class Names {

        public static final String CHARGEBACK = "CHAR<PERSON><PERSON><PERSON>K";
        public static final String CHARGEBACK_PENALTY = "CHAR<PERSON><PERSON><PERSON>K_PENALTY";
        public static final String CHARGEBACK_REVERSAL = "CHARGEBACK_REVERSAL";
    }
}
