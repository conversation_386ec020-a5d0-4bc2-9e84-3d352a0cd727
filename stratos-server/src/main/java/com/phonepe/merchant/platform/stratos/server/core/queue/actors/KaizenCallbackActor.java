package com.phonepe.merchant.platform.stratos.server.core.queue.actors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.EmptyTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.kaizen.KaizenKey;
import com.phonepe.merchant.platform.stratos.models.kaizen.KaizenKeyValue;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.KaizenService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.verified.kaizen.models.responses.WorkflowClientCallback;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

import javax.inject.Inject;
import javax.inject.Singleton;
import java.util.*;


@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class KaizenCallbackActor extends Actor<ActionType, WorkflowClientCallback> {

    private final DisputeService disputeService;
    private final KaizenService kaizenService;

    @Inject
    protected KaizenCallbackActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            ConnectionRegistry connectionRegistry,
            ObjectMapper mapper,
            RetryStrategyFactory retryStrategyFactory,
            ExceptionHandlingFactory exceptionHandlingFactory,
            DisputeService disputeService,
            KaizenService kaizenService) {
        super(ActionType.KAIZEN_CALLBACK, actorConfigMap.get(ActionType.KAIZEN_CALLBACK), connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory, WorkflowClientCallback.class, Set.of(JsonProcessingException.class));
        this.disputeService = disputeService;
        this.kaizenService = kaizenService;
    }

    @Override
    protected boolean handle(
            final WorkflowClientCallback workflowClientCallback,
            final MessageMetadata messageMetadata) {

        final var disputeWorkflow = getDisputeWorkflow(workflowClientCallback);
        final var kaizenWorkflowId = disputeWorkflow.getCommunicationId();
        final var disputeType = disputeWorkflow.getDisputeType();
        final var disputeWorkflowId = disputeWorkflow.getDisputeWorkflowId();
        final var transactionReferenceId = disputeWorkflow.getTransactionReferenceId();

        log.info("Receive Kaizen callback for DisputeWorkflowId {}, KaizenWorkflowId {} with state {}",disputeWorkflow.getDisputeWorkflowId(), kaizenWorkflowId, workflowClientCallback.getWorkflowState());  //TODO: remove log

        switch (workflowClientCallback.getWorkflowState()) {
            case SUCCESS -> {
                if(verifyKaizenResponse(kaizenWorkflowId, KaizenKey.MERCHANT_ACTION, KaizenKeyValue.ACCEPT)){
                    if(verifyKaizenResponse(kaizenWorkflowId, KaizenKey.MERCHANT_ACCEPT_ACTION, KaizenKeyValue.SUBMIT)){
                        triggerEvent(transactionReferenceId,disputeWorkflowId,
                                DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK);
                    }
                    return true;
                }
                if(verifyKaizenResponse(kaizenWorkflowId, KaizenKey.MERCHANT_ACTION, KaizenKeyValue.CONTEST)){
                    if (disputeType.equals(DisputeType.EDC_CHARGEBACK) ||
                        disputeType.equals(DisputeType.FRA_FRAUD)) {
                        triggerEvent(
                                transactionReferenceId, disputeWorkflowId,
                                DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS);
                        return true;
                    }
                    var contestedAmount = Long.parseLong(kaizenService.getValueForKey(kaizenWorkflowId, KaizenKey.DISPUTE_PROOF_UPLOAD_AMOUNT));
                    if (contestedAmount * 100 >= disputeWorkflow.getDisputedAmount())
                    {
                        triggerEvent(transactionReferenceId, disputeWorkflowId,
                                DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS);
                    }
                    else
                    {
                        triggerPartialContestEvent( transactionReferenceId,
                                disputeWorkflowId,
                                contestedAmount);
                    }
                }
            }
            case FAILURE, SKIPPED -> {
                log.error("Kaizen workflow with workflow id {} for disputeWorkflow id {} failed/skipped", kaizenWorkflowId, disputeWorkflow.getDisputeWorkflowId());
                kaizenService.updateCommunicationId(disputeWorkflow.getDisputeWorkflowId(), disputeWorkflow
                    .getTransactionReferenceId(), "");
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.KAIZEN_CALLBACK_ERROR,
                    Map.of(Constants.MESSAGE,
                        String.format("%s Kaizen workflow failed/skipped",
                            StratosErrorCodeKey.KAIZEN_CALLBACK_ERROR.name())));
            }
        }
        return true;
    }

    private void triggerPartialContestEvent(
            String transactionReferenceId,
            String disputeWorkflowId,
            long contestedAmount) {
        disputeService
            .triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                transactionReferenceId,
                disputeWorkflowId,
                DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS,
                PartialAcceptanceTransitionContext.builder()
                    .acceptedAmount(contestedAmount)
                    .build());
    }

    private void triggerEvent(
            String transactionReferenceId,
            String disputeWorkflowId,
            DisputeWorkflowEvent disputeWorkflowEvent) {
        disputeService
            .triggerEvent(Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                transactionReferenceId,
                disputeWorkflowId,
                disputeWorkflowEvent,
                EmptyTransitionContext.builder().build());
    }

    private boolean verifyKaizenResponse(
            String kaizenWorkflowId,
            KaizenKey kaizenKey,
            KaizenKeyValue kaizenKeyValue) {
        return Objects.equals(kaizenService.getValueForKey(kaizenWorkflowId, kaizenKey),
                kaizenKeyValue.name());
    }

    private DisputeWorkflow getDisputeWorkflow(WorkflowClientCallback workflowClientCallback) {
        if (workflowClientCallback.getWorkflowId() != null) {
            return disputeService.getDisputeWorkflowFromCommunicationId(
                    workflowClientCallback.getWorkflowId());
        } else {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.KAIZEN_CALLBACK_ERROR,
                Map.of(Constants.MESSAGE,
                    String.format("%s Workflow id should not be null",
                    StratosErrorCodeKey.KAIZEN_CALLBACK_ERROR.name())));
        }
    }
}
