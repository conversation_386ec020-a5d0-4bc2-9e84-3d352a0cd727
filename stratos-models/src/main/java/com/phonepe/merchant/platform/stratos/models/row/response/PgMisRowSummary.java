package com.phonepe.merchant.platform.stratos.models.row.response;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Pg Transaction Id",
    "Source Id",
    "Row Id",
    "Row State",
    "Transaction Type",
    "Net Amount",
    "Interchange"
})
public class PgMisRowSummary {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Source Id")
    private String sourceId;

    @JsonProperty("Transaction Id")
    private String transactionId;

    @JsonProperty("Transaction Type")
    private String transactionType;

    @JsonProperty("Net Amount")
    private String netAmount;

    @JsonProperty("Interchange")
    private String interchange;
}
