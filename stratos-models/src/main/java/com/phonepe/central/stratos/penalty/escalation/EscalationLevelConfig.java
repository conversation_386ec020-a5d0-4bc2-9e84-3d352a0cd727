package com.phonepe.central.stratos.penalty.escalation;

import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EscalationLevelConfig {

    @NotNull
    private EscalationLevel level;

    @NotEmpty
    private Set<String> emailIds;
}
