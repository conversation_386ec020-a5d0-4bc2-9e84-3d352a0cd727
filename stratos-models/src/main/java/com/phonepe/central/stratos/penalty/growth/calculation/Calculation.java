package com.phonepe.central.stratos.penalty.growth.calculation;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.PenaltyConversionDateFixedPercentageCalculation;
import javax.validation.constraints.NotBlank;

import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedAmountCalculation;
import com.phonepe.central.stratos.penalty.growth.calculation.impl.TransactionDateFixedPercentageCalculation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(name = CalculationType.PENALTY_CONVERSION_DATE_FIXED_AMOUNT_TEXT,
                value = PenaltyConversionDateFixedAmountCalculation.class),
        @JsonSubTypes.Type(name = CalculationType.PENALTY_CONVERSION_DATE_FIXED_PERCENTAGE_TEXT,
                value = PenaltyConversionDateFixedPercentageCalculation.class),
        @JsonSubTypes.Type(name = CalculationType.TRANSACTION_DATE_FIXED_AMOUNT_TEXT,
                value = TransactionDateFixedAmountCalculation.class),
        @JsonSubTypes.Type(name = CalculationType.TRANSACTION_DATE_FIXED_PERCENTAGE_TEXT,
                value = TransactionDateFixedPercentageCalculation.class),
})
public abstract class Calculation {

    private final CalculationType type;
    @NotBlank
    private String amountPath;
    private boolean isDefault;

    public abstract <T> T accept(CalculationVisitor<T> visitor);

    public interface CalculationVisitor<T> {
        T visit(PenaltyConversionDateFixedPercentageCalculation calculation);
        T visit(PenaltyConversionDateFixedAmountCalculation calculation);
        T visit(TransactionDateFixedPercentageCalculation calculation);
        T visit(TransactionDateFixedAmountCalculation calculation);
    }
}
