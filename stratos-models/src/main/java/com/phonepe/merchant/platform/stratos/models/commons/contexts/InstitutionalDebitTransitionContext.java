package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.SourceType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class InstitutionalDebitTransitionContext extends TransitionContext {

    @NotNull
    private SourceType debitSourceType;

    private String debitSourceId;

    @Min(1)
    private long debitAmount;

    public InstitutionalDebitTransitionContext() {
        super(ContextType.INSTITUTIONAL_DEBIT_CONTEXT);
    }

    @Builder
    public InstitutionalDebitTransitionContext(
        final SourceType debitSourceType,
        final String debitSourceId,
        final long debitAmount) {
        this();
        this.debitSourceType = debitSourceType;
        this.debitSourceId = debitSourceId;
        this.debitAmount = debitAmount;
    }
}
