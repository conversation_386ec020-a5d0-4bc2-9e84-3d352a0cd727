package com.phonepe.merchant.platform.stratos.models.feeds;

import static com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType.Names.CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType.Names.CHARGEBACK_PENALTY;
import static com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType.Names.CHARGEBACK_REVERSAL;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackDisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackPenaltyDisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.contexts.ChargebackReversalDisputeContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "contextType", visible = true)
@JsonSubTypes(value = {
    @Type(value = ChargebackDisputeContext.class, name = CHARGEBACK),
    @Type(value = ChargebackPenaltyDisputeContext.class, name = CHARGEBACK_PENALTY),
    @Type(value = ChargebackReversalDisputeContext.class, name = CHARGEBACK_REVERSAL)
})
public class DisputeContext {

    private DisputeContextType contextType;
}
