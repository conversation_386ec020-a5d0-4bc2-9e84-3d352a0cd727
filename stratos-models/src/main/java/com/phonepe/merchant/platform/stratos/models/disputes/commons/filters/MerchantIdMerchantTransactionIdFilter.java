package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import javax.validation.constraints.NotEmpty;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class MerchantIdMerchantTransactionIdFilter extends DisputeFilter {

    @NotEmpty(message = "Merchant id is mandatory field")
    private String merchantId;

    @NotEmpty(message = "Merchant transaction id is mandatory field")
    private String merchantTransactionId;

    public MerchantIdMerchantTransactionIdFilter() {
        super(DisputeFilterType.MERCHANT_TRANSACTION_ID);
    }

    @Builder
    public MerchantIdMerchantTransactionIdFilter(
        final String merchantId,
        final String merchantTransactionId) {
        this();
        this.merchantId = merchantId;
        this.merchantTransactionId = merchantTransactionId;
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
