package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import javax.validation.constraints.NotEmpty;

@Data
@EqualsAndHashCode(callSuper = true)
public class FileNameDisputeFilter extends DisputeFilter {

    @NotEmpty
    private String fileName;

    public FileNameDisputeFilter() {
        super(DisputeFilterType.FILE_NAME);
    }

    @Builder
    public FileNameDisputeFilter(final String fileName) {
        this();
        this.fileName = fileName;
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
