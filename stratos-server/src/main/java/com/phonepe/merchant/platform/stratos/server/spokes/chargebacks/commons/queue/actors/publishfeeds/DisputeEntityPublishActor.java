package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.FeedUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FraUtils;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Set;
import javax.inject.Singleton;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class DisputeEntityPublishActor extends Actor<ActionType, DisputeWorkflowMessage> {

    private final PaymentsService paymentsService;

    private final DisputeService disputeService;

    private final TstoreService tstoreService;

    @Inject
    @SuppressWarnings("java:S107")
    public DisputeEntityPublishActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory,
            final PaymentsService paymentsService,
            final DisputeService disputeService,
            final TstoreService tstoreService) {

        super(ActionType.DISPUTE_ENTITY_FEED_PUBLISHER,
                actorConfigMap.get(ActionType.DISPUTE_ENTITY_FEED_PUBLISHER),
                connectionRegistry, mapper, retryStrategyFactory,
                exceptionHandlingFactory, DisputeWorkflowMessage.class,
                Set.of(JsonProcessingException.class));
        this.paymentsService = paymentsService;
        this.disputeService = disputeService;
        this.tstoreService = tstoreService;
    }

    @Override
    protected boolean handle(
            final DisputeWorkflowMessage disputeWorkflowMessage,
            final MessageMetadata messageMetadata) {

        final var transactionReferenceId = disputeWorkflowMessage.getTransactionReferenceId();
        final var disputeWorkflowId = disputeWorkflowMessage.getDisputeWorkflowId();

        try{
            final var disputeWorkflow = disputeService.validateAndGetDisputeWorkflow(transactionReferenceId,
                    disputeWorkflowId);

            final var globalPaymentId = paymentsService.getGlobalPaymentId(transactionReferenceId);
            log.info("Publishing dispute entity feed for disputeWorkflowId: {}, transactionReferenceId: {}, globalPaymentId: {}",
                    disputeWorkflowId, transactionReferenceId, globalPaymentId);
            tstoreService.createMerchantFeed(disputeWorkflowId, FeedUtils.toDisputeEntityFeed(disputeWorkflow),
                    disputeWorkflow.getDispute()
                            .getMerchantId(), globalPaymentId, FraUtils.toEpochMilli(disputeWorkflow.getCreatedAt()),
                    FraUtils.toEpochMilli(disputeWorkflow.getUpdatedAt()));
        }
        catch (Exception e){
            log.error("Exception in publishing dispute entity feed for disputeWorkflowId: {}, transactionReferenceId: {}",
                    disputeWorkflowId, transactionReferenceId, e);
            throw e;
        }
        return true;
    }
}
