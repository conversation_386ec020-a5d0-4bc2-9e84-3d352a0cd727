package com.phonepe.central.stratos.penalty.request.penaltyclass;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PenaltyClassUpdateType {
    BASIC_UPDATE(PenaltyClassUpdateType.BASIC_UPDATE_TEXT),
    CRITERIA_UPDATE(PenaltyClassUpdateType.CRITERIA_UPDATE_TEXT);
    public static final String BASIC_UPDATE_TEXT = "BASIC_UPDATE";
    public static final String CRITERIA_UPDATE_TEXT = "CRITERIA_UPDATE";

    @Getter
    private final String value;
}
