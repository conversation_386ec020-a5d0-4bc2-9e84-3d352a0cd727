package com.phonepe.merchant.platform.stratos.models.udir.response;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.disputes.UdirComplaintStateDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CheckStatusResponse;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UdirOutgoingComplaintStatusResponse extends CheckStatusResponse {

    private String complaintId;
    private String crn;
    private String adjRemark;
    private String adjReason;
    private UdirComplaintStateDto complaintState;
    private String adjFlag;
    private String adjCode;
    private String errorCode;
    private String stratosComplaintId;

    public UdirOutgoingComplaintStatusResponse() {
        super(DisputeTypeDto.UDIR_OUTGOING_COMPLAINT);
    }

    @SuppressWarnings("java:S107")
    @Builder
    public UdirOutgoingComplaintStatusResponse(
        String complaintId, String crn, String adjRemark, String adjReason,
        UdirComplaintStateDto complaintState, String adjFlag, String adjCode,
        DisputeStageDto disputeStageDto, String transactionId, String errorCode,
        String stratosComplaintId) {

        super(DisputeTypeDto.UDIR_OUTGOING_COMPLAINT, disputeStageDto,
            transactionId);
        this.complaintId = complaintId;
        this.crn = crn;
        this.adjRemark = adjRemark;
        this.adjReason = adjReason;
        this.complaintState = complaintState;
        this.adjFlag = adjFlag;
        this.adjCode = adjCode;
        this.errorCode = errorCode;
        this.stratosComplaintId = stratosComplaintId;
    }
}
