package com.phonepe.merchant.platform.stratos.models.accountingevents;

public enum AccountingTransactionType {
    UPI_CHARGEBACK_RECOVERY,
    UPI_CHARGEBACK_RECOVERY_REVERSAL,
    UPI_CHARGEBACK_PENALTY_RECOVERY,
    PG_CHARGEBACK_RECOVERY,
    PG_<PERSON>ARGEBACK_RECOVERY_REVERSAL,
    PG_CHARGEBACK_PENALTY_RECOVERY,
    NB_CHARGEBACK_RECOVERY,
    NB_CHARGEBACK_RECOVERY_REVERSAL,
    MERCHANT_PENALTY_RECOVERY,
    PENALTY_RECOVERY,
    AMOUNT_ON_HOLD,
    AMOUNT_ON_HOLD_REVERSAL,
    FRA_AMOUNT_ON_HOLD,
    FRA_AMOUNT_ON_HOLD_REVERSAL;

}
