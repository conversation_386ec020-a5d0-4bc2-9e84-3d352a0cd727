package com.phonepe.merchant.platform.stratos.models.row;

public enum RowTransactionType {
    CHARGEBACK {
        @Override
        public <T> T accept(final RowTransactionTypeVisitor<T> visitor) {
            return visitor.visitChargeback();
        }
    },
    CHARGEBACK_REVERSED {
        @Override
        public <T> T accept(final RowTransactionTypeVisitor<T> visitor) {
            return visitor.visitChargebackReversed();
        }
    };

    public abstract <T> T accept(RowTransactionTypeVisitor<T> visitor);

}
