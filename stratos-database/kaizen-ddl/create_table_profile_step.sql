CREATE TABLE `profile_step` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `profile_id` varchar(64) NOT NULL,
  `profile_step_id` varchar(64) NOT NULL,
  `profile_step_mapping_id` varchar(128) NOT NULL,
  `title` varchar(128) NOT NULL,
  `execution_rule` text NOT NULL,
  `screen_config` mediumtext NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_idx_profile_step_id` (`profile_step_id`),
  KEY `profile_id_idx` (`profile_id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4;