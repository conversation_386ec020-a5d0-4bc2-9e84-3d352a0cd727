CREATE TABLE `profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `profile_id` varchar(64) NOT NULL,
  `organization` varchar(128) NOT NULL,
  `namespace` varchar(128) NOT NULL,
  `type` varchar(128) NOT NULL,
  `version` varchar(32) NOT NULL,
  `workflow_tag_config` text DEFAULT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  `get_template_config` text DEFAULT NULL,
  `summary_view_config` text DEFAULT NULL,
  `profile_type` tinyint(3) unsigned DEFAULT 0,
  `upgrade_type` text DEFAULT NULL,
  `post_completion_action_config` text DEFAULT NULL,
  `rule` text DEFAULT NULL,
  `priority` tinyint(3) unsigned DEFAULT NULL,
  `spec_id` varchar(128) DEFAULT NULL,
  `approved_by` varchar(32) DEFAULT NULL,
  `post_workflow_creation_action_config` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_idx_profile_id` (`profile_id`),
  KEY `profile_key_idx` (`organization`,`namespace`,`type`,`version`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4;