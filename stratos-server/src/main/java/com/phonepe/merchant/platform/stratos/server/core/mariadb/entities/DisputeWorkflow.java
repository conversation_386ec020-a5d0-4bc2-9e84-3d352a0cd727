package com.phonepe.merchant.platform.stratos.server.core.mariadb.entities;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.SourceType;
import com.phonepe.merchant.platform.stratos.server.core.models.UserType;
import java.io.Serializable;
import java.time.LocalDateTime;
import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.ForeignKey;
import javax.persistence.Id;
import javax.persistence.Index;
import javax.persistence.JoinColumn;
import javax.persistence.JoinColumns;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.DiscriminatorFormula;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.envers.AuditTable;
import org.hibernate.envers.Audited;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
@AuditTable(value = "dispute_workflow_audit")
@DiscriminatorFormula("case when dispute_type in (0,1,4,5,6,7,8,9,10) then 0 when dispute_type in (2,3) then 1 end")
@Table(name = "dispute_workflow", indexes = {
    @Index(name = "idx_unq_dispute_workflow_id", columnList = "dispute_workflow_id, partition_id", unique = true),
    @Index(name = "idx_created_at_dispute_type_state", columnList = "created_at, dispute_type, current_state"),
    @Index(name = "idx_communication_id", columnList = "communication_id"),
    @Index(name = "idx_dispute_source_type_source_id", columnList = "dispute_source_type, dispute_source_id"),
    @Index(name = "idx_no_response_identification", columnList = "respond_by, dispute_type, dispute_stage, current_state, dispute_workflow_version"),
    @Index(name = "idx_txn_dispute_type_stage", columnList = "transaction_reference_id, dispute_type, dispute_stage")
})
public abstract class DisputeWorkflow implements Sharded, Serializable {

    private static final long serialVersionUID = -3661614849102800863L;

    @Id
    @EmbeddedId
    private PrimaryKey key;

    @NotEmpty
    @Column(name = "dispute_id", columnDefinition = "varchar(32)", nullable = false)
    private String disputeId;

    @NotEmpty
    @Column(name = "dispute_workflow_id", columnDefinition = "varchar(32)", nullable = false)
    private String disputeWorkflowId;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "dispute_workflow_version", columnDefinition = "int", nullable = false)
    private DisputeWorkflowVersion disputeWorkflowVersion;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "dispute_source_type", columnDefinition = "int", nullable = false)
    private SourceType disputeSourceType;

    @NotEmpty
    @Column(name = "dispute_source_id", columnDefinition = "varchar(32)", nullable = false)
    private String disputeSourceId;

    @NotEmpty
    @Column(name = "transaction_reference_id", columnDefinition = "varchar(32)", nullable = false)
    private String transactionReferenceId;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "dispute_type", columnDefinition = "int", nullable = false)
    private DisputeType disputeType;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "dispute_stage", columnDefinition = "int", nullable = false)
    private DisputeStage disputeStage;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "current_event", columnDefinition = "int", nullable = false)
    private DisputeWorkflowEvent currentEvent;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "current_state", columnDefinition = "int", nullable = false)
    private DisputeWorkflowState currentState;

    @Column(name = "disputed_amount", columnDefinition = "int", nullable = false)
    private long disputedAmount;

    @NotNull
    @Column(name = "raised_at", columnDefinition = "datetime", nullable = false)
    private LocalDateTime raisedAt;

    @NotNull
    @Column(name = "respond_by", columnDefinition = "datetime", nullable = false)
    private LocalDateTime respondBy;

    @NotEmpty
    @Column(name = "gandalf_user_id", columnDefinition = "varchar(32)", nullable = false)
    private String gandalfUserId;

    @NotNull
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "user_type", columnDefinition = "int", nullable = false)
    private UserType userType;

    @CreationTimestamp
    @Column(name = "created_at", columnDefinition = "datetime DEFAULT current_timestamp", updatable = false, nullable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", columnDefinition = "datetime DEFAULT current_timestamp ON UPDATE current_timestamp", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "communication_id", columnDefinition = "varchar(32)")
    private String communicationId;

    @ToString.Exclude
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumns(value = {
        @JoinColumn(name = "transaction_reference_id", referencedColumnName = "transaction_reference_id", insertable = false, updatable = false),
        @JoinColumn(name = "dispute_type", referencedColumnName = "dispute_type", insertable = false, updatable = false),
        @JoinColumn(name = "dispute_id", referencedColumnName = "dispute_id", insertable = false, updatable = false),
    }, foreignKey = @ForeignKey(ConstraintMode.NO_CONSTRAINT))
    private Dispute dispute;

    @Override
    public String getShardingKey() {
        return transactionReferenceId;
    }

    public abstract <T> T accept(DisputeWorkflowVisitor<T> visitor);
}
