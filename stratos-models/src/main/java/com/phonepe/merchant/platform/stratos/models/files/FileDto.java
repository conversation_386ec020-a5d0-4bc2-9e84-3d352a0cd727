package com.phonepe.merchant.platform.stratos.models.files;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDto {

    private String fileId;
    private String fileName;
    private DisputeTypeDto disputeType;
    private FileTypeDto fileType;
    private FileStateDto fileState;
    private LocalDateTime createdAt;
}
