package com.phonepe.merchant.platform.stratos.models.commons.disputedata;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.visitors.DisputeCategoryVisitor;

public enum DisputeCategory {
    FRAUD {
        @Override
        public <T> T accept(DisputeCategoryVisitor<T> visitor) {
            return visitor.visitFraud();
        }
    },
    SERVICE {
        @Override
        public <T> T accept(DisputeCategoryVisitor<T> visitor) {
            return visitor.visitService();
        }
    };

    public abstract <T> T accept(final DisputeCategoryVisitor<T> visitor);

}
