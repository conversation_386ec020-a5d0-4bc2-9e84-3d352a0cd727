package com.phonepe.central.stratos.penalty.growth.calculation.impl;

import com.phonepe.central.stratos.penalty.growth.calculation.Calculation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.math.BigDecimal;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

public class PenaltyConversionDateFixedPercentageCalculationTest {
    private final BigDecimal percentage = BigDecimal.valueOf(20.5);
    private final BigDecimal maxAmount = BigDecimal.valueOf(500.00);
    private final String amountPath = "abc/pqr/xyz";
    private final boolean isDefault = false;

    private PenaltyConversionDateFixedPercentageCalculation fixedPercentageCalculation;
    @Mock
    private Calculation.CalculationVisitor<String> visitor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        fixedPercentageCalculation = PenaltyConversionDateFixedPercentageCalculation.builder()
                .amountPath(amountPath)
                .percentage(percentage)
                .maxAmount(maxAmount)
                .isDefault(isDefault)
                .build();
    }

    @Test
    public void testConstructor() {
        assertNotNull(fixedPercentageCalculation);
        assertEquals(percentage, fixedPercentageCalculation.getPercentage());
        assertEquals(maxAmount,fixedPercentageCalculation.getMaxAmount());
        assertEquals(amountPath,fixedPercentageCalculation.getAmountPath());
        assertEquals(isDefault,fixedPercentageCalculation.isDefault());
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(fixedPercentageCalculation)).thenReturn("Visited");
        String result = fixedPercentageCalculation.accept(visitor);
        assertEquals("Visited", result);
        verify(visitor, times(1)).visit(fixedPercentageCalculation);
    }
}
