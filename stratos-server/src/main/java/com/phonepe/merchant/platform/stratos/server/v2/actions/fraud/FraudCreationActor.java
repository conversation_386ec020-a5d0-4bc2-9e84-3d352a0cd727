package com.phonepe.merchant.platform.stratos.server.v2.actions.fraud;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.Actor;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import java.util.Map;
import java.util.Set;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@EqualsAndHashCode(callSuper = true)
public class FraudCreationActor extends Actor<ActionType, DisputeWorkflowMessage> {

    private final PaymentsService paymentsService;

    private final DisputeService disputeService;

    @Inject
    @SuppressWarnings("java:S107")
    public FraudCreationActor(
            final Map<ActionType, ActorConfig> actorConfigMap,
            final ConnectionRegistry connectionRegistry,
            final ObjectMapper mapper,
            final RetryStrategyFactory retryStrategyFactory,
            final ExceptionHandlingFactory exceptionHandlingFactory,
            final PaymentsService paymentsService,
            final DisputeService disputeService) {
        super(ActionType.FRA_PRE_CHARGEBACK_CREATION_HANDLER,
                actorConfigMap.get(ActionType.FRA_PRE_CHARGEBACK_CREATION_HANDLER),
                connectionRegistry, mapper, retryStrategyFactory,
                exceptionHandlingFactory, DisputeWorkflowMessage.class,
                Set.of(JsonProcessingException.class));
        this.paymentsService = paymentsService;
        this.disputeService = disputeService;
    }

    @Override
    protected boolean handle(
            final DisputeWorkflowMessage disputeWorkflowMessage,
            final MessageMetadata messageMetadata) {

        final var transactionReferenceId = disputeWorkflowMessage.getTransactionReferenceId();
        final var disputeWorkflowId = disputeWorkflowMessage.getDisputeWorkflowId();

        final var disputeWorkflow = disputeService
                .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);

        if (paymentsService.isFullyReversed(transactionReferenceId)) {
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                transactionReferenceId,
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.REQUEST_REPRESENTMENT,
                Constants.EMPTY_TRANSITION_CONTEXT);

        } else {
            disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                transactionReferenceId,
                disputeWorkflow.getDisputeWorkflowId(),
                DisputeWorkflowEvent.BLOCK_REFUND,
                Constants.EMPTY_TRANSITION_CONTEXT);
        }
        return true;
    }
}