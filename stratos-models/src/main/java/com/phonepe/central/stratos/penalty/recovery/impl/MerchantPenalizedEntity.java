package com.phonepe.central.stratos.penalty.recovery.impl;

import com.phonepe.central.stratos.penalty.recovery.PenalizedEntity;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntityType;
import com.phonepe.central.stratos.penalty.recovery.PenalizedEntityVisitor;
import javax.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MerchantPenalizedEntity extends PenalizedEntity {
    @NotBlank
    private String merchantId;
    @NotBlank
    private String mcc;

    public MerchantPenalizedEntity() {
        super(PenalizedEntityType.MERCHANT_PENALIZED_ENTITY);
    }
    
    @Builder
    public MerchantPenalizedEntity(String merchantId,
                                   String mcc) {
        super(PenalizedEntityType.MERCHANT_PENALIZED_ENTITY);
        this.merchantId = merchantId;
        this.mcc = mcc;
    }

    @Override
    public <T> T accept(PenalizedEntityVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
