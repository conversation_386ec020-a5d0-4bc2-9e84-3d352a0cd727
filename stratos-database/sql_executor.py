import os
import sys

# Sample Usage
    # python3 sql_executor.py stg-dbvipstage02.phonepe.nb6 ppdb phonepe stratos_shard_ 1 2 file_create_table.sql

if len(sys.argv) < 8:
    print('Usage: sql_executor <host> <username> <password> <database> <shard_start> <shard_end> <sql_file>')

host = sys.argv[1]
username = sys.argv[2]
password = sys.argv[3]
database = sys.argv[4]
shard_start = sys.argv[5]
shard_end = sys.argv[6]
sql_file = sys.argv[7]

for i in range(int(shard_start), int(shard_end) + 1):
    os.system('mysql --user=' + username + ' --password=' + password + ' --host=' + host + ' --database=' + database + str(i) + ' < ' + sql_file)
