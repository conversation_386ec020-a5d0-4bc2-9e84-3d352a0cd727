package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeSummary {

    @NotBlank
    private String disputeId;
    @NotBlank
    private DisputeStatus disputeStatus;
    private DisputeData disputeData;
    private TransactionType transactionType;
    private String transactionId;
    private String merchantTransactionId;
    private long transactionAmount;
    private long latestDisputedAmount;
    private long latestAcceptedAmount;
    private LocalDateTime respondBy;
    private LocalDateTime createdOn;
    private LocalDateTime updatedOn;

}
