package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirRaiseComplaintRequest;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirRaiseComplaintResponse;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.DisputeStateMachineRegistry;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.UdirService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.UdirComplaintResponseProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.udir.UdirRaiseComplaintProcessor;
import com.phonepe.payments.upiclientmodel.complaint.UPIClientOutgoingComplaintResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class UdirServiceImpl implements UdirService {

    private final UdirRaiseComplaintProcessor udirRaiseComplaintProcessor;
    private final UdirComplaintResponseProcessor udirComplaintResponseProcessor;
    private final DisputeStateMachineRegistry disputeStateMachineRegistry;
    private final DisputeService disputeService;

    @Override
    public UdirRaiseComplaintResponse raiseComplaint(
        UdirRaiseComplaintRequest udirRaiseComplaintRequest) {

        return udirRaiseComplaintRequest.accept(
            udirOutgoingRaiseComplaintRequest -> {
                var disputeStage = DtoUtils.disputeStageDtoToDisputeStage(
                    udirOutgoingRaiseComplaintRequest.getDisputeStage());

                return disputeStage.accept(
                    new DisputeStageVisitor<UdirRaiseComplaintResponse>() {
                        @Override
                        public UdirRaiseComplaintResponse visitFirstLevel() {
                            return udirRaiseComplaintProcessor.process(
                                udirOutgoingRaiseComplaintRequest.getPaymentTransactionId(),
                                udirOutgoingRaiseComplaintRequest.getRequestCode(),
                                udirOutgoingRaiseComplaintRequest.getAdjAmount(), disputeStage,
                                udirOutgoingRaiseComplaintRequest.getComplaintId());
                        }

                        @Override
                        public UdirRaiseComplaintResponse visitPreArbitration() {
                            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_UDIR_COMPLAINT,
                                Map.of(
                                    Constants.MESSAGE,
                                    "Invalid Dispute Stage"
                                ));
                        }

                        @Override
                        public UdirRaiseComplaintResponse visitPreChargeback() {
                            return udirRaiseComplaintProcessor.process(
                                udirOutgoingRaiseComplaintRequest.getPaymentTransactionId(),
                                udirOutgoingRaiseComplaintRequest.getRequestCode(),
                                udirOutgoingRaiseComplaintRequest.getAdjAmount(), disputeStage,
                                udirOutgoingRaiseComplaintRequest.getComplaintId());
                        }
                    });
            });
    }

    @Override
    public void processCallback(
        UPIClientOutgoingComplaintResponse upiClientOutgoingComplaintResponse) {
        udirComplaintResponseProcessor.process(upiClientOutgoingComplaintResponse);
    }


    @Override
    public void updateUdirComplaintStateBreachingTtl(final DateRange dateRange) {

        final var currentDate = LocalDate.now();

        if (dateRange.getEndDate().isAfter(currentDate)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_END_DATE, Map.of(
                "endDate", dateRange.getEndDate().toString(),
                "currentDate", currentDate.toString()));
        }

        final var allRegisteredKeys = disputeStateMachineRegistry.getAllRegisteredKeys();

        allRegisteredKeys.stream()
            .filter(k -> DisputeType.UDIR_COMPLAINTS.contains(k.getDisputeType()))
            .forEach(key ->
                this.updateUdirStateBreachingTtlGivenStateMachineRegistryKey(key, dateRange));
    }

    private void updateUdirStateBreachingTtlGivenStateMachineRegistryKey(
        final DisputeStateMachineRegistryKey disputeStateMachineRegistryKey,
        final DateRange dateRange) {

        final var internalStateMachine = disputeStateMachineRegistry.getInternalStateMachine(
            disputeStateMachineRegistryKey);

        final var transitions = internalStateMachine.getTransitions();
        final var sourceStateToEventMap = transitions.stream()
            .filter(t ->
                DisputeWorkflowState.UDIR_RESPONSE_NOT_RECEIVED_WITHIN_TTL == t.getTarget().getId())
            .collect(Collectors.toMap(
                t -> t.getSource().getId(),
                t -> t.getTrigger().getEvent()));

        final var disputeWorkflows = disputeService.getDisputeWorkflows(
            disputeStateMachineRegistryKey, sourceStateToEventMap.keySet(), dateRange);

        disputeWorkflows.forEach(
            disputeWorkflow -> disputeService.triggerEvent(
                Constants.STRATOS_SYSTEM_USER_OLYMPUS,
                disputeWorkflow.getTransactionReferenceId(),
                disputeWorkflow.getDisputeWorkflowId(),
                sourceStateToEventMap.get(disputeWorkflow.getCurrentState()),
                Constants.EMPTY_TRANSITION_CONTEXT));
    }
}
