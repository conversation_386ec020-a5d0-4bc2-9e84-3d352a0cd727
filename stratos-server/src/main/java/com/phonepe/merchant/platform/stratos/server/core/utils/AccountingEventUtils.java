package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.merchant.platform.stratos.models.accountingevents.*;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.EventGenerationType;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.experimental.UtilityClass;

import java.util.EnumMap;
import java.util.Map;
import java.util.Objects;

@UtilityClass
public class AccountingEventUtils {

    public AccountingEvent toHoldAccountingEvent(
        final StratosConfiguration stratosConfiguration,
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final EventGenerationType eventGenerationType,
        final String originalTransactionId,
        final String mcc,
        final long amount) {

        return AccountingEvent.builder()
            .header(AccountingEventHeader.builder()
                .eventType(toAccountingEventType(
                    stratosConfiguration, disputeWorkflow.getDisputeType(), eventGenerationType))
                .transactionId(getHoldEventId(
                    disputeWorkflow.getTransactionReferenceId(), eventGenerationType, amount))
                .externalTransactionId(dispute.getMerchantTransactionId())
                .paymentId(disputeWorkflow.getTransactionReferenceId())
                .transactionDate(System.currentTimeMillis())
                .build())
            .transaction(AccountingEventTransaction.builder()
                .type(toAccountingTransactionType(
                    stratosConfiguration, disputeWorkflow.getDisputeType(), eventGenerationType))
                .amount(amount)
                .mcc(mcc)
                .merchant(dispute.getMerchantId())
                .originalTransactionId(originalTransactionId)
                .build())
            .build();
    }

    public AccountingEvent toAccountingEvent(
        final StratosConfiguration stratosConfiguration,
        final Dispute dispute,
        final DisputeWorkflow disputeWorkflow,
        final EventGenerationType eventGenerationType,
        final String originalTransactionId,
        final String mcc,
        final long amount) {

        return AccountingEvent.builder()
            .header(AccountingEventHeader.builder()
                .eventType(toAccountingEventType(
                    stratosConfiguration, disputeWorkflow.getDisputeType(), eventGenerationType))
                .transactionId(toAccountingEventId(
                    disputeWorkflow.getDisputeWorkflowId(), eventGenerationType))
                .externalTransactionId(dispute.getMerchantTransactionId())
                .paymentId(disputeWorkflow.getTransactionReferenceId())
                .transactionDate(System.currentTimeMillis())
                .build())
            .transaction(AccountingEventTransaction.builder()
                .type(toAccountingTransactionType(
                    stratosConfiguration, disputeWorkflow.getDisputeType(), eventGenerationType))
                .amount(amount)
                .mcc(mcc)
                .merchant(dispute.getMerchantId())
                .originalTransactionId(originalTransactionId)
                .build())
            .build();
    }

    public AccountingEventType toAccountingEventType(
        final StratosConfiguration config,
        final DisputeType disputeType,
        final EventGenerationType eventGenerationType) {

        final var eventType = config.getAccountingEventTypeMapping()
            .getOrDefault(disputeType, new EnumMap<>(EventGenerationType.class))
            .get(eventGenerationType);

        if (Objects.isNull(eventType)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR, Map.of(
                "disputeType", disputeType,
                "eventGenerationType", eventGenerationType));
        }

        return eventType;
    }

    public AccountingTransactionType toAccountingTransactionType(
        final StratosConfiguration config,
        final DisputeType disputeType,
        final EventGenerationType eventGenerationType) {

        final var transactionType = config.getAccountingTransactionTypeMapping()
            .getOrDefault(disputeType, new EnumMap<>(EventGenerationType.class))
            .get(eventGenerationType);

        if (Objects.isNull(transactionType)) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.WRONG_INPUT_ERROR, Map.of(
                "disputeType", disputeType,
                "eventGenerationType", eventGenerationType));
        }

        return transactionType;
    }

    public String toAccountingEventId(
        final String disputeWorkflowId,
        final EventGenerationType eventGenerationType) {

        return disputeWorkflowId + eventGenerationType.getEventIdSuffix();
    }
    public String getHoldEventId(
            String originalTransactionId,
            EventGenerationType eventGenerationType, long amount) {
        return originalTransactionId + eventGenerationType.getEventIdSuffix() + "_" + amount;
    }
    public EventGenerationType getHoldGenerationType(Dispute dispute) {
        if(dispute.getDisputeType().equals(DisputeType.FRA_FRAUD)) {
            return EventGenerationType.FRA_HOLD_RECOVERY;
        }
        return EventGenerationType.HOLD_RECOVERY;
    }
    public EventGenerationType getHoldReversalGenerationType(Dispute dispute) {
        if(dispute.getDisputeType().equals(DisputeType.FRA_FRAUD)) {
            return EventGenerationType.FRA_HOLD_RECOVERY_REVERSAL;
        }
        return EventGenerationType.HOLD_RECOVERY_REVERSAL;
    }
    public long getHoldAmount(FinancialDisputeWorkflow disputeWorkflow) {
        Dispute dispute = disputeWorkflow.getDispute();
        DisputeType disputeType = dispute.getDisputeType();
        if(disputeType.equals(DisputeType.FRA_FRAUD)) return disputeWorkflow.getDisputedAmount();
        return dispute.getTransactionAmount() -
                disputeWorkflow.getAcceptedAmount();
    }


}
