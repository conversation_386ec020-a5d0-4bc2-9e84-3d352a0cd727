package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.EdcService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

@Slf4j
@Singleton
public class UnblockRefundAction extends UpdateDisputeStateBaseAction {

    private final PaymentsService paymentsService;
    private final EdcService edcService;

    @Inject
    public UnblockRefundAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final PaymentsService paymentsService,
            final EventIngester eventIngester,
            final CallbackActor callbackActor, EdcService edcService) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.paymentsService = paymentsService;
        this.edcService = edcService;
    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        disputeWorkflow.getDisputeType().accept(new DisputeTypeVisitor<Void>() {
            @Override
            public Void visitUpiChargeback() {
                return paymentsService.unblockReversals(disputeWorkflow.getTransactionReferenceId());
            }

            @Override
            public Void visitPgChargeback() {
                return paymentsService.unblockReversals(disputeWorkflow.getTransactionReferenceId());
            }

            @Override
            public Void visitUdirOutgoingComplaint() {
                return null;
            }

            @Override
            public Void visitUdirIncomingComplaint() {
                return null;
            }

            @Override
            public Void visitP2PMToa() {
                return null;
            }

            @Override
            public Void visitEdcChargeback() {
                final Dispute dispute = disputeWorkflow.getDispute();
                return edcService.unblockReversals(dispute.getMerchantTransactionId());
            }

            @Override
            public Void visitNetBankingChargeback() {
                return paymentsService.unblockReversals(disputeWorkflow.getTransactionReferenceId());
            }

            @Override
            public Void visitNotionalCreditToa() {
                return null;
            }

            @Override
            public Void visitBbpsTatBreachToa() {
                return null;
            }

            @Override
            public Void visitFraFraud() {
                return paymentsService.unblockReversals(disputeWorkflow.getTransactionReferenceId());
            }
            @Override
            public Void visitWalletChargeback() {
                return null;
            }
        });
    }
}
