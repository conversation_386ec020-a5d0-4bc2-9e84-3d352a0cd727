package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;

import com.phonepe.merchant.platform.stratos.models.commons.DownloadReportType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileFormat;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DownloadReportRequest {

    @Valid
    @NotNull
    private DisputeFilter filter;

    @NotNull
    private FileFormat fileFormat;

    @NotNull
    private DownloadReportType reportType;

}
