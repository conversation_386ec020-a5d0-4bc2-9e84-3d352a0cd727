package com.phonepe.central.stratos.penalty.growth;

import com.phonepe.central.stratos.notification.NotificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class GrowthUnitTest {
    private GrowthUnit.Visitor<String> visitor;

    @BeforeEach
    public void setUp() {
        visitor = new GrowthUnit.Visitor<>() {
            @Override
            public String visitDay() {
                return "Day";
            }
            @Override
            public String visitWeek() {
                return "Week";
            }
            @Override
            public String visitMonth() {
                return "Month";
            }
            @Override
            public String visitYear() {
                return "Year";
            }
        };
    }

    @Test
    public void testAcceptMethod() {
        assertEquals("Day", GrowthUnit.DAY.accept(visitor));
        assertEquals("Week", GrowthUnit.WEEK.accept(visitor));
        assertEquals("Month", GrowthUnit.MONTH.accept(visitor));
        assertEquals("Year", GrowthUnit.YEAR.accept(visitor));
    }

    @Test
    public void testEnumValuesExist() {
        GrowthUnit[] growthUnits = GrowthUnit.values();
        assertEquals(4,growthUnits.length);
        assertEquals(GrowthUnit.DAY,growthUnits[0]);
        assertEquals(GrowthUnit.WEEK,growthUnits[1]);
        assertEquals(GrowthUnit.MONTH,growthUnits[2]);
        assertEquals(GrowthUnit.YEAR,growthUnits[3]);
    }
}
