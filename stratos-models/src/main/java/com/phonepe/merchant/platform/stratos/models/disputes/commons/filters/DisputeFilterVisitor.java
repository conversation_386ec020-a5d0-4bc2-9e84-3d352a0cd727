package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

public interface DisputeFilterVisitor<T> {

    T visit(DateRangeFilter filter);

    T visit(StateDateRangeFilter filter);

    T visit(MerchantIdDateRangeFilter filter);

    T visit(MerchantIdMerchantTransactionIdFilter filter);

    T visit(TransactionReferenceIdFilter filter);

    T visit(FileNameDisputeFilter filter);

    T visit(CommunicationIdFilter filter);

    T visit(InstrumentTransactionIdFilter filter);
}
