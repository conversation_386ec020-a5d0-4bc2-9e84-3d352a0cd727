package com.phonepe.central.stratos.penalty.growth.calculation.impl;

import com.phonepe.central.stratos.penalty.growth.calculation.Calculation;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.math.BigDecimal;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PenaltyConversionDateFixedAmountCalculationTest {
    private final BigDecimal amount = BigDecimal.valueOf(100.50);
    private final String amountPath = "abc/pqr/xyz";
    private final boolean isDefault = true;

    private PenaltyConversionDateFixedAmountCalculation fixedAmountCalculation;

    @Mock
    private Calculation.CalculationVisitor<String> visitor;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        fixedAmountCalculation = PenaltyConversionDateFixedAmountCalculation.builder().amount(amount).amountPath(amountPath).isDefault(isDefault).build();
    }

    @Test
    public void testConstructor() {
        assertNotNull(fixedAmountCalculation);
        assertEquals(amount, fixedAmountCalculation.getAmount());
        assertEquals(amountPath, fixedAmountCalculation.getAmountPath());
        assertEquals(isDefault, fixedAmountCalculation.isDefault());
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(fixedAmountCalculation)).thenReturn("Visited");
        String result = fixedAmountCalculation.accept(visitor);
        assertEquals("Visited", result);
        verify(visitor).visit(fixedAmountCalculation);
    }
}
