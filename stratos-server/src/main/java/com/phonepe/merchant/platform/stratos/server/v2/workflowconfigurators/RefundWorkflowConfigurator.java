package com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.RefundInitActionV2;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class RefundWorkflowConfigurator implements WorkflowConfigurator<DisputeWorkflowState, DisputeWorkflowEvent> {

    private final RefundInitActionV2 refundInitiationAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;

    @Override
    @SneakyThrows
    public void configure(
            StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {

        transitions
                .withExternal()
                .source(DisputeWorkflowState.CB_REFUND_CREATED)
                .target(DisputeWorkflowState.CB_REFUND_INITIATED)
                .event(DisputeWorkflowEvent.INITIATE_CHARGEBACK_REFUND)
                .action(refundInitiationAction) // make call to RO & state change based on inline success

                .and()
                .withExternal()
                .source(DisputeWorkflowState.CB_REFUND_INITIATED)
                .target(DisputeWorkflowState.CB_REFUND_ACCEPTED)
                .event(DisputeWorkflowEvent.CHARGEBACK_REFUND_ACCEPTED)
                .action(updateDisputeStateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.CB_REFUND_INITIATED)
                .target(DisputeWorkflowState.CB_REFUND_FAILED)
                .event(DisputeWorkflowEvent.INITIATED_TO_FAILED)
                .action(updateDisputeStateAction)

                .and()
                .withExternal()
                .source(DisputeWorkflowState.CB_REFUND_ACCEPTED)
                .target(DisputeWorkflowState.END)
                .event(DisputeWorkflowEvent.END_WORKFLOW)
                .action(updateDisputeStateAction);

    }
}
