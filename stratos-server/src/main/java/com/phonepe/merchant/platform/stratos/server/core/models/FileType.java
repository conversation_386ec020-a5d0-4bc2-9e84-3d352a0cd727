package com.phonepe.merchant.platform.stratos.server.core.models;


/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */

public enum FileType {
    YES{

        @Override
        public <T> T accept(final FileTypeVisitor<T> visitor) {
            return visitor.visitYes();
        }
    },
    APP{
        @Override
        public <T> T accept(final FileTypeVisitor<T> visitor) {
            return visitor.visitApp();
        }
    },
    ICP{
        @Override
        public <T> T accept(final FileTypeVisitor<T> visitor) {
            return visitor.visitIcp();
        }
    },
    PG_FIRST_LEVEL{
        @Override
        public <T> T accept(final FileTypeVisitor<T> visitor) {
            return visitor.visitPgFirstLevel();
        }
    },
    PG_PRE_ARBITRATION{
        @Override
        public <T> T accept(final FileTypeVisitor<T> visitor) {
            return visitor.visitPgPreArb();
        }
    },
    EDC_FIRST_LEVEL {
        @Override
        public <T> T accept(FileTypeVisitor<T> visitor) {
            return visitor.visitEdcFirstLevel();
        }
    },
    NB_FIRST_LEVEL {
        @Override
        public <T> T accept(FileTypeVisitor<T> visitor) {
            return visitor.visitNetBankingFirstLevel();
        }
    },
    BBPS_TOA {
        @Override
        public <T> T accept(FileTypeVisitor<T> visitor) {
            return visitor.visitBbpsToa();
        }
    },
    PHP {
        @Override
        public <T> T accept(FileTypeVisitor<T> visitor) {
            return visitor.visitPhp();
        }
    },
    PRE_CHARGEBACK {
        @Override
        public <T> T accept(FileTypeVisitor<T> visitor) {
            return visitor.visitPreChargeback();
        }
    };

    public abstract <T> T accept(FileTypeVisitor<T> visitor);
}
