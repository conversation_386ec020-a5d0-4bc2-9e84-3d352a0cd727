package com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeMetadataRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStageVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.AutoApprovalActor;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeMetadataHelper;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

import java.util.Map;

@Slf4j
@Singleton
public class ResetChargebackAction extends MandatoryCommentUpdateDisputeStateAction {

    private final IdHelper idHelper;

    @Inject
    @SuppressWarnings("java:S107")
    public ResetChargebackAction(
            final DisputeService disputeService,
            final DisputeWorkflowRepository disputeWorkflowRepository,
            final DisputeMetadataRepository disputeMetadataRepository,
            final DisputeMetadataHelper disputeMetadataHelper,
            final IdHelper idHelper,
            final EventIngester eventIngester,
            final AutoApprovalActor autoApprovalActor,
            final CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, disputeMetadataRepository,
            disputeMetadataHelper, eventIngester, autoApprovalActor, callbackActor);
        this.idHelper = idHelper;
    }

    @Override
    protected void preTransition(final DisputeWorkflow disputeWorkflow,
        final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {
        disputeWorkflow.getDisputeStage().accept(new DisputeStageVisitor<Void>() {
            @Override
            public Void visitFirstLevel() {
                disputeService.getDisputeWorkflowOptional(
                        disputeWorkflow.getTransactionReferenceId(), disputeWorkflow.getDisputeType(),
                        DisputeStage.PRE_ARBITRATION)
                    .ifPresent(disputeWorkflow -> {
                        throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                            Map.of(
                                Constants.MESSAGE,
                                "Reset not allowed as Pre-Arb already exists",
                                Fields.disputeType, disputeWorkflow.getDisputeType(),
                                Fields.disputeWorkflowId, disputeWorkflow.getDisputeWorkflowId(),
                                Fields.transactionReferenceId,
                                disputeWorkflow.getTransactionReferenceId()
                            ));
                    });
                return null;
            }

            @Override
            public Void visitPreArbitration() {
                return null;
            }

            @Override
            public Void visitPreChargeback() {
                return null;
            }
        });
    }

    @Override
    protected void postTransition(final DisputeWorkflow disputeWorkflow) {
        final var dispute = disputeWorkflow.getDispute();
        disputeService.createEntry(dispute,
            newDisputeWorkflow(DisputeWorkflowUtils.getFinancialDisputeWorkflow(
                disputeWorkflow), dispute));
    }

    private DisputeWorkflow newDisputeWorkflow(final FinancialDisputeWorkflow disputeWorkflow,
        final Dispute dispute) {
        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeType(disputeWorkflow.getDisputeType())
            .disputeSourceId(disputeWorkflow.getDisputeSourceId())
            .disputeSourceType(disputeWorkflow.getDisputeSourceType())
            .disputeWorkflowId(
                idHelper.disputeWorkflowId(disputeWorkflow.getTransactionReferenceId()))
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .respondBy(disputeWorkflow.getRespondBy())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .userType(disputeWorkflow.getUserType())
            .gandalfUserId(disputeWorkflow.getGandalfUserId())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .currentState(DisputeWorkflowState.RECEIVED)
            .currentEvent(DisputeWorkflowEvent.CREATE_ENTRY)
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .penaltyAmount(disputeWorkflow.getPenaltyAmount())
            .disputeId(dispute.getDisputeId())
            .communicationId(disputeWorkflow.getCommunicationId())
            .build();
    }
}
