package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.files;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.*;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PartyMerchantIdVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.NetBankingSourceTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.files.models.NetBankingFileRowMeta;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;

@Slf4j
public class NetBankingFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {

    private final PgTransportService pgTransportService;
    private final NetPeService netpeService;
    private final PaymentsService paymentsService;
    private final IdHelper idHelper;

    @Inject
    public NetBankingFileRowProcessorImpl(
        final HandleBarsService handleBarsService,
        final DisputeService disputeService,
        final MerchantMandateService merchantMandateService,
        final NetPeService netpeService,
        final PgTransportService pgTransportService,
        final PaymentsService paymentsService,
        final IdHelper idHelper) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.netpeService = netpeService;
        this.pgTransportService = pgTransportService;
        this.paymentsService = paymentsService;
        this.idHelper = idHelper;
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(TransactionDetail transactionDetails,
        FinancialDisputeWorkflow disputeWorkflowFilePojo, String fileId, DisputeType disputeType,
        FileConfig fileConfig, String disputeId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final var ttlInDays = Optional.ofNullable(disputeWorkflowFilePojo.getDisputeStage())
            .map(disputeStage -> fileConfig.getDisputeStageTTLDaysMap()
                .get(disputeStage))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of(Constants.MESSAGE,
                    "Invalid TTL config for disputeStage: "
                        + disputeWorkflowFilePojo.getDisputeStage())));

        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeWorkflowId(idHelper.disputeWorkflowId(receivedPayment.getTransactionId()))
            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
            .disputeSourceType(SourceType.FILE)
            .disputeSourceId(fileId)
            .transactionReferenceId(receivedPayment.getTransactionId())
            .disputeType(disputeType)
            .disputeStage(disputeWorkflowFilePojo.getDisputeStage())
            .currentEvent(disputeWorkflowFilePojo.getCurrentEvent())
            .currentState(disputeWorkflowFilePojo.getCurrentState())
            .disputedAmount(disputeWorkflowFilePojo.getDisputedAmount())
            .raisedAt(disputeWorkflowFilePojo.getRaisedAt())
            .respondBy(disputeWorkflowFilePojo.getRaisedAt().plusDays(ttlInDays))
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .userType(UserType.SYSTEM)
            .disputeId(disputeId)
            .build();
    }

    @Override
    public TransactionDetail getTransactionDetails(String instrumentTransactionId,
        FileRowMeta fileRowMeta) {

        NetBankingFileRowMeta netbankingFileRowMeta = (NetBankingFileRowMeta) fileRowMeta;
        return netbankingFileRowMeta.getNetBankingSourceType().accept(
            new NetBankingSourceTypeVisitor<>() {
                String paymentsTxnId;

                @Override
                public TransactionDetail visitTPSL() {
                    paymentsTxnId = pgTransportService.getPaymentsIdFromPgTxnId(
                        instrumentTransactionId);
                    return paymentsService.transactionDetailFromOriginalTransactionId(
                        paymentsTxnId);
                }

                @Override
                public TransactionDetail visitNetpe() {

                    paymentsTxnId = netpeService.getPaymentIdFromNetpeTxnService(
                        instrumentTransactionId);
                    return paymentsService.transactionDetailFromOriginalTransactionId(
                        paymentsTxnId);
                }
            }
        );
    }

    @Override
    public Dispute getDispute(DisputeWorkflow disputeWorkflow, TransactionDetail transactionDetail,
        Dispute disputeFilePojo, DisputeType disputeType, String instrumentTransactionId) {
        return disputeWorkflow.getDisputeStage().accept(
            new DisputeStageVisitor<>() {
                @Override
                public Dispute visitFirstLevel() {
                    return enrichDisputeDetails(transactionDetail, disputeFilePojo,
                        disputeType, instrumentTransactionId);
                }

                @Override
                public Dispute visitPreArbitration() {
                    return null; //no pre-arb for netbanking
                }

                @Override
                public Dispute visitPreChargeback() {
                    return null;
                }
            }
        );
    }

    @SneakyThrows
    private Dispute enrichDisputeDetails(final TransactionDetail transactionDetails,
        Dispute disputeFilePojo, final DisputeType disputeType, final String txnId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final String merchantId = receivedPayment.getTo()
            .accept(new PartyMerchantIdVisitor(receivedPayment));

        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeId(idHelper.disputeId(receivedPayment.getTransactionId()))
            .disputeType(disputeType)
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .merchantId(merchantId)
            .merchantTransactionId(receivedPayment.getContext()
                .visit(new PaymentContextMerchantTransactionIdVisitor()))
            .transactionReferenceId(receivedPayment.getTransactionId())
            .instrumentTransactionId(disputeFilePojo.getInstrumentTransactionId())
            .rrn(disputeFilePojo.getRrn())
            .disputeReferenceId(StringUtils.join(disputeFilePojo.getRrn(),
                disputeFilePojo.getInstrumentTransactionId()))
            .transactionAmount(getAmount(transactionDetails.getSentPayment(), txnId))
            .disputeCategory(disputeFilePojo.getDisputeCategory())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .build();

    }

    private Long getAmount(final SentPayment sentPayment, final String txnId) {

        return sentPayment.getPaidFrom()
            .stream()
            .filter(paymentInstrument -> Constants.CHARGEBACK_INSTRUMENT_MAP.get(
                DisputeType.NB_CHARGEBACK).contains(paymentInstrument.getType()))
            .map(PaymentInstrument::getAmount)
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION,
                Map.of(Constants.MESSAGE, "No Netbanking Txn for txnid " + txnId)));

    }
}
