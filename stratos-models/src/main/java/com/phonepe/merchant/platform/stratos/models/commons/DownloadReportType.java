package com.phonepe.merchant.platform.stratos.models.commons;

import com.phonepe.merchant.platform.stratos.models.commons.visitors.DownloadReportVisitor;

public enum DownloadReportType {

    CHARGEBACK {
        @Override
        public <T, R> T accept(final DownloadReportVisitor<T, R> visitor, R downloadReportRequest) {
            return visitor.visitChargeback(downloadReportRequest);
        }
    },

    NPCI_CHARGEBACK {
        @Override
        public <T, R> T accept(final DownloadReportVisitor<T, R> visitor, R downloadReportRequest) {
            return visitor.visitNpicChargeback(downloadReportRequest);
        }
    },

    TOA {
        @Override
        public <T, R> T accept(final DownloadReportVisitor<T, R> visitor, R downloadReportRequest) {
            return visitor.visitToa(downloadReportRequest);
        }
    };

    public abstract <T, R> T accept(final DownloadReportVisitor<T, R> visitor, R downloadReportRequest);
}

