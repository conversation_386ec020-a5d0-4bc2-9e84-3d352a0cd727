CREATE TABLE `client_callback_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `profile_id` varchar(64) NOT NULL,
  `callback_service` varchar(128) NOT NULL,
  `callback_url` varchar(256) NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unq_idx_profile_id` (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;