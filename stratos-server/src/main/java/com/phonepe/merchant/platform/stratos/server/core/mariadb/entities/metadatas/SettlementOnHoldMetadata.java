package com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas;

import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.PrimaryKey;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.v2.files.fraud.FraudMeta;
import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.FieldNameConstants;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.DiscriminatorValue;
import javax.persistence.Entity;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Getter
@Setter
@Entity
@Audited
@ToString
@DynamicInsert
@DynamicUpdate
@NoArgsConstructor
@FieldNameConstants
@DiscriminatorValue(DisputeMetadataType.Ordinals.SOH_DISPUTE_METADA)
public class SettlementOnHoldMetadata extends DisbursementDisputeMetadata {

    @Builder
    @SuppressWarnings("java:S107")
    public SettlementOnHoldMetadata(
            final PrimaryKey key,
            final String disputeMetadataId,
            final String disputeWorkflowId,
            final String transactionReferenceId,
            final LocalDateTime createdAt,
            final LocalDateTime updatedAt,
            final String reasonCode,
            final FraudMeta sohMeta,
            final String disbursementTransactionId) {
        super(key, disputeMetadataId,
                disputeWorkflowId, transactionReferenceId,
                createdAt, updatedAt,DisputeMetadataType.SOH_METADATA,
                disbursementTransactionId);
        this.sohBlob = MapperUtils.serializeToBytes(sohMeta);
        this.reasonCode = reasonCode;
    }

    @Column(name = "reason_code", columnDefinition = "varchar(255)")
    private String reasonCode;

    @Getter(AccessLevel.NONE)
    @Setter(AccessLevel.NONE)
    @Column(name = "metadata_blob", columnDefinition = "blob")
    private byte[] sohBlob;

    public FraudMeta getSohMeta() {
        return MapperUtils.deserialize(sohBlob, FraudMeta.class);
    }

    public void setSohMeta(final FraudMeta sohMeta) {
        this.sohBlob = MapperUtils.serializeToBytes(sohMeta);
    }

}
