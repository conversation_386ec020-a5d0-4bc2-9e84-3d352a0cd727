package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeEvent {
    private String disputeWorkflowId;
    private long disputedAmount;
    private long acceptedAmount;
    private String disputeReason;
    private LocalDateTime lastUpdated;
    private DisputeStatus disputeState;
    private DisputeStageDto disputeStage;
    private List<EvidenceDetail> evidenceDetails;
}
