package com.phonepe.merchant.platform.stratos.models.commons;

import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.COMMENT_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.EMPTY_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.INSTITUTIONAL_CREDIT_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.INSTITUTIONAL_DEBIT_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.PARTIAL_ACCEPTANCE_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.TOA_CONTEXT;
import static com.phonepe.merchant.platform.stratos.models.commons.ContextType.Names.REFUND_CONTEXT;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.CommentContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.EmptyTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalCreditTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.PartialAcceptanceTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.RefundContext;
import javax.validation.constraints.NotNull;

import com.phonepe.merchant.platform.stratos.models.commons.contexts.TOAContext;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "contextType", visible = true)
@JsonSubTypes(value = {
    @Type(value = EmptyTransitionContext.class, name = EMPTY_CONTEXT),
    @Type(value = PartialAcceptanceTransitionContext.class, name = PARTIAL_ACCEPTANCE_CONTEXT),
    @Type(value = InstitutionalCreditTransitionContext.class, name = INSTITUTIONAL_CREDIT_CONTEXT),
    @Type(value = CommentContext.class, name = COMMENT_CONTEXT),
    @Type(value = InstitutionalDebitTransitionContext.class, name = INSTITUTIONAL_DEBIT_CONTEXT),
    @Type(value = TOAContext.class, name = TOA_CONTEXT),
    @Type(value = RefundContext.class, name = REFUND_CONTEXT)
})
public class TransitionContext {

    @NotNull
    private ContextType contextType;
}
