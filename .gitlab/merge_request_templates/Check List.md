# {MR Title}

{Description Place Holder}

## Deployment Pre-Requisites

* [ ] Unit Testing
* [ ] Dev Testing
* [ ] Update local.config file
* [ ] Update stage-stable.yml file
* [ ] QA Testing & Sign Off
* [ ] Automation suite success {Link Placeholder}
* [ ] Code formatting
* [ ] MR Review Sign Off

## Deployment Plan checklist

* [ ] API whitelisting required -- YES / NO
* [ ] Rosey changes required -- YES / NO
* [ ] Complete downtime required -- YES / NO
* [ ] New foxtrot events being added --  YES/NO
    * If yes {JIRA Link for event/column addition in lucy}
* [ ] DB changes required -- YES / NO
    * if Yes {JIRA link will be added}
* [ ] Dependency on other release -- YES / NO
    * If Yes {list dependency with MR link where able applicable}
* [ ] Olympus Roles or permission changes needed -- YES/NO
  * If yes then list them 

# Post Deployment Plan
{ADD if any}
