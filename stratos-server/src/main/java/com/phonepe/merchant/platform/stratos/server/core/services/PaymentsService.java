package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.payments.pay.PaymentProcessorResult;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.upi.udircomplaint.UdirOutgoingComplaintRequest;
import java.time.LocalDateTime;

public interface PaymentsService {

    boolean isFullyReversed(String originalTransactionId);

    boolean hasRgcsReversalTransactions(String originalTransactionId);

    void blockReversals(String originalTransactionId);

    Void unblockReversals(String originalTransactionId);

    TransactionDetail transactionDetailFromUpiId(String upiTransactionId, String utr,
        LocalDateTime txnDate);

    String getGlobalPaymentId(String originalTransactionId);

    TransactionDetail transactionDetailFromOriginalTransactionId(String originalTransactionId);

    void raiseUdirComplaint(UdirOutgoingComplaintRequest udirOutgoingComplaintRequest);

    PaymentProcessorResult p2pmToaPay(long amount, String merchantId, String merchantOrderId, String originalTxnId);

    MerchantTransactionState getMerchantTransactionStatus(String merchantId, String merchantTransactionId);
}
