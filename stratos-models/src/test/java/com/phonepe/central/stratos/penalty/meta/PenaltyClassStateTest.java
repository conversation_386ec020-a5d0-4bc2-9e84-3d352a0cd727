package com.phonepe.central.stratos.penalty.meta;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

public class PenaltyClassStateTest {

    @Test
    public void testEnumValues() {
        PenaltyClassState[] states = PenaltyClassState.values();
        assertEquals(4, states.length);
        assertArrayEquals(new PenaltyClassState[]{
                PenaltyClassState.CREATED,
                PenaltyClassState.ACTIVE,
                PenaltyClassState.SUSPENDED,
                PenaltyClassState.DEPRECATED
        }, states);
    }

    @Test
    public void testValueOf() {
        assertEquals(PenaltyClassState.ACTIVE, PenaltyClassState.valueOf("ACTIVE"));
        assertEquals(PenaltyClassState.SUSPENDED, PenaltyClassState.valueOf("SUSPENDED"));
        assertEquals(PenaltyClassState.DEPRECATED, PenaltyClassState.valueOf("DEPRECATED"));
    }
}
