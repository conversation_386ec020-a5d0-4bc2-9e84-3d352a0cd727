package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.COMMUNICATION_ID;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.DATE_RANGE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.FILE_NAME;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.INSTRUMENT_TRANSACTION_ID;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.MERCHANT_ID_DATE_RANGE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.MERCHANT_TRANSACTION_ID;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.STATE_DATE_RANGE;
import static com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType.Names.TRANSACTION_REFERENCE_ID;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.CommunicationIdFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.FileNameDisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DisputeFilterVisitor;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.InstrumentTransactionIdFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.MerchantIdDateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.MerchantIdMerchantTransactionIdFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.StateDateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.TransactionReferenceIdFilter;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "filterType", visible = true)
@JsonSubTypes(value = {
    @Type(value = DateRangeFilter.class, name = DATE_RANGE),
    @Type(value = StateDateRangeFilter.class, name = STATE_DATE_RANGE),
    @Type(value = MerchantIdDateRangeFilter.class, name = MERCHANT_ID_DATE_RANGE),
    @Type(value = MerchantIdMerchantTransactionIdFilter.class, name = MERCHANT_TRANSACTION_ID),
    @Type(value = TransactionReferenceIdFilter.class, name = TRANSACTION_REFERENCE_ID),
    @Type(value = FileNameDisputeFilter.class, name = FILE_NAME),
    @Type(value = CommunicationIdFilter.class, name = COMMUNICATION_ID),
    @Type(value = InstrumentTransactionIdFilter.class, name = INSTRUMENT_TRANSACTION_ID)
})
public abstract class DisputeFilter {

    @NotNull
    private DisputeFilterType filterType;

    public abstract <T> T accept(DisputeFilterVisitor<T> filterVisitor);
}
