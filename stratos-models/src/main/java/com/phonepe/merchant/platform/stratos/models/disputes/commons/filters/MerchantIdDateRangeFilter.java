package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import java.util.Collections;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class MerchantIdDateRangeFilter extends DisputeFilter {

    @NotEmpty
    private List<String> merchantIds;

    @Valid
    @NotNull
    private DateRange dateRange;

    public MerchantIdDateRangeFilter() {
        super(DisputeFilterType.MERCHANT_ID_DATE_RANGE);
    }

    @Builder
    public MerchantIdDateRangeFilter(
        @Singular final List<String> merchantIds,
        final DateRange dateRange) {
        this();
        this.merchantIds = merchantIds;
        this.dateRange = dateRange;
    }

    @JsonProperty("merchantId")
    public void setMerchantId(final String merchantId) {
        this.merchantIds = Collections.singletonList(merchantId);
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
