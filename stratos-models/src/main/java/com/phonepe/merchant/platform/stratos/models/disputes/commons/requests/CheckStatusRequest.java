package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;

import static com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto.Names.UDIR_OUTGOING_COMPLAINT;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirOutgoingComplaintStatusRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "disputeType", visible = true)
@JsonSubTypes(value = {
    @Type(value = UdirOutgoingComplaintStatusRequest.class, name = UDIR_OUTGOING_COMPLAINT)
})
public abstract class CheckStatusRequest {

    private DisputeTypeDto disputeType;
    private String transactionId;

    public CheckStatusRequest(DisputeTypeDto disputeType) {
        this.disputeType = disputeType;
    }

    public abstract <T> T accept(
        CheckStatusRequestVisitor<T> checkStatusRequestVisitor);
}
