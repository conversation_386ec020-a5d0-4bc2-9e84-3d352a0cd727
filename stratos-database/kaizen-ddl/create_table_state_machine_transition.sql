CREATE TABLE `state_machine_transition` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `action_type` varchar(128) NOT NULL,
  `version` varchar(64) NOT NULL,
  `source` varchar(128) NOT NULL,
  `target` varchar(128) NOT NULL,
  `event` varchar(128) NOT NULL,
  `action_key` varchar(128) NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `action_type_version_idx` (`action_type`,`version`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;