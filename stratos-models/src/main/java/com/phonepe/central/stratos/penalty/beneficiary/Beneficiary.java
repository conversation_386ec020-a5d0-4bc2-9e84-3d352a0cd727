package com.phonepe.central.stratos.penalty.beneficiary;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public final class Beneficiary implements Serializable {

    @NotNull
    private BeneficiaryType type;

    @NotBlank
    private String id;


}
