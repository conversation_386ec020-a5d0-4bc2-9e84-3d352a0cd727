package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.ContextType;
import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EmptyTransitionContext extends TransitionContext {

    @Builder
    public EmptyTransitionContext() {
        super(ContextType.EMPTY_CONTEXT);
    }
}
