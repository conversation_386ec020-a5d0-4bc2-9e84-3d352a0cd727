package com.phonepe.central.stratos.notification.impl;

import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.penalty.Penalty;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PenaltyGrowingNotificationTest {

    @Mock
    private Penalty penalty;

    @Mock
    private ChangeNotificationVisitor<String> visitor;

    @InjectMocks
    private PenaltyGrowingNotification penaltyGrowingNotification;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        penaltyGrowingNotification= PenaltyGrowingNotification.builder()
                .penalty(penalty)
                .build();
    }

    @Test
    public void testPenaltyGrowingNotificationCreation() {
        assertNotNull(penaltyGrowingNotification);
        assertEquals(penalty, penaltyGrowingNotification.getPenalty());
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(any(PenaltyGrowingNotification.class))).thenReturn("Visited");

        String result = penaltyGrowingNotification.accept(visitor);
        assertEquals("Visited", result);

        verify(visitor).visit(penaltyGrowingNotification);
    }
}
