package com.phonepe.merchant.platform.stratos.models.feeds.contexts;

import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContext;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeContextType;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class ChargebackReversalDisputeContext extends DisputeContext {

    private String instrumentTransactionId;

    private String disputeReferenceId;

    private String rrn;

    private String chargebackReversalSourceType;

    private String chargebackReversalSourceId;

    private long reversedAmount;

    public ChargebackReversalDisputeContext() {
        super(DisputeContextType.CHARGEBACK_REVERSAL);
    }

    @Builder
    public ChargebackReversalDisputeContext(
        final String instrumentTransactionId,
        final String disputeReferenceId,
        final String rrn,
        final String chargebackReversalSourceType,
        final String chargebackReversalSourceId,
        final long reversedAmount) {
        this();
        this.instrumentTransactionId = instrumentTransactionId;
        this.disputeReferenceId = disputeReferenceId;
        this.rrn = rrn;
        this.chargebackReversalSourceType = chargebackReversalSourceType;
        this.chargebackReversalSourceId = chargebackReversalSourceId;
        this.reversedAmount = reversedAmount;
    }
}
