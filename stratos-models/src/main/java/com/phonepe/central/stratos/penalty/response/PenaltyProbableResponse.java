package com.phonepe.central.stratos.penalty.response;

import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class PenaltyProbableResponse {

    @NotBlank
    private PenaltyProbable penaltyProbable;

    @NotBlank
    private TenantInfo tenantInfo;

}
