package com.phonepe.merchant.platform.stratos.models.row;

import static com.phonepe.merchant.platform.stratos.models.row.RowTypeDto.Names.EDC_MIS_ROW;
import static com.phonepe.merchant.platform.stratos.models.row.RowTypeDto.Names.PG_MIS_ROW;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "rowTypeDto", visible = true)
@JsonSubTypes(value = {
    @Type(value = PgMisRowDto.class, name = PG_MIS_ROW),
    @Type(value = EdcRowDto.class, name = EDC_MIS_ROW)
})
public abstract class RowDto {

    private RowTypeDto type;
}
