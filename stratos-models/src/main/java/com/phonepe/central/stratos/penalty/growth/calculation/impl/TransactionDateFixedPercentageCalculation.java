package com.phonepe.central.stratos.penalty.growth.calculation.impl;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.growth.calculation.CalculationType;

import java.math.BigDecimal;

import com.phonepe.central.stratos.penalty.growth.calculation.FixedPercentageCalculation;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@JsonPropertyOrder({"type", "percentage", "maxAmount", "amountPath", "isDefault"})
public class TransactionDateFixedPercentageCalculation extends FixedPercentageCalculation {

    @Builder
    @JsonCreator
    public TransactionDateFixedPercentageCalculation(@JsonProperty("amountPath") String amountPath,
                                                           @JsonProperty("percentage") BigDecimal percentage,
                                                           @JsonProperty("maxAmount") BigDecimal maxAmount,
                                                           @JsonProperty("isDefault") boolean isDefault) {
        super(CalculationType.TRANSACTION_DATE_FIXED_PERCENTAGE, amountPath, isDefault, percentage, maxAmount);
    }

    @Override
    public <T> T accept(CalculationVisitor<T> visitor) {
        return visitor.visit(this);
    }
}