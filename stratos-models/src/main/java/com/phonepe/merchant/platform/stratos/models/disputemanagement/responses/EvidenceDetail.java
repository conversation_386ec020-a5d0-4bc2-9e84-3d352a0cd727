package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import java.util.Map;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.EvidenceStatusDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvidenceDetail {
    private String evidenceId;
    private String documentId;
    private EvidenceStatusDto status;
    private String disputeWorkflowId;
    private Map<String, Object> metadata;
}
