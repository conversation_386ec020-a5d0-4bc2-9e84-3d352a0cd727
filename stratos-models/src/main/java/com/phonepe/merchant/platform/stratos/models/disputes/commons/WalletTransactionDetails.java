package com.phonepe.merchant.platform.stratos.models.disputes.commons;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@SuperBuilder
public class WalletTransactionDetails extends TransactionDetails {

    private String upiTransactionId;
    @ToString.Exclude
    private String sourceVpa;
    @ToString.Exclude
    private String sourceName;
    @ToString.Exclude
    private String destinationVpa;
    @ToString.Exclude
    private String destinationName;
    private String mcc;
}
