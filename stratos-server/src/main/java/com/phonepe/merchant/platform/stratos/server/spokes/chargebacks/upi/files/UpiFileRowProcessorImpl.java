package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.files;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.commons.SourceType;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalCreditTransitionContext;
import com.phonepe.merchant.platform.stratos.models.commons.contexts.InstitutionalDebitTransitionContext;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.events.type.DisputeSignalEvent;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.StringUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.files.models.UpiFileRowMeta;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;


@Slf4j
public class UpiFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {

    private final DisputeService disputeService;
    private final PaymentsService paymentsService;
    private final IdHelper idHelper;

    private final EventIngester eventIngester;
    private final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap;

    @Inject
    public UpiFileRowProcessorImpl(final HandleBarsService handleBarsService,
        final DisputeService disputeService,
        final PaymentsService paymentsService, final MerchantMandateService merchantMandateService,
        final IdHelper idHelper,final EventIngester eventIngester, final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.disputeService = disputeService;
        this.paymentsService = paymentsService;
        this.idHelper = idHelper;
        this.eventIngester=eventIngester;
        this.disputeWorkflowVersionMap = disputeWorkflowVersionMap;
    }

    @Override
    public TransactionDetail getTransactionDetails(final String instrumentTransactionId,
        final FileRowMeta fileRowMeta) {

        return paymentsService.transactionDetailFromUpiId(instrumentTransactionId,
            getUtr((UpiFileRowMeta) fileRowMeta),
            getTxnDate((UpiFileRowMeta) fileRowMeta));
    }

    private String getUtr(final UpiFileRowMeta upiFileRowMeta) {
        return upiFileRowMeta.getUtr().replace("'", ""); // Hack To be removed
    }

    private LocalDateTime getTxnDate(final UpiFileRowMeta upiFileRowMeta) {
        return Optional.ofNullable(upiFileRowMeta.getTransactionDate())
            .orElse(LocalDateTime.now());
    }

    @Override
    public Dispute getDispute(final DisputeWorkflow disputeWorkflowFilePojo,
        final TransactionDetail transactionDetails,
        final Dispute disputeFilePojo, final DisputeType disputeType,
        final String instrumentTransactionId) {
        return disputeWorkflowFilePojo.getDisputeStage()
            .accept(new DisputeStageVisitor<>() {
                @SneakyThrows
                @Override
                public Dispute visitFirstLevel() {
                    return enrichDisputeDetails(transactionDetails, disputeFilePojo, disputeType);
                }

                @Override
                public Dispute visitPreArbitration() {
                    final var currentStage =
                        disputeWorkflowFilePojo.getCurrentState() == DisputeWorkflowState.RECEIVED
                            ? DisputeStage.FIRST_LEVEL :
                            DisputeStage.PRE_ARBITRATION;
                    final var transactionReferenceId = transactionDetails.getSentPayment()
                        .getTransactionId();

                    try {
                        final Dispute dispute = disputeService.getDispute(
                            transactionReferenceId,
                            disputeType,
                            currentStage);
                        dispute.setCurrentDisputeStage(DisputeStage.PRE_ARBITRATION);
                        return dispute;
                    } catch (final DisputeException e) {
                        // Throwing the error FIRST_LEVEL_DISPUTE_NOT_FOUND when PreArbs for which no First-Level processed via Stratos.
                        if (StratosErrorCodeKey.DISPUTE_NOT_FOUND == e.getErrorCode()) {
                            log.info("First Level Dispute not found for {}",
                                transactionReferenceId);
                            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FIRST_LEVEL_DISPUTE_NOT_FOUND,
                                Map.of(
                                    "transactionReferenceId", transactionReferenceId,
                                    "disputeType", disputeType,
                                    "disputeStage", currentStage
                                ));
                        } else {
                            throw e;
                        }
                    }

                }

                @Override
                public Dispute visitPreChargeback() {
                    return null;
                }
            });
    }

    @Override
    public Boolean triggerEvent(final DisputeWorkflow disputeWorkflow, final Dispute dispute,
        final TransactionDetail transactionDetails) {
        return disputeWorkflow.getCurrentState()
            .accept(new DisputeWorkflowStateNonMandatoryVisitor<>() {
                @Override
                public Boolean visitReceived() {
                    ValidationUtils.validateEntryDisputeAmount(disputeWorkflow, dispute,
                        disputeService);
                    ValidationUtils.validateAmount(transactionDetails.getSentPayment(),
                        dispute.getInstrumentTransactionId(),
                        dispute.getTransactionAmount(), DisputeType.UPI_CHARGEBACK);
                    disputeService.createEntry(dispute, disputeWorkflow);
                    return true;
                }

                @Override
                public Boolean visitCreditReceived() {

                    final var storedDisputeWorkflow = disputeService
                        .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                            dispute.getDisputeType(),
                            dispute.getCurrentDisputeStage());
                    eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(storedDisputeWorkflow,DisputeWorkflowEvent.RECEIVE_CREDIT,DisputeSignalEvent.SignalType.CREDIT));
                    return Optional.ofNullable(storedDisputeWorkflow.getCurrentState()
                            .accept(new DisputeWorkflowStateNonMandatoryVisitor<Boolean>() {
                                @Override
                                public Boolean visitNPCIRepresentmentCompleted() {
                                    triggerCreditEvent(disputeWorkflow.getDisputeSourceId(),
                                        disputeWorkflow, dispute,
                                        storedDisputeWorkflow,
                                        DisputeWorkflowEvent.RECEIVE_CREDIT);
                                    return true;
                                }

                                @Override
                                public Boolean visitNPCIPartialRepresentmentCompleted() {
                                    triggerCreditEvent(disputeWorkflow.getDisputeSourceId(),
                                        disputeWorkflow, dispute,
                                        storedDisputeWorkflow,
                                        DisputeWorkflowEvent.RECEIVE_PARTIAL_CREDIT);
                                    return true;
                                }

                                @Override
                                public Boolean visitRepresentmentRequired(){
                                    triggerCreditEvent(disputeWorkflow.getDisputeSourceId(),
                                        disputeWorkflow, dispute,
                                        storedDisputeWorkflow,
                                        DisputeWorkflowEvent.REPRESENTMENT_REQUIRED_TO_CREDIT_RECEIVED);
                                    return true;
                                }

                                @Override
                                public Boolean visitFulfilmentDocumentsReceived() {
                                    triggerCreditEvent(disputeWorkflow.getDisputeSourceId(),
                                        disputeWorkflow, dispute,
                                        storedDisputeWorkflow,
                                        DisputeWorkflowEvent.FULFILMENT_DOCUMENTS_RECEIVED_TO_CREDIT_RECEIVED);
                                    return true;
                                }

                                @Override
                                public Boolean visitRepresentedCompleted() { //used for V2 version
                                    return visitNPCIRepresentmentCompleted();
                                }

                                @Override
                                public Boolean visitPartialRepresentmentCompleted() { //used for V2 version
                                    return visitNPCIPartialRepresentmentCompleted();
                                }
                            }))
                        .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                            Map.of(Constants.MESSAGE,
                                String.format("Invalid credit transformation for %s from %s",
                                    storedDisputeWorkflow.getDisputeWorkflowId(),
                                    storedDisputeWorkflow.getCurrentState()),
                                Constants.DISPUTE_TYPE, disputeWorkflow.getDisputeType()
                            )
                        ));
                }

                @Override
                public Boolean visitDebitReceived() {
                    try {
                        final var storedDisputeWorkflow = disputeService
                            .getDisputeWorkflow(dispute.getTransactionReferenceId(),
                                dispute.getDisputeType(),
                                dispute.getCurrentDisputeStage());
                        eventIngester.generateEvent(FoxtrotEventUtils.getSignalEvent(storedDisputeWorkflow,DisputeWorkflowEvent.RECEIVE_DEBIT,DisputeSignalEvent.SignalType.DEBIT));
                        return Optional.ofNullable(storedDisputeWorkflow.getCurrentState()
                                .accept(new DisputeWorkflowStateNonMandatoryVisitor<Boolean>() {
                                    @Override
                                    public Boolean visitNPCIAcceptanceCompleted() {
                                        triggerDebitEvent(disputeWorkflow.getDisputeSourceId(),
                                            disputeWorkflow, dispute,
                                            storedDisputeWorkflow,
                                            DisputeWorkflowEvent.RECEIVE_DEBIT);
                                        return true;
                                    }

                                    @Override
                                    public Boolean visitNPCIPartialRepresentmentCompleted() {
                                        triggerDebitEvent(disputeWorkflow.getDisputeSourceId(),
                                            disputeWorkflow, dispute,
                                            storedDisputeWorkflow,
                                            DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT);
                                        return true;
                                    }

                                    @Override
                                    public Boolean visitMerchantAcceptedChargeback() {
                                        triggerDebitEvent(disputeWorkflow.getDisputeSourceId(),
                                            disputeWorkflow, dispute,
                                            storedDisputeWorkflow,
                                            DisputeWorkflowEvent.MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED);
                                        return true;
                                    }

                                    @Override
                                    public Boolean visitPartialRepresentmentCompleted() { //for V2 version
                                        return visitNPCIPartialRepresentmentCompleted();
                                    }

                                    @Override
                                    public Boolean visitAcceptanceCompleted() { //for V2 version
                                        return visitNPCIAcceptanceCompleted();
                                    }
                                }))
                            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                                Map.of(Constants.MESSAGE,
                                    String.format("Invalid debit transformation for %s from %s",
                                        storedDisputeWorkflow.getDisputeWorkflowId(),
                                        storedDisputeWorkflow.getCurrentState()),
                                    Constants.DISPUTE_TYPE, disputeWorkflow.getDisputeType()
                                )
                            ));
                    } catch (final DisputeException e) {
                        // Temporary Measure to store PreArbs for which First-Level Not processed via Stratos.
                        if (StratosErrorCodeKey.DISPUTE_WORKFLOW_NOT_FOUND == e.getErrorCode()) {
                            log.info("upi pre arb acceptance row ignored for {}",
                                disputeWorkflow.getTransactionReferenceId());
                            return true;
                        }
                        throw e;
                    }
                }
            });
    }



    private void triggerCreditEvent(final String fileId, final DisputeWorkflow disputeWorkflow,
        final Dispute dispute, final DisputeWorkflow storedDisputeWorkflow,
        final DisputeWorkflowEvent disputeWorkflowEvent) {
        disputeService.triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            dispute.getTransactionReferenceId(),
            storedDisputeWorkflow.getDisputeWorkflowId(),
            disputeWorkflowEvent,
            InstitutionalCreditTransitionContext.builder()
                .creditAmount(disputeWorkflow.getDisputedAmount())
                .creditSourceId(fileId)
                .creditSourceType(SourceType.FILE)
                .build()
        );
    }

    private void triggerDebitEvent(final String fileId, final DisputeWorkflow disputeWorkflow,
        final Dispute dispute, final DisputeWorkflow storedDisputeWorkflow,
        final DisputeWorkflowEvent disputeWorkflowEvent) {
        disputeService.triggerEvent(
            Constants.STRATOS_SYSTEM_USER_OLYMPUS,
            dispute.getTransactionReferenceId(),
            storedDisputeWorkflow.getDisputeWorkflowId(),
            disputeWorkflowEvent,
            InstitutionalDebitTransitionContext.builder()
                .debitAmount(disputeWorkflow.getDisputedAmount())
                .debitSourceId(fileId)
                .debitSourceType(SourceType.FILE)
                .build()
        );
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(final TransactionDetail transactionDetails,
        final FinancialDisputeWorkflow disputeWorkflow,
        final String fileId, final DisputeType disputeType,
        final FileConfig fileConfig, final String disputeId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final var ttlInDays = Optional.ofNullable(disputeWorkflow.getDisputeStage())
            .map(disputeStage -> fileConfig.getDisputeStageTTLDaysMap()
                .get(disputeStage))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of(Constants.MESSAGE,
                    "Invalid TTL config for disputeStage: " + disputeWorkflow.getDisputeStage())));

        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeSourceId(fileId)
            .disputeSourceType(
                com.phonepe.merchant.platform.stratos.server.core.models.SourceType.FILE)
            .disputeWorkflowId(idHelper.disputeWorkflowId(receivedPayment.getTransactionId()))
            .disputeWorkflowVersion(disputeWorkflowVersionMap.get(disputeType))
            .transactionReferenceId(receivedPayment.getTransactionId())
            .respondBy(disputeWorkflow.getRaisedAt().plusDays(ttlInDays))
            .userType(UserType.SYSTEM)
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .disputeStage(disputeWorkflow.getDisputeStage())
            .currentState(disputeWorkflow.getCurrentState())
            .currentEvent(disputeWorkflow.getCurrentEvent())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .penaltyAmount(disputeWorkflow.getPenaltyAmount())
            .disputeId(disputeId)
            .build();
    }

    @SneakyThrows
    private Dispute enrichDisputeDetails(final TransactionDetail transactionDetails,
        final Dispute disputeFilePojo, final DisputeType disputeType) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final String merchantId = getMerchantIdByTransactionDetails(transactionDetails);
        // Merchant details

        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeType(disputeType)
            .disputeId(idHelper.disputeId(receivedPayment.getTransactionId()))
            .transactionReferenceId(receivedPayment.getTransactionId())
            .disputeReferenceId(StringUtils.join(disputeFilePojo.getRrn(),
                disputeFilePojo.getInstrumentTransactionId()))
            .merchantId(merchantId)
            .merchantTransactionId(receivedPayment.getContext()
                .visit(new PaymentContextMerchantTransactionIdVisitor()))
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .instrumentTransactionId(disputeFilePojo.getInstrumentTransactionId())
            .transactionAmount(getAmount(transactionDetails.getSentPayment(), receivedPayment.getTransactionId()))
            .rrn(disputeFilePojo.getRrn())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .disputeCategory(disputeFilePojo.getDisputeCategory())
            .build();
    }
    private Long getAmount(final SentPayment sentPayment, final String txnId) {

        return sentPayment.getPaidFrom()
            .stream()
            .filter(paymentInstrument -> Constants.CHARGEBACK_INSTRUMENT_MAP.get(
                DisputeType.UPI_CHARGEBACK).contains(paymentInstrument.getType()))
            .map(PaymentInstrument::getAmount)
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION,
                Map.of(Constants.MESSAGE, "No UPI Txn for txnid " + txnId)));

    }

}
