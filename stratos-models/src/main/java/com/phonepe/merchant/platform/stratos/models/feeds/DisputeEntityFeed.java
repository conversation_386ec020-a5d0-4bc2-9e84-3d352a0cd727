package com.phonepe.merchant.platform.stratos.models.feeds;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeEntityFeed extends TstoreFeed {
    // ENUMS will be converted to string here as per Tstore guidelines
    @NotEmpty
    private String entityId;

    @NotEmpty
    private String disputeId;

    @NotEmpty
    private String workflowId;

    @NotEmpty
    private String type;

    @NotEmpty
    private String stage;

    @NotEmpty
    private String category;

    @NotEmpty
    private String status;

    @NotEmpty
    private String transactionType;

    @NotEmpty
    private String sourceType;

    @NotEmpty
    private String sourceId;

    @NotEmpty
    private String version;

    @NotEmpty
    private String merchantId;

    @NotEmpty
    private String transactionId;

    @NotEmpty
    private String merchantTransactionId;

    @NotEmpty
    private String instrumentTransactionId;

    @NotEmpty
    private long transactionAmount;

    @NotEmpty
    private long disputedAmount;

    @NotEmpty
    private long penaltyAmount;

    @NotEmpty
    private long acceptedAmount;

    private String disputeReason;

    @NotEmpty
    private LocalDateTime raisedAt;

    @NotEmpty
    private LocalDateTime respondBy;

    @NotEmpty
    private LocalDateTime createdAt;

    @NotEmpty
    private LocalDateTime updatedAt;

    @Override
    public EntityType getEntityType() {
        return EntityType.DISPUTE_ENTITY;
    }
}
