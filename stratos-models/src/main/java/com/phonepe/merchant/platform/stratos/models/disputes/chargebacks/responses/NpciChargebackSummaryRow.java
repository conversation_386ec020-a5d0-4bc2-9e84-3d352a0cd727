package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Bankadjref",
    "flag",
    "shdate",
    "adjamt",
    "shser",
    "UTXID",
    "filename",
    "Reason",
    "specifyother"
})
public class NpciChargebackSummaryRow {

    @JsonProperty("Bankadjref")
    private String bankadjref;

    @JsonProperty("flag")
    private String flag;

    @JsonProperty("shdate")
    private String shdate;

    @JsonProperty("adjamt")
    private long adjamt;

    @JsonProperty("shser")
    private String shser;

    @JsonProperty("UTXID")
    private String uTXID;

    @JsonProperty("filename")
    private String filename;

    @JsonProperty("Reason")
    private String reason;

    @JsonProperty("specifyother")
    private String specifyother;

}
