package com.phonepe.merchant.platform.stratos.models.disputes.commons.filters;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilter;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeFilterType;
import java.util.Collections;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

@Data
@EqualsAndHashCode(callSuper = true)
public class InstrumentTransactionIdFilter extends DisputeFilter {

    @NotEmpty
    private List<String> instrumentTransactionIds;

    public InstrumentTransactionIdFilter() {
        super(DisputeFilterType.INSTRUMENT_TRANSACTION_ID);
    }

    @Builder
    public InstrumentTransactionIdFilter(@Singular final List<String> instrumentTransactionIds) {
        this();
        this.instrumentTransactionIds = instrumentTransactionIds;
    }

    @JsonProperty("instrumentTransactionId")
    public void setTransactionReferenceId(final String instrumentTransactionId) {
        this.instrumentTransactionIds = Collections.singletonList(instrumentTransactionId);
    }

    @Override
    public <T> T accept(final DisputeFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
