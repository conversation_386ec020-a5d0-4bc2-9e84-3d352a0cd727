package com.phonepe.central.stratos.notification.impl;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.notification.ChangeNotitification;
import com.phonepe.central.stratos.notification.NotificationType;
import com.phonepe.central.stratos.penalty.Penalty;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PenaltyCreatedNotification extends ChangeNotitification {
    @Valid
    @NotNull
    private Penalty penalty;

    @Builder
    @JsonCreator
    public PenaltyCreatedNotification(@JsonProperty("penalty") final Penalty penalty) {
        super(NotificationType.PENALTY_CREATED);
        this.penalty = penalty;
    }

    @Override
    public <T> T accept(ChangeNotificationVisitor<T> visitor) {
        return visitor.visit(this);
    }

}
