package com.phonepe.central.stratos.notification;

import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@AllArgsConstructor
public class PenaltyCreatedEmailContext {
    @NotBlank
    private final String tenantName;
    @NotBlank
    private final String penaltyClassName;
    @NotBlank
    private final String numberOfPenalties;
    @NotBlank
    private final String startDate;
    @NotBlank
    private final String endDate;
}
