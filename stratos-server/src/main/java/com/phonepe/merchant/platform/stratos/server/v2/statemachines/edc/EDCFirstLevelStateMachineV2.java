package com.phonepe.merchant.platform.stratos.server.v2.statemachines.edc;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.DebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.StateChangeListener;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.TerminalStateListener;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseHoldRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.statemachines.actions.EdcChargebackCreateEntryAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.ChargebackRecoveryEligibilityAction;
import com.phonepe.merchant.platform.stratos.server.v2.actions.HoldReversalAction;
import com.phonepe.merchant.platform.stratos.server.v2.statemachines.BaseStateMachineV2;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.*;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

@Singleton
public class EDCFirstLevelStateMachineV2 extends BaseStateMachineV2 {

    private final EdcChargebackCreateEntryAction createEntryAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final DebitDisputeAction debitDisputeAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction;
    private final ChargebackRecoveryEligibilityAction recoverChargebackAction;
    private final HoldReversalAction holdReversalAction;

    @Inject
    @SuppressWarnings("java:S107")
    protected EDCFirstLevelStateMachineV2(
            final TransitionLockCommand transitionLockCommand,
            final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
            final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator,
            final MerchantCommunicationConfigurator merchantCommunicationConfigurator,
            final FraudDetectionConfigurator fraudDetectionConfigurator,
            final EdcChargebackCreateEntryAction createEntryAction,
            final AcceptDisputeAction acceptDisputeAction,
            final DebitDisputeAction debitDisputeAction,
            final FraudCheckDisputeAction fraudCheckDisputeAction,
            final UpdateDisputeStateAction updateDisputeStateAction,
            final CancelWorkflowConfigurator cancelWorkflowConfigurator,
            final RaiseHoldRecoveryReversalAccountingEventAction raiseHoldRecoveryReversalAccountingEventAction,
            final ReverseHoldRecoverCBConfigurator reverseHoldRecoverCBConfigurator,
            final RaiseChargebackRecoveryAccountingEventAction raiseAccountingEventAction,
            final ChargebackRecoveryEligibilityAction recoverChargebackAction,
            final HoldReversalAction holdReversalAction,
            final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener,
            final StateChangeListener<DisputeWorkflowState, DisputeWorkflowEvent> stateChangeListener) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor, reverseHoldRecoverCBConfigurator, proactiveHoldWorkflowConfigurator,
          merchantCommunicationConfigurator, fraudDetectionConfigurator, fraudCheckDisputeAction, cancelWorkflowConfigurator, terminalStateListener,
                stateChangeListener);
        this.createEntryAction = createEntryAction;
        this.acceptDisputeAction = acceptDisputeAction;
        this.debitDisputeAction = debitDisputeAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.raiseHoldRecoveryReversalAccountingEventAction = raiseHoldRecoveryReversalAccountingEventAction;
        this.raiseAccountingEventAction = raiseAccountingEventAction;
        this.recoverChargebackAction = recoverChargebackAction;
        this.holdReversalAction = holdReversalAction;
    }

    @Override
    public DisputeStateMachineRegistryKey getRegistryKey() {
        return DisputeStateMachineRegistryKey.builder()
                .disputeType(DisputeType.EDC_CHARGEBACK)
                .disputeStage(DisputeStage.FIRST_LEVEL)
                .disputeWorkflowVersion(DisputeWorkflowVersion.V2)
                .build();
    }

    @Override
    @SneakyThrows
    public void addTransitions(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
                .withExternal()
                .source(DisputeWorkflowState.RECEIVED)
                .target(DisputeWorkflowState.RECEIVED)
                .event(DisputeWorkflowEvent.CREATE_ENTRY)
                .action(createEntryAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
                .target(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .event(DisputeWorkflowEvent.COMPLETE_ACCEPTANCE)
                .action(acceptDisputeAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.ACCEPTANCE_COMPLETED)
                .target(DisputeWorkflowState.DEBIT_RECEIVED)
                .event(DisputeWorkflowEvent.RECEIVE_DEBIT)
                .action(debitDisputeAction)
                .and()

                /* DEBIT_RECEIVED, PARTIAL_DEBIT_RECEIVED and REPRESENTMENT_COMPLETED are terminal states in PG FL
                 * after these, flow will be automated. Following transitions are for that automated flow
                 */
                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.HOLD)
                .event(DisputeWorkflowEvent.HOLD)
                .action(updateDisputeStateAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.REPRESENTMENT_COMPLETED)
                .target(DisputeWorkflowState.END)
                .event(DisputeWorkflowEvent.END_WORKFLOW)
                .action(updateDisputeStateAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.DEBIT_RECEIVED)
                .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD)
                .action(holdReversalAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.DEBIT_RECEIVED)
                .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_HOLD_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_HOLD_EVENT)
                .action(raiseHoldRecoveryReversalAccountingEventAction)
                .and()

                .withInternal()
                .source(DisputeWorkflowState.DEBIT_RECEIVED)
                .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
                .action(recoverChargebackAction)
                .and()

                .withExternal()
                .source(DisputeWorkflowState.DEBIT_RECEIVED)
                .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
                .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
                .action(raiseAccountingEventAction)
                .and();

    }
}
