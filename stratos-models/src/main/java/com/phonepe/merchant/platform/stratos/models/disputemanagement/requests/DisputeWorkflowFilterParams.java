package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeWorkflowFilterParams {
    @NotBlank
    private String merchantId;
    @Valid
    private DateRangeParam dateRange;
    private String state;
    private DisputeStageDto stage;
    private String transactionId;
    private int limit;
    private int offset;
}
