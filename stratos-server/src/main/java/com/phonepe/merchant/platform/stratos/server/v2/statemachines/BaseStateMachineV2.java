package com.phonepe.merchant.platform.stratos.server.v2.statemachines;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.StateChangeListener;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners.TerminalStateListener;
import com.phonepe.merchant.platform.stratos.server.v2.workflowconfigurators.*;
import lombok.SneakyThrows;
import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;
import java.util.EnumSet;

@Singleton
public abstract class BaseStateMachineV2 extends DisputeStateMachine {

    private final ReverseHoldRecoverCBConfigurator reverseHoldRecoverCBConfigurator;
    private final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator;
    private final MerchantCommunicationConfigurator merchantCommunicationConfigurator;
    private final FraudDetectionConfigurator fraudDetectionConfigurator;
    private final FraudCheckDisputeAction fraudCheckDisputeAction;
    private final CancelWorkflowConfigurator cancelWorkflowConfigurator;
    private final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener;
    private final StateChangeListener<DisputeWorkflowState, DisputeWorkflowEvent> stateChangeListener;

    @Inject
    @SuppressWarnings("java:S107")
    protected BaseStateMachineV2(
             final TransitionLockCommand transitionLockCommand,
             final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
             final ReverseHoldRecoverCBConfigurator reverseHoldRecoverCBConfigurator,
             final ProactiveHoldWorkflowConfigurator proactiveHoldWorkflowConfigurator,
             final MerchantCommunicationConfigurator merchantCommunicationConfigurator,
             final FraudDetectionConfigurator fraudDetectionConfigurator,
             final FraudCheckDisputeAction fraudCheckDisputeAction,
             final CancelWorkflowConfigurator cancelWorkflowConfigurator,
             final TerminalStateListener<DisputeWorkflowState, DisputeWorkflowEvent> terminalStateListener,
             final StateChangeListener<DisputeWorkflowState, DisputeWorkflowEvent> stateChangeListener) {
        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.reverseHoldRecoverCBConfigurator = reverseHoldRecoverCBConfigurator;
        this.proactiveHoldWorkflowConfigurator = proactiveHoldWorkflowConfigurator;
        this.merchantCommunicationConfigurator = merchantCommunicationConfigurator;
        this.fraudDetectionConfigurator = fraudDetectionConfigurator;
        this.fraudCheckDisputeAction = fraudCheckDisputeAction;
        this.cancelWorkflowConfigurator = cancelWorkflowConfigurator;
        this.terminalStateListener = terminalStateListener;
        this.stateChangeListener = stateChangeListener;
    }

    @Override
    @SneakyThrows
    protected void configure(StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
                .withConfiguration()
                .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states.withStates()
                .initial(DisputeWorkflowState.RECEIVED)
                .states(EnumSet.allOf(DisputeWorkflowState.class))
                .stateEntry(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED, fraudCheckDisputeAction)
                .end(DisputeWorkflowState.END);
    }

    @Override
    @SneakyThrows
    protected void configure(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        proactiveHoldWorkflowConfigurator.configure(transitions);
        merchantCommunicationConfigurator.configure(transitions);
        reverseHoldRecoverCBConfigurator.configure(transitions);
        fraudDetectionConfigurator.configure(transitions);
        cancelWorkflowConfigurator.configure(transitions);
        addTransitions(transitions);
    }
    @Override
    protected void addStateListeners(StateMachine<DisputeWorkflowState, DisputeWorkflowEvent> stateMachine) {
        stateMachine.addStateListener(eventNotAcceptedListener);
        stateMachine.addStateListener(terminalStateListener);
        stateMachine.addStateListener(stateChangeListener);
    }

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();

    public abstract void addTransitions(StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions);
}
