package com.phonepe.central.stratos.notification.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.Collections;

@ExtendWith(MockitoExtension.class)
public class PenaltyDisbursedNotificationTest {

    @Mock
    private Penalty penalty;

    @Mock
    private PenaltyDisbursement disbursement;

    @Mock
    private ChangeNotificationVisitor<String> visitor;

    @InjectMocks
    private PenaltyDisbursedNotification notification;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        notification= PenaltyDisbursedNotification.builder()
                .penalty(penalty)
                .disbursements(Collections.singletonList(disbursement))
                .build();
    }

    @Test
    public void testPenaltyDisbursedNotificationCreation() {
        assertNotNull(notification);
        assertEquals(penalty, notification.getPenalty());
        assertEquals(1, notification.getDisbursements().size());
        assertEquals(disbursement, notification.getDisbursements().get(0));
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(any(PenaltyDisbursedNotification.class))).thenReturn("Visited");

        String result = notification.accept(visitor);
        assertEquals("Visited", result);

        verify(visitor).visit(notification);
    }
}
