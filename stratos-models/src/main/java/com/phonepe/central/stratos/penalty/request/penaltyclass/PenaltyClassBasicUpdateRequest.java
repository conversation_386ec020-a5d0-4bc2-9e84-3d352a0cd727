package com.phonepe.central.stratos.penalty.request.penaltyclass;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.phonepe.central.stratos.penalty.escalation.EscalationMatrix;
import com.phonepe.central.stratos.penalty.meta.PenaltyClassState;
import javax.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@JsonPropertyOrder({"type", "penaltyClassId", "name", "description", "state", "escalationMatrix"})
public class PenaltyClassBasicUpdateRequest extends PenaltyClassUpdateRequest {


    private String name;

    private String description;

    private PenaltyClassState state;

    @Valid
    private EscalationMatrix escalationMatrix;

    @Builder
    @JsonCreator
    public PenaltyClassBasicUpdateRequest(@JsonProperty("penaltyClassId") String penaltyClassId,
                                          @JsonProperty("name") String name,
                                          @JsonProperty("description") String description,
                                          @JsonProperty("state") PenaltyClassState state,
                                          @JsonProperty("escalationMatrix") EscalationMatrix escalationMatrix) {
        super(PenaltyClassUpdateType.BASIC_UPDATE, penaltyClassId);
        this.name = name;
        this.description = description;
        this.state = state;
        this.escalationMatrix = escalationMatrix;
    }

    @Override
    public <T> T accept(PenaltyClassUpdateRequestVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
