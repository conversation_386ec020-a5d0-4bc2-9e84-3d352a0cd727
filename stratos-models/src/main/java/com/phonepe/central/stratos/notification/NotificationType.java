package com.phonepe.central.stratos.notification;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum NotificationType {
    PENALTY_PROBABLE_IDENTIFIED("") {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitProbableIdentified();
        }
    },
    PENALTY_DUE_SHORTLY("") {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitPenaltyDueShortly();
        }
    },
    PENALTY_CREATED("") {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitPenaltyCreated();
        }
    },
    PENALTY_GROWING("") {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitPenaltyGrowing();
        }
    },
    PENALTY_DISBURSED("") {
        @Override
        public <T> T accept(Visitor<T> visitor) {
            return visitor.visitPenaltyDisbursed();
        }
    };

    public static final String PENALTY_PROBABLE_IDENTIFIED_TEXT = "PENALTY_PROBABLE_IDENTIFIED";
    public static final String PENALTY_DUE_SHORTLY_TEXT = "PENALTY_DUE_SHORTLY";
    public static final String PENALTY_CREATED_TEXT = "PENALTY_CREATED";
    public static final String PENALTY_GROWING_TEXT = "PENALTY_GROWING";
    public static final String PENALTY_DISBURSED_TEXT = "PENALTY_DISBURSED";

    @Getter
    private String value;

    public abstract <T> T accept(Visitor<T> visitor);

    public interface Visitor<T> {
        T visitProbableIdentified();

        T visitPenaltyDueShortly();

        T visitPenaltyCreated();

        T visitPenaltyGrowing();

        T visitPenaltyDisbursed();
    }

}
