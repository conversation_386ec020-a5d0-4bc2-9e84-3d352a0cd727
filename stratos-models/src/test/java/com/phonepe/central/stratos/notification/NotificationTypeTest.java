package com.phonepe.central.stratos.notification;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.assertEquals;

public class NotificationTypeTest {

    @Test
    public void testEnumValuesExist() {
        NotificationType[] notificationType = NotificationType.values();
        assertEquals(5,notificationType.length);
        assertEquals(NotificationType.PENALTY_PROBABLE_IDENTIFIED,notificationType[0]);
        assertEquals(NotificationType.PENALTY_DUE_SHORTLY,notificationType[1]);
        assertEquals(NotificationType.PENALTY_CREATED,notificationType[2]);
        assertEquals(NotificationType.PENALTY_GROWING,notificationType[3]);
        assertEquals(NotificationType.PENALTY_DISBURSED,notificationType[4]);
    }

    @Test
    public void test_MethodCalls_VisitorInterface() {
        NotificationType.Visitor<String> visitorMock = new TestNotificationTypeVisitorMock();

        assertEquals("Probable Identified", NotificationType.PENALTY_PROBABLE_IDENTIFIED.accept(visitorMock));

        assertEquals("Penalty Due Shortly", NotificationType.PENALTY_DUE_SHORTLY.accept(visitorMock));

        assertEquals("Penalty Created", NotificationType.PENALTY_CREATED.accept(visitorMock));

        assertEquals("Penalty Growing", NotificationType.PENALTY_GROWING.accept(visitorMock));

        assertEquals("Penalty Disbursed", NotificationType.PENALTY_DISBURSED.accept(visitorMock));

    }

    static class TestNotificationTypeVisitorMock implements NotificationType.Visitor<String>
    {

        @Override
        public String visitProbableIdentified() {
            return "Probable Identified";
        }

        @Override
        public String visitPenaltyDueShortly() {
            return "Penalty Due Shortly";
        }

        @Override
        public String visitPenaltyCreated() {
            return "Penalty Created";
        }

        @Override
        public String visitPenaltyGrowing() {
            return "Penalty Growing";
        }

        @Override
        public String visitPenaltyDisbursed() {
            return "Penalty Disbursed";
        }
    }
}
