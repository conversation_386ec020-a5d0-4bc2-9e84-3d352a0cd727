package com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.StratosConfiguration;
import io.appform.dropwizard.sharding.DBShardingBundle;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
public class SOHMetadataRepository extends DisputeMetadataRepository {
    @Inject
    public SOHMetadataRepository(
            DBShardingBundle<StratosConfiguration> dbShardingBundle) {
        super(dbShardingBundle);
    }
}
