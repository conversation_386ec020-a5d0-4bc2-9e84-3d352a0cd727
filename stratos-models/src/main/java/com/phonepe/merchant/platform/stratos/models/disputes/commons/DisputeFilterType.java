package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import lombok.experimental.UtilityClass;

public enum DisputeFilterType {
    DATE_RANGE,
    STATE_DATE_RANGE,
    MERCHANT_ID_DATE_RANGE,
    MERCHANT_TRANSACTION_ID,
    TRANSACTION_REFERENCE_ID,
    FILE_NAME,
    COMMUNICATION_ID,
    INSTRUMENT_TRANSACTION_ID;

    @UtilityClass
    public static final class Names {

        public static final String DATE_RANGE = "DATE_RANGE";
        public static final String STATE_DATE_RANGE = "STATE_DATE_RANGE";
        public static final String MERCHANT_ID_DATE_RANGE = "MERCHANT_ID_DATE_RANGE";
        public static final String MERCHANT_TRANSACTION_ID = "MERCHANT_TRANSACTION_ID";
        public static final String TRANSACTION_REFERENCE_ID = "TRANSACTION_REFERENCE_ID";
        public static final String FILE_NAME = "FILE_NAME";
        public static final String COMMUNICATION_ID = "COMMUNICATION_ID";
        public static final String INSTRUMENT_TRANSACTION_ID = "INSTRUMENT_TRANSACTION_ID";
    }
}
