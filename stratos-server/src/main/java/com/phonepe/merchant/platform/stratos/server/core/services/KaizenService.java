package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.merchant.platform.stratos.models.kaizen.KaizenKey;
import com.phonepe.merchant.platform.stratos.server.core.models.TokenResponse;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.stratos.kaizen.models.data.ActionStatus;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import com.phonepe.verified.kaizen.storage.mariadb.entities.StoredActionMetadata;

import java.util.List;
import java.util.Map;
import java.util.Optional;

public interface KaizenService {
    Optional<StoredActionMetadata> uploadEvidence(String disputeWorkflowId, String documentId, Map<String, Object> metadata);
    List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId);
    List<StoredDocumentUploadWithMetaDataActionMetadata> getAllEvidences(String kaizenWorkflowId, ActionStatus actionStatus);
    Optional<StoredActionMetadata> deleteEvidence(String actionId);
    void validateTakeActionRequest(String disputeWorkflowId);
    GenericResponse<TokenResponse> getAccessToken(String disputeWorkflowId);
    String getValueForKey(String kaizenWorkflowId, KaizenKey key);
    void updateCommunicationId(String disputeWorkflowId, String transactionReferenceId, String kaizenWorkflowId);
}