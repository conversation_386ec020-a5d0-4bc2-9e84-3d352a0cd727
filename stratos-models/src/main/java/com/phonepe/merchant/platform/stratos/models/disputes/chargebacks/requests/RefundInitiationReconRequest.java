package com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.ReconcileTypeVisitor;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.DisputeReconcileRequest;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class RefundInitiationReconRequest  extends DisputeReconcileRequest {
    public RefundInitiationReconRequest() {
        super(ReconcileType.REFUND_INITIATION_RECONCILE);
    }
    @Override
    public <T> T accept(ReconcileTypeVisitor<T> visitor) {
        return visitor.visit(this);
    }
}