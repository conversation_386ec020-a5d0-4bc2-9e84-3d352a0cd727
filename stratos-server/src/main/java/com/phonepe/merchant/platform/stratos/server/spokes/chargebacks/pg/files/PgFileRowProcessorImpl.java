package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.files;

import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.DisputeWorkflowStateConfig;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.PgTransportService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.StorageUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.BaseFileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PartyMerchantIdVisitor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.Optional;
import java.util.Set;


@Slf4j
public class PgFileRowProcessorImpl extends BaseFileRowProcessor<TransactionDetail> {

    private final DisputeService disputeService;
    private final PaymentsService paymentsService;
    private final PgTransportService pgTransportService;
    private final Map<DisputeType, Map<DisputeStage, Set<DisputeWorkflowState>>> disputeWorkflowEndingStates;
    private final IdHelper idHelper;
    private final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap;

    @Inject
    public PgFileRowProcessorImpl(final HandleBarsService handleBarsService,
                                  final DisputeService disputeService,
                                  final PaymentsService paymentsService,
                                  final MerchantMandateService merchantMandateService,
                                  final PgTransportService pgTransportService,
                                  final DisputeWorkflowStateConfig disputeWorkflowStateConfig,
                                  final IdHelper idHelper,
                                  final Map<DisputeType, DisputeWorkflowVersion> disputeWorkflowVersionMap) {
        super(handleBarsService, disputeService, merchantMandateService);
        this.disputeService = disputeService;
        this.paymentsService = paymentsService;
        this.pgTransportService = pgTransportService;
        this.disputeWorkflowEndingStates = disputeWorkflowStateConfig.getDisputeWorkflowEndingStates();
        this.idHelper = idHelper;
        this.disputeWorkflowVersionMap = disputeWorkflowVersionMap;
    }

    private void validateFirstLevelDisputeWorkflowEnded(final String transactionReferenceId) {
        final var firstLevelDisputeWorkflow = disputeService.getDisputeWorkflow(
            transactionReferenceId,
            DisputeType.PG_CHARGEBACK,
            DisputeStage.FIRST_LEVEL
        );
        final Set<DisputeWorkflowState> firstLevelDisputeWorkflowEndingStates = disputeWorkflowEndingStates
            .get(DisputeType.PG_CHARGEBACK).get(DisputeStage.FIRST_LEVEL);

        if (firstLevelDisputeWorkflow.getDisputeWorkflowVersion().equals(DisputeWorkflowVersion.V1) &&
            !firstLevelDisputeWorkflowEndingStates.contains(
            firstLevelDisputeWorkflow.getCurrentState())) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED,
                Map.of(
                    Constants.MESSAGE,
                    "First Level Dispute Workflow not ended yet",
                    Constants.TRANSACTION_ID,
                    transactionReferenceId)
            );
        }
    }

    @Override
    public DisputeWorkflow enrichAndGetDisputeWorkflow(final TransactionDetail transactionDetails,
        final FinancialDisputeWorkflow disputeWorkflowFilePojo,
        final String fileId, final DisputeType disputeType,
        final FileConfig fileConfig, final String disputeId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        final var ttlInDays = Optional.ofNullable(disputeWorkflowFilePojo.getDisputeStage())
            .map(disputeStage -> fileConfig.getDisputeStageTTLDaysMap()
                .get(disputeStage))
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                Map.of(Constants.MESSAGE,
                    "Invalid TTL config for disputeStage: "
                        + disputeWorkflowFilePojo.getDisputeStage())));

        return FinancialDisputeWorkflow.builder()
            .key(StorageUtils.primaryKey())
            .disputeWorkflowId(idHelper.disputeWorkflowId(receivedPayment.getTransactionId()))
            .disputeWorkflowVersion(disputeWorkflowVersionMap.get(disputeType))
            .disputeSourceType(SourceType.FILE)
            .disputeSourceId(fileId)
            .transactionReferenceId(receivedPayment.getTransactionId())
            .disputeType(disputeType)
            .disputeStage(disputeWorkflowFilePojo.getDisputeStage())
            .currentEvent(disputeWorkflowFilePojo.getCurrentEvent())
            .currentState(disputeWorkflowFilePojo.getCurrentState())
            .disputedAmount(disputeWorkflowFilePojo.getDisputedAmount())
            .raisedAt(disputeWorkflowFilePojo.getRaisedAt())
            .respondBy(disputeWorkflowFilePojo.getRaisedAt().plusDays(ttlInDays))
            .gandalfUserId(Constants.STRATOS_SYSTEM_USER_OLYMPUS.getUserDetails().getUserId())
            .userType(UserType.SYSTEM)
            .disputeId(disputeId)
            .build();
    }

    @SneakyThrows
    private Dispute enrichDisputeDetails(final TransactionDetail transactionDetails,
        final Dispute disputeFilePojo, final DisputeType disputeType, final String pgTxnId) {

        final var receivedPayment = transactionDetails.getReceivedPayment();

        // Merchant details
        final String merchantId = receivedPayment.getTo()
            .accept(new PartyMerchantIdVisitor(receivedPayment));

        return Dispute.builder()
            .key(StorageUtils.primaryKey())
            .disputeId(idHelper.disputeId(receivedPayment.getTransactionId()))
            .transactionReferenceId(receivedPayment.getTransactionId())
            .disputeType(disputeType)
            .currentDisputeStage(disputeFilePojo.getCurrentDisputeStage())
            .merchantId(merchantId)
            .merchantTransactionId(receivedPayment.getContext()
                .visit(new PaymentContextMerchantTransactionIdVisitor()))
            .disputeReferenceId(disputeFilePojo.getDisputeReferenceId())
            .instrumentTransactionId(disputeFilePojo.getInstrumentTransactionId())
            .rrn(disputeFilePojo.getRrn())
            .transactionAmount(getPgAmount(transactionDetails.getSentPayment(), pgTxnId))
            .disputeCategory(disputeFilePojo.getDisputeCategory())
            .disputeIssuer(disputeFilePojo.getDisputeIssuer())
            .build();
    }


    private long getPgAmount(final SentPayment sentPayment, final String pgTxnId) {
        return sentPayment.getPaidFrom()
            .stream()
            .filter(paymentInstrument -> Constants.CHARGEBACK_INSTRUMENT_MAP.get(
                DisputeType.PG_CHARGEBACK).contains(paymentInstrument.getType()))
            .map(PaymentInstrument::getAmount)
            .findFirst()
            .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSACTION,
                Map.of(Constants.MESSAGE, "No Pg Txn for pgTxnId " + pgTxnId)
            ));
    }

    @Override
    public TransactionDetail getTransactionDetails(final String instrumentTransactionId,
        final FileRowMeta fileRowMeta) {
        final var paymentsTxnId = pgTransportService.getPaymentsIdFromPgTxnId(
            instrumentTransactionId);
        return paymentsService.transactionDetailFromOriginalTransactionId(
            paymentsTxnId);
    }

    @Override
    public Dispute getDispute(final DisputeWorkflow disputeWorkflowFilePojo,
        final TransactionDetail transactionDetails,
        final Dispute disputeFilePojo, final DisputeType disputeType,
        final String instrumentTransactionId) {
        return disputeWorkflowFilePojo.getDisputeStage()
            .accept(new DisputeStageVisitor<>() {

                @Override
                public Dispute visitFirstLevel() {
                    return enrichDisputeDetails(transactionDetails, disputeFilePojo, disputeType,
                        instrumentTransactionId);
                }

                @Override
                public Dispute visitPreArbitration() {
                    validateFirstLevelDisputeWorkflowEnded(
                        transactionDetails.getSentPayment().getTransactionId());

                    final Dispute dispute = disputeService.getDispute(
                        transactionDetails.getSentPayment().getTransactionId(),
                        disputeType,
                        DisputeStage.FIRST_LEVEL);
                    dispute.setCurrentDisputeStage(DisputeStage.PRE_ARBITRATION);
                    return dispute;
                }

                @Override
                public Dispute visitPreChargeback() {
                    return null;
                }
            });
    }
}
