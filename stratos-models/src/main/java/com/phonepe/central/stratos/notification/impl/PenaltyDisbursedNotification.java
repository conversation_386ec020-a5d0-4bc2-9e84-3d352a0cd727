package com.phonepe.central.stratos.notification.impl;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.notification.ChangeNotitification;
import com.phonepe.central.stratos.notification.NotificationType;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.disbursement.PenaltyDisbursement;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PenaltyDisbursedNotification extends ChangeNotitification {
    @Valid
    @NotNull
    private Penalty penalty;
    @Valid
    @NotEmpty
    private List<PenaltyDisbursement> disbursements;

    @Builder
    @JsonCreator
    public PenaltyDisbursedNotification(@JsonProperty("penalty") final Penalty penalty,
            @JsonProperty("disbursements") final List<PenaltyDisbursement> disbursements) {
        super(NotificationType.PENALTY_DISBURSED);
        this.penalty = penalty;
        this.disbursements = disbursements;
    }

    @Override
    public <T> T accept(ChangeNotificationVisitor<T> visitor) {
        return visitor.visit(this);
    }

}
