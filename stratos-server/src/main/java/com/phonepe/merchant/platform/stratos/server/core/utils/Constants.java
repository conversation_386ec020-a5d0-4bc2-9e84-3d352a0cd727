package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.merchant.platform.stratos.models.commons.contexts.EmptyTransitionContext;
import com.phonepe.merchant.platform.stratos.server.StratosApplication;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.RowType;
import com.phonepe.models.payments.merchant.MerchantTransactionState;
import com.phonepe.models.payments.pay.instrument.PaymentInstrumentType;
import com.phonepe.olympus.im.models.user.SystemUserDetails;
import com.phonepe.olympus.im.models.user.UserAuthDetails;
import com.phonepe.platform.http.v2.executor.httpdata.StringHttpData;
import java.util.Map;
import java.util.Set;
import javax.ws.rs.core.MediaType;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang.StringUtils;

@UtilityClass
public class Constants {

    public final String MESSAGE = "message";
    public final String KEY = "key";
    public final String VALUE = "value";
    public final String SERVICE_NAME = "service";
    public final String COMMAND_NAME = "command";
    public final String URL = "url";
    public final String RESPONSE = "response";
    public final String CODE = "code";
    public static final int BLOCKED_ERROR_CODE = 429;
    public final String AUTHORIZATION = "Authorization";
    public final String AUTHORIZATION_FOR_ID = "x-authorized-for-id";
    public final String AUTHORIZATION_FOR_SUBJECT = "x-authorized-for-subject";
    public final String DEVICE_FINGERPRINT = "x-device-fingerprint";
    public static final String X_END_USER_AUTHORIZATION = "x-end-user-authorization";

    public final String TRANSACTION_ID = "transactionId";
    public final String MERCHANT_TRANSACTION_ID = "merchantTransactionId";
    public final String DISPUTE_TYPE = "disputeType";
    public final String PENALTY_CLASS_ID = "penalty class id";
    public final String ESCALATION_LEVEL = "escalation config";
    public final String ROW_ID = "rowId";
    public final String FILE_ID = "fileId";

    public final String UNSUPPORTED_OPERATION ="UNSUPPORTED_OPERATION";

    public final String ENUM_CLASS_LIST= "EnumCLassList";
    public final String TRANSACTION = "transaction";
    public final String COMMENT_SUFFIX = "_COMMENT";
    public final String CHARGEBACK_RECOVERY_EVENT = "CHARGEBACK_RECOVERY_EVENT";
    public final String CHARGEBACK_PENALTY_RECOVERY_EVENT = "CHARGEBACK_PENALTY_RECOVERY_EVENT";
    public final String CHARGEBACK_RECOVERY_REVERSAL_EVENT = "CHARGEBACK_RECOVERY_REVERSAL_EVENT";
    public final String HOLD_RECOVERY_EVENT = "HOLD_RECOVERY_EVENT";
    public final String HOLD_RECOVERY_REVERSAL_EVENT = "HOLD_RECOVERY_REVERSAL_EVENT";
    public final String ORIGINAL_TXN_ID = "originalTransactionId";
    public final String MERCHANT_ID = "merchantId";
    public final String PAYMENT_RESPONSE_CODE = "paymentResponseCode";
    public final String MERCHANT_ORDER_ID = "merchantOrderId";
    public final String TERMINAL_ID = "terminalId";
    public final String RRN = "rrn";
    public final String SUCCESS = "SUCCESS";
    public final String FAILURE = "FAILURE";
    public final String TENANT = "tenant";
    public final String P2P_MCC = "0000";
    public final String EXTERNAL_MERCHANT_PAYMENT_FLOW = "CONSUMER_TO_EXTERNAL";

    public final String END_USER_TOKEN = "End User Token";



    public final int ALLOWED_MAX_COMMENT_LENGTH = 512;

    public final String MEDIA_TYPE_CSV = "text/csv";

    public final String DEFAULT_BIN = "default";

    public final String MESOS_HOSTNAME = StringUtils
        .defaultIfBlank(System.getenv("HOST"), "UNKNOWN");
    public final String MESOS_TASK_ID = StringUtils
        .defaultIfBlank(System.getenv("MESOS_TASK_ID"), "UNKNOWN");

    public final StringHttpData EMPTY_HTTP_DATA = new StringHttpData(MediaType.APPLICATION_JSON,
        "");

    public final UserAuthDetails STRATOS_SYSTEM_USER_OLYMPUS = UserAuthDetails.builder()
            .userDetails(
                    SystemUserDetails.builder()
                            .userId(StratosApplication.APP_NAME)
                            .build()
            ).build();

    public final EmptyTransitionContext EMPTY_TRANSITION_CONTEXT = EmptyTransitionContext.builder()
        .build();

    public final String SPLIT_MULTIVALUE_SEPARATOR = ", ";

    public final Map<DisputeType, Set<PaymentInstrumentType>> CHARGEBACK_INSTRUMENT_MAP = Map.of(
        DisputeType.UPI_CHARGEBACK,
        Set.of(PaymentInstrumentType.EXTERNAL_VPA, PaymentInstrumentType.ACCOUNT),
        DisputeType.PG_CHARGEBACK,
        Set.of(PaymentInstrumentType.DEBIT_CARD, PaymentInstrumentType.CREDIT_CARD),
        DisputeType.UDIR_OUTGOING_COMPLAINT,
        Set.of(PaymentInstrumentType.ACCOUNT),
        DisputeType.NB_CHARGEBACK,
        Set.of(PaymentInstrumentType.NET_BANKING)
    );

    public final Set<DisputeWorkflowState> KRATOS_SKIP_VALIDATION_STATES = Set.of(DisputeWorkflowState.RECEIVED);
    public final Set<DisputeWorkflowState> ACTIONABLE_SUSPECTED_STATES = Set.of(
        DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK, DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED, DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL);

    public final Map<DisputeType, RowType> CHARGEBACK_ROW_TYPE_MAP = Map.of(
        DisputeType.UPI_CHARGEBACK, RowType.UPI_FILE_ROW,
        DisputeType.PG_CHARGEBACK, RowType.PG_FILE_ROW,
        DisputeType.EDC_CHARGEBACK, RowType.EDC_FILE_ROW,
        DisputeType.NB_CHARGEBACK, RowType.NB_FILE_ROW,
        DisputeType.WALLET_CHARGEBACK, RowType.WALLET_FILE_ROW,
        DisputeType.BBPS_TAT_BREACH_TOA, RowType.TOA_API_ROW,
        DisputeType.FRA_FRAUD, RowType.FRAUD_FILE_ROW);

    public final Set<DisputeWorkflowState> CANCELLED_DISPUTES = Set.of(
        DisputeWorkflowState.CHARGEBACK_CANCELLED);

    public final Set<DisputeWorkflowState> P2PM_TOA_RECONCILE_STATES = Set.of(DisputeWorkflowState.P2PM_TOA_PENDING);
    public final Set<DisputeWorkflowState> P2PM_TOA_RETRY_STATES = Set.of(DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
        DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY, DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS);

    public final Set<DisputeWorkflowState> P2PM_TOA_COMPLETE_EXTERNALLY_STATES = Set.of(DisputeWorkflowState.P2PM_TOA_INITIATION_FAILED,
        DisputeWorkflowState.P2PM_TOA_FAILED_AFTER_MAX_AUTO_RETRY, DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS);

    public final Set<DisputeWorkflowState> TOA_RECONCILE_STATES = Set.of(DisputeWorkflowState.TOA_PENDING);

    public final Set<DisputeWorkflowState> TOA_RETRY_STATES = Set.of(DisputeWorkflowState.TOA_INITIATION_FAILED,
            DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY, DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS);

    public final Set<DisputeWorkflowState> TOA_COMPLETE_EXTERNALLY_STATES = Set.of(DisputeWorkflowState.TOA_INITIATION_FAILED,
            DisputeWorkflowState.TOA_FAILED_AFTER_MAX_AUTO_RETRY, DisputeWorkflowState.TOA_BLOCKED_DUE_TO_KS);

    public final Set<MerchantTransactionState> TXN_FAILED_STATES = Set.of(MerchantTransactionState.FAILED, MerchantTransactionState.CANCELLED);

    public final Set<DisputeWorkflowEvent> TOA_KS_PERMISSIBLE_EVENTS = Set.of(DisputeWorkflowEvent.CREATE_ENTRY,DisputeWorkflowEvent.TOA_OPENED_TO_INITIATED);
    public final String TOA_BREACH_EMAIL_TEMPLATE = """
        Hi Team,
        <br/>
        <br/>
        TOA Threshold breached for date: %s
        <br/>
        THRESHOLD AMOUNT: Rs. %.2f
        <br/>
        BREACH AMOUNT:    Rs. %.2f
        <br/>
        DIFF AMOUNT:      Rs. %.2f
        <br/>
        <br/>
        Please apply Kill switch if there seems to be an anomaly.
        <br/>
        <br/>
        Thanks,
        <br/>
        Stratos Dev Team
        """;

    public final String PG_TRANSPORT_CLIENT_ID= "pg-transport";

    public final String PLUTUS_TRANSACTION_STATUS_CLIENT_ID = "plutus-transaction-status";

    public final String PLUTUS_EVENTS_INGESTION_CLIENT_ID = "plutus-events-ingestion";

    public final String MERCHANT_SERVICE_CLIENT_ID = "merchant-service";

    public final String MERCHANT_MANDATE_CLIENT_ID = "MandateClient";

    public final String DOCSTORE_CLIENT_ID = "stratosDocstore";

    public final String PAYMENTS_TXNL_CLIENT_ID = "payments-txnl";

    public final String NEXUS_CLIENT_ID = "nexusClient";

    public final String KILLSWITCH_CLIENT_ID = "killswitches";

    public final String ZENCAST_CLIENT_ID = "StratosZencastClient";

    public final String EDC_CLIENT_ID = "edcServiceClient";


    public final String NETPE_CLIENT_ID = "netpeServiceClient";

    public final String RO_CLIENT_ID = "refund-orchestrator";
    public final String WARDEN_CLIENT_ID = "warden";
    public final String PHONEPETOA_MID = "PHONEPETOA";
    public final String STRATOS_EVIDENCE_UPLOAD = "STRATOS_EVIDENCE_UPLOAD";
    public final String STRATOS_SYSTEM_USER = "stratos";
    public static final String BBPS_MERCHANT_ID = "BBPSBP";
    public static final String BBPS_TOA_MERCHANT_TXN_ID_PREFIX = "BBPSTOA";
    public static final String BBPS_EGV_TOA_PROGRAM_ID = "TOA_VO";
    public static final String UPI_ACCOUNT_TOA_CONTEXT_MESSAGE = "UPI account payment for BBPS refund TAT breach TOA " +
            "as part of BBPS harmonization, processed by Stratos";
    public static final String EGV_TOA_CONTEXT_MESSAGE = "EGV payment for BBPS refund TAT breach TOA as part of BBPS " +
            "harmonization, processed by Stratos";
    public final String TRANSACTION_TYPE = "transactionType";
    public final String WALLET_CHARGEBACK = "payments-txnl-ppi";
    public final String APPLICATION_JSON = "application/json; " + "charset=utf-8";
    public static final String CSV_FILE_TYPE = "csv";
}
