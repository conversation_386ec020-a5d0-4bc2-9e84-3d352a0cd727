package com.phonepe.merchant.platform.stratos.models.disputes.commons;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisputeData implements Serializable{

    @NotNull
    private DisputeType disputeType;
    @NotNull
    private DisputeStage disputeStage;
    @NotNull
    private DisputeCategory disputeCategory;
}
