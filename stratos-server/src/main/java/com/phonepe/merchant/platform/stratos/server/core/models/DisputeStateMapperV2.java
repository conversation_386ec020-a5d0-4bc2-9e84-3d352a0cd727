package com.phonepe.merchant.platform.stratos.server.core.models;

import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.DisputeStatus;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Singleton
@NoArgsConstructor
public class DisputeStateMapperV2 extends DisputeStateMapper {

    @Override
    public DisputeStatus visitRgcsAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitCreditReceived(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRefundBlocked(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverChargebackEventAccepted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPgRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitPgPartialRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitChargebackCancelled(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRepresentedCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitAcceptanceCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitFraudRejected(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitFraudRepresentmentCompleted(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitHold(DisputeWorkflow dw) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitEnd(DisputeWorkflow dw) {
        long acceptedAmount = 0;
        long disputedAmount = dw.getDisputedAmount();
        if(dw instanceof FinancialDisputeWorkflow)
            acceptedAmount = ((FinancialDisputeWorkflow) dw).getAcceptedAmount();
        if(acceptedAmount != disputedAmount){
            return DisputeStatus.REJECTED;
        }
        return DisputeStatus.ACCEPTED;
    }
    @Override
    public DisputeStatus visitRecoverHoldEventRaised(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverHoldEventAccepted(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredHoldEventRaised(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitReversalOfRecoveredHoldEventAccepted(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }
    @Override
    public DisputeStatus visitRecoverPartialHoldEventRaised(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

    @Override
    public DisputeStatus visitRecoverPartialHoldEventAccepted(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }
    @Override
    public DisputeStatus visitInvalidEvidences(DisputeWorkflow context) {
        return DisputeStatus.UNDER_REVIEW;
    }

}
