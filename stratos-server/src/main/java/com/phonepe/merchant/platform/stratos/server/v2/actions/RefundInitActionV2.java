package com.phonepe.merchant.platform.stratos.server.v2.actions;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.helpers.id.IdHelper;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.UpdateDisputeStateBaseAction;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.RefundUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FoxtrotEventUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.models.DisputedRefund;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Singleton
public class RefundInitActionV2 extends UpdateDisputeStateBaseAction {

    private final RefundService refundService;
    private final PaymentsService paymentsService;
    private final EventIngester eventIngester;
    private final IdHelper idHelper;
    protected DisputedRefund disputedRefund;


    @Inject
    @SuppressWarnings("java:S107")
    public RefundInitActionV2(
            DisputeService disputeService,
            DisputeWorkflowRepository disputeWorkflowRepository,
            RefundService refundService,
            PaymentsService paymentsService,
            EventIngester eventIngester,
            final CallbackActor callbackActor,
            IdHelper idHelper) {
        super(disputeService, disputeWorkflowRepository, eventIngester, callbackActor);
        this.refundService = refundService;
        this.paymentsService = paymentsService;
        this.idHelper = idHelper;
        this.eventIngester = eventIngester;
    }

    @Override
    protected void preTransition(final DisputeWorkflow disputeWorkflow,
                                 final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final String txnRefId = stateContext.getExtendedState()
                .get(Fields.transactionReferenceId, String.class);

        var dispute = disputeWorkflow.getDispute();

        try {
            Objects.requireNonNull(dispute);
            stateContext.getExtendedState().getVariables().put(Dispute.class, dispute);
            ValidationUtils.validateDisputeId(disputeWorkflow, dispute);
            ValidationUtils.validateEntryDisputeAmount(disputeWorkflow, dispute,
                    disputeService);

        } catch (Exception exception) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.ROW_NOT_FOUND,
                    Map.of("Message", String.format(
                            "Dispute not found against DisputeWorkflowId %s, TransactionId %s",
                            disputeWorkflow.getDisputeWorkflowId(),
                            txnRefId)));
        }

    }

    @Override
    protected void transition(final DisputeWorkflow disputeWorkflow,
                              final StateContext<DisputeWorkflowState, DisputeWorkflowEvent> stateContext) {

        final var transactionId = stateContext.getExtendedState()
                .get(Fields.transactionReferenceId, String.class);

        final TransactionDetail transactionDetail = paymentsService.transactionDetailFromOriginalTransactionId(
                transactionId);

        final Dispute dispute = stateContext.getExtendedState().get(Dispute.class, Dispute.class);
        Objects.requireNonNull(dispute);
        Objects.requireNonNull(disputeWorkflow);

        FinancialDisputeWorkflow financialDisputeWorkflow = DisputeWorkflowUtils
                .getFinancialDisputeWorkflow(disputeWorkflow);

        Objects.requireNonNull(financialDisputeWorkflow);

        disputedRefund = RefundUtils.getDisputedRefundObject(dispute, disputeWorkflow,
                transactionDetail, transactionId,
                financialDisputeWorkflow.getAcceptedAmount());

        initiateRefund(disputedRefund);
        eventIngester.generateEvent(
            FoxtrotEventUtils.toRefundActionEvent(
                disputedRefund.getDisputeWorkflow().getDisputeWorkflowId(),
                disputedRefund.getTransactionId(), disputedRefund.getRefundId(),
                RefundStatus.INITIATED.name(), null, null)
        );

    }

    private void initiateRefund(DisputedRefund disputedRefund) {

        String refundId = idHelper.createRefundId(disputedRefund.getTransactionId(),
                disputedRefund.getDisputeWorkflow().getDisputeStage().ordinal());

        var response = refundService.intiateRefund(disputedRefund);

        if (!response.isSuccess() || response.getData().getStatus()
                .equals(RefundStatus.FAILED)) {

            log.error("Error occurred while initiating refund for txnId {}, error : {}",
                    disputedRefund.getDisputeWorkflow().getTransactionReferenceId(),
                    response.getData().getErrorContext());

            paymentsService.blockReversals(disputedRefund.getTransactionId());

            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INTERNAL_SERVER_ERROR,
                    Map.of("error", response.getData().getErrorContext()));

        } else {

            log.info("Refund initiated Successfully for workflow {} with refundId {}",
                    disputedRefund.getDisputeWorkflow().getDisputeWorkflowId(), refundId);

            refundService.updateRefundId(disputedRefund.getDisputeWorkflow(),
                    refundId);
        }
    }

}
