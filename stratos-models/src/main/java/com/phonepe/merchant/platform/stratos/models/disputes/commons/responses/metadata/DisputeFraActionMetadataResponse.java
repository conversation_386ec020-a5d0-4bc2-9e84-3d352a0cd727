package com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DisputeFraActionMetadataResponse extends DisputeMetadataBaseResponse {

    private final String reasonCode;

    @NotEmpty
    private final String actionType;

    @Builder
    public DisputeFraActionMetadataResponse(
        final String transactionId, final String disputeWorkflowId,
        final String reasonCode, final String actionType ) {
        super(DisputeMetadataDto.FRA_ACTION_METADATA, transactionId,disputeWorkflowId);
        this.reasonCode = reasonCode;
        this.actionType = actionType;
    }
}
