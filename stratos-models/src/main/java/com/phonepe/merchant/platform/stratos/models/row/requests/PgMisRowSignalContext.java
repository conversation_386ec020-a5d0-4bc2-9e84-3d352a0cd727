package com.phonepe.merchant.platform.stratos.models.row.requests;

import com.phonepe.merchant.platform.stratos.models.row.RowTransactionType;
import com.phonepe.merchant.platform.stratos.models.row.RowSignalContextVisitor;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@SuppressWarnings("java:S2637")
@EqualsAndHashCode(callSuper = true)
public class PgMisRowSignalContext extends RowSignalContext {

    @NotNull
    private String transactionId;
    private String pgId;
    @NotNull
    private String pgTransactionId;
    private String bankTransactionId;
    private String instrumentType;
    @NotNull
    private RowTransactionType transactionType;
    private String mid;
    @NotNull
    private BigDecimal transactionAmount;
    private BigDecimal charges;
    private BigDecimal igst;
    private BigDecimal cgst;
    private BigDecimal sgst;
    @NotNull
    private BigDecimal netAmount;
    private Date transactionDate;
    private Date paymentDate;
    private boolean autoRefund;
    private String bankCode;
    private String interchange;

    public PgMisRowSignalContext() {
        super(RowTypeDto.PG_MIS_ROW);
    }

    @SuppressWarnings("java:S107")
    @Builder
    public PgMisRowSignalContext(final String transactionId,
        final String pgId,
        final String pgTransactionId,
        final String bankTransactionId,
        final String instrumentType,
        final RowTransactionType transactionType,
        final String mid,
        final BigDecimal transactionAmount,
        final BigDecimal charges,
        final BigDecimal igst,
        final BigDecimal cgst,
        final BigDecimal sgst,
        final BigDecimal netAmount,
        final Date transactionDate,
        final Date paymentDate,
        final boolean autoRefund,
        final String bankCode,
        final String interchange) {

        this();
        this.transactionId = transactionId;
        this.pgId = pgId;
        this.pgTransactionId = pgTransactionId;
        this.bankTransactionId = bankTransactionId;
        this.instrumentType = instrumentType;
        this.transactionType = transactionType;
        this.mid = mid;
        this.transactionAmount = transactionAmount;
        this.charges = charges;
        this.igst = igst;
        this.cgst = cgst;
        this.sgst = sgst;
        this.netAmount = netAmount;
        this.transactionDate = transactionDate;
        this.paymentDate = paymentDate;
        this.autoRefund = autoRefund;
        this.bankCode = bankCode;
        this.interchange = interchange;
    }

    @Override
    public <T> T accept(RowSignalContextVisitor<T> visitor) {
        return visitor.visit(this);
    }
}
