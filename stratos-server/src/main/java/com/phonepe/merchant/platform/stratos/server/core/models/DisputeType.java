package com.phonepe.merchant.platform.stratos.server.core.models;

import java.util.Arrays;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.UtilityClass;


/**
 * This entity is persisted in DB by it's Ordinal Value Hence only append at the end and do not
 * change Order of existing values while adding new values
 */

@Getter
@AllArgsConstructor
public enum DisputeType {
    UPI_CHARGEBACK {
        @Override
        public <T> T accept(final DisputeTypeVisitor<T> visitor) {
            return visitor.visitUpiChargeback();
        }
    },
    PG_CHARGEBACK {
        @Override
        public <T> T accept(final DisputeTypeVisitor<T> visitor) {
            return visitor.visitPgChargeback();
        }
    },
    UDIR_OUTGOING_COMPLAINT {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitUdirOutgoingComplaint();
        }
    },
    UDIR_INCOMING_COMPLAINT {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitUdirIncomingComplaint();
        }
    },

    P2PM_TOA {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitP2PMToa();
        }
    },
    EDC_CHARGEBACK {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitEdcChargeback();
        }
    },
    NB_CHARGEBACK {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitNetBankingChargeback();
        }
    },
    NOTIONAL_CREDIT_TOA {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitNotionalCreditToa();
        }
    },
    BBPS_TAT_BREACH_TOA {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitBbpsTatBreachToa();
        }
    },
    WALLET_CHARGEBACK {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitWalletChargeback();
        }
    },
    FRA_FRAUD {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitFraFraud();
        }
    }
    ;

    public abstract <T> T accept(DisputeTypeVisitor<T> visitor);

    public static final Set<DisputeType> CHARGEBACKS = new LinkedHashSet<>(  //NOSONAR
        Arrays.asList(UPI_CHARGEBACK, PG_CHARGEBACK, EDC_CHARGEBACK, NB_CHARGEBACK, WALLET_CHARGEBACK, FRA_FRAUD));

    public static final Set<DisputeType> NPCI_CHARGEBACKS = new LinkedHashSet<>(  //NOSONAR
        List.of(WALLET_CHARGEBACK));

    public static final Set<DisputeType> UDIR_COMPLAINTS = new LinkedHashSet<>(  //NOSONAR
        List.of(UDIR_OUTGOING_COMPLAINT));

    public static final Set<DisputeType> TOAS = new LinkedHashSet<>( //NOSONAR
            Arrays.asList(P2PM_TOA, NOTIONAL_CREDIT_TOA, BBPS_TAT_BREACH_TOA));

    @UtilityClass
    public static final class Names {

        public static final String UPI_CHARGEBACK = "UPI_CHARGEBACK";
        public static final String PG_CHARGEBACK = "PG_CHARGEBACK";
        public static final String EDC_CHARGEBACK = "EDC_CHARGEBACK";
        public static final String UDIR_OUTGOING_COMPLAINT = "UDIR_OUTGOING_COMPLAINT";
        public static final String NETBANKING_CHARGEBACK = "NB_CHARGEBACK";
        public static final String BBPS_TAT_BREACH_TOA = "BBPS_TAT_BREACH_TOA";
        public static final String WALLET_CHARGEBACK = "WALLET_CHARGEBACK";
        public static final String FRA_FRAUD = "FRA_FRAUD";
    }

}