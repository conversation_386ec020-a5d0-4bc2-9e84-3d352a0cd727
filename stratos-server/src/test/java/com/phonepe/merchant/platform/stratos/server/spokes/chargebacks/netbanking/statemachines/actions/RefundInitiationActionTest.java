package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.statemachines.actions;

import com.google.inject.Provider;
import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeRepository;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl.DisputeWorkflowRepository;
import com.phonepe.merchant.platform.stratos.server.core.queue.actors.CallbackActor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.RefundService;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.models.DisputedRefund;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.services.NetBankingChargebackService;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 */
@Slf4j
public class RefundInitiationActionTest extends ErrorConfiguratorBaseTest {

    @Mock
    private DisputeService disputeService;
    @Mock
    private DisputeWorkflowRepository disputeWorkflowRepository;
    @Mock
    private RefundService refundService;
    @Mock
    private PaymentsService paymentsService;
    @Mock
    private DisputeRepository disputeRepository;
    @Mock
    private NetBankingChargebackService netBankingChargebackService;
    @Mock
    private Provider<StateChangeHandlerActor> stateChangeHandlerProvider;
    @Mock
    private EventIngester eventIngester;

    @Mock
    private CallbackActor callbackActor;

    private RefundInitiationActionImpl refundInitiationAction;

    @BeforeEach
    public void setup() throws URISyntaxException, MalformedURLException {
        MockitoAnnotations.initMocks(this);
        refundInitiationAction =new RefundInitiationActionImpl(disputeService,
            disputeWorkflowRepository,
            refundService,
            paymentsService,
            disputeRepository,
            netBankingChargebackService,
            stateChangeHandlerProvider,
            eventIngester,
            callbackActor);
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testDefaultInValid(){
        DisputeWorkflow disputeWorkflow =new FinancialDisputeWorkflow();
        disputeWorkflow.setDisputeWorkflowId("3L");
        DisputedRefund disputeRefund = DisputedRefund.builder().retryCount(2L).transactionId("txnId").disputeWorkflow(disputeWorkflow).dispute(Dispute.builder().build()).build();
        Mockito.when(netBankingChargebackService.checkLatestRefundStatus(
            disputeRefund.getTransactionId(),
            disputeRefund.getDisputeWorkflow().getDisputeWorkflowId(),
            disputeRefund.getDispute())).thenReturn(RefundStatus.INITIATED);
        final var stratosError = Assertions.assertThrows(DisputeException.class, () -> refundInitiationAction.validateAndInitiateRefund(disputeRefund));
        Assertions.assertEquals("Internal Server Error",stratosError.getMessage());
    }


}
class RefundInitiationActionImpl extends RefundInitiationAction{

    public RefundInitiationActionImpl(
        DisputeService disputeService, DisputeWorkflowRepository disputeWorkflowRepository,
        RefundService refundService, PaymentsService paymentsService,
        DisputeRepository disputeRepository, NetBankingChargebackService netBankingChargebackService,
        Provider<StateChangeHandlerActor> stateChangeHandlerProvider, EventIngester eventIngester,
            CallbackActor callbackActor) {
        super(disputeService, disputeWorkflowRepository, refundService, paymentsService,
            disputeRepository, netBankingChargebackService, stateChangeHandlerProvider,
            eventIngester, callbackActor);
    }

    @Override
    public void validateAndInitiateRefund(DisputedRefund disputedRefund) {
        super.validateAndInitiateRefund(disputedRefund);
    }
}
