package com.phonepe.central.stratos.penalty.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.dropwizard.validation.ValidationMethod;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DateRangeRequest {

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date fromDate = new Date();

    @NotNull
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm", timezone = "Asia/Kolkata")
    private Date toDate;

    @JsonIgnore
    @ValidationMethod(message = "toDate must be after fromDate")
    public boolean isValid() {
        return toDate.after(fromDate);
    }

    public long getDifferenceInTimeUnit(final TimeUnit timeUnit) {
        long diffInMillis = Math.abs(toDate.getTime() - fromDate.getTime());
        return timeUnit.convert(diffInMillis, TimeUnit.MILLISECONDS);
    }

}
