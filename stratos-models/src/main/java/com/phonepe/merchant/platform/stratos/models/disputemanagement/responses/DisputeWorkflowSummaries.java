package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.PaginationDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeWorkflowSummaries {
    private List<ChargebackSummary> disputes;
    private PaginationDetails paginationDetails;

}
