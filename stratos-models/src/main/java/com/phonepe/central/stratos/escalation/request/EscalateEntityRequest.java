package com.phonepe.central.stratos.escalation.request;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EscalateEntityRequest {
    @NotBlank
    private String mappingId;

    @NotNull
    private EscalationLevel escalationLevel;

    @NotNull
    private DateRangeRequest dateRangeRequest;
}
