CREATE TABLE `client_callback_config_audit` (
  `id` bigint(20) NOT NULL,
  `REV` int(11) NOT NULL,
  `REVTYPE` tinyint(4) DEFAULT NULL,
  `profile_id` varchar(64) NOT NULL,
  `callback_service` varchar(128) NOT NULL,
  `callback_url` varchar(256) NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  PRIMARY KEY (`id`,`REV`),
  UNIQUE KEY `unq_idx_profile_id_REV` (`profile_id`,`REV`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;