package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.clients.PgTransportClient;
import com.phonepe.merchant.platform.stratos.server.core.services.PgTransportService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class PgTransportServiceImpl implements PgTransportService {

    private final PgTransportClient pgTransportClient;

    @Override
    public String getPaymentsIdFromPgTxnId(String pgTransactionId) {
        try{
            final var pgTransactionDetailResponse = pgTransportClient.getTransactionDetailsFromPgTxnId(
                    pgTransactionId);
            return pgTransactionDetailResponse.getStoredTransaction().getMerchantTransactionId();
        } catch (Exception e) {
            log.error("Error Occurred while getting transaction details from PGT:", e);
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.PGT_TRANSACTION_DETAIL_ERROR,
                    Map.of(Constants.TRANSACTION_ID, pgTransactionId));
        }
    }
}
