
ALTER TABLE `dispute_metadata` CHANGE COLUMN `refund_credit_source_type` `source_type` int, CHANGE COLUMN `refund_credit_source_id` `source_id` varchar(32), CHANGE COLUMN `refund_credit_amount` `amount` int;

ALTER TABLE `dispute_metadata_audit` CH<PERSON>GE COLUMN `refund_credit_source_type` `source_type` int, CHANGE COLUMN `refund_credit_source_id` `source_id` varchar(32), CHANGE COLUMN `refund_credit_amount` `amount` int;
