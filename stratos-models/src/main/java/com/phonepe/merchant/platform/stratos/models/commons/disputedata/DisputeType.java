package com.phonepe.merchant.platform.stratos.models.commons.disputedata;

import com.phonepe.merchant.platform.stratos.models.commons.disputedata.visitors.DisputeTypeVisitor;

public enum DisputeType {
    CHARGEBACK {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitChargeback();
        }
    },
    COMPLAINT {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitComplaint();
        }
    },
    FRAUD {
        @Override
        public <T> T accept(DisputeTypeVisitor<T> visitor) {
            return visitor.visitFraud();
        }
    };

    public abstract <T> T accept(final DisputeTypeVisitor<T> visitor);

}
