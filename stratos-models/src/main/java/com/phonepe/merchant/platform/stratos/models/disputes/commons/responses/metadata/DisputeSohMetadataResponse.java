package com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DisputeSohMetadataResponse extends DisputeMetadataBaseResponse {
    private final String agent;
    private final String reasonCode;
    private final String subReasonCode;
    private final String source;
    private final String instrumentType;
    private final String remark;

    @Builder
    public DisputeSohMetadataResponse(
            final String transactionId,
            final String disputeWorkflowId,
            final String agent,
            final String reasonCode,
            final String source,
            final String subReasonCode,
            final String instrumentType,
            final String remark) {
        super(DisputeMetadataDto.SOH_METADATA, transactionId,disputeWorkflowId);
        this.agent = agent;
        this.reasonCode = reasonCode;
        this.source = source;
        this.subReasonCode = subReasonCode;
        this.instrumentType = instrumentType;
        this.remark = remark;
    }
}
