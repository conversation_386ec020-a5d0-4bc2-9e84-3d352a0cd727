package com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.fraud;

import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeCategory;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.requests.RefundStatusReconRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeData;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.requests.CreateDisputeRequest;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.CreateDisputeResponse;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.integrationTests.disputeWorkflow.chargeback.ChargebackBaseTestCaseV2;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.AssertionUtils;
import com.phonepe.merchant.platform.stratos.server.integrationTests.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.utils.MockingUtils;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import org.eclipse.jetty.http.HttpStatus;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.concurrent.TimeUnit;

import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.COMPLETE_REPRESENTMENT;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.CB_REFUND_INITIATED;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.END;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK;
import static com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState.MERCHANT_ACTION_REQUESTED;

public class SusptectedFraudTest extends ChargebackBaseTestCaseV2 {
/*    @Test
    void testCreateDisputeFraud(){
        MockingUtils.setupPaymentsApiNoRefundExistsMocking("TXN1234",
                TestDataUtils.transactionAmount, "");
        MockingUtils.setupGetMerchantProfileApiMocking("IRCTCINAPP", "P2P_MERCHANT");
        MockingUtils.setupHoldEventIngestionApiMocking("TXN1234");
        CreateDisputeRequest testCreateDisputeRequest = SohDisputeRequest.builder()
            .transactionId("TXN1234")
            .disputedAmount((int) TestDataUtils.transactionAmount)
            .disputeData(DisputeData.builder()
                .disputeType(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.FRAUD)
                .disputeCategory(DisputeCategory.FRA)
                .disputeStage(
                    com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.PRE_CHARGEBACK)
                .build())
            .build();

        GenericResponse<CreateDisputeResponse> response = disputeResource.createDispute(TransactionType.FRA,
            com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.FRAUD,
            testCreateDisputeRequest, TestDataUtils.getOlympusUser());
        Assertions.assertEquals(String.valueOf(HttpStatus.OK_200), response.getCode());
        CreateDisputeResponse responseData = response.getData();
        responseData.getDisputeId();
        DisputeWorkflow dw = disputeService.getDisputeWorkflow("TXN1234", DisputeType.FRA_FRAUD, DisputeStage.PRE_CHARGEBACK);
        MockingUtils.setupPlutusEventIngestionApiMocking(dw);
        MockingUtils.setUpRefundResponseMocking(RefundStatus.INITIATED.name(),
                dw.getDisputedAmount());

        AssertionUtils.assertDisputeWorkflowStateEquals(
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                MERCHANT_ACTION_REQUESTED,
                20, TimeUnit.SECONDS);
        disputeResource.triggerEvent(
                serviceUserPrincipal,
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                MERCHANT_ACCEPT_CHARGEBACK, Constants.EMPTY_TRANSITION_CONTEXT);
//        AssertionUtils.assertDisputeWorkflow(
//                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
//                MERCHANT_ACCEPTED_CHARGEBACK, dw.getDispute().getDisputeCategory(), dw.getDispute().getDisputeType());

        AssertionUtils.assertDisputeWorkflowStateEquals(
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                CB_REFUND_INITIATED,20L, TimeUnit.SECONDS);

        String merchantTxnId = idHelper.createRefundId("TXN1234",
                dw.getDisputeStage().ordinal());

        MockingUtils.setUpRefundStatusMocking(RefundStatus.ACCEPTED.name(),
                dw.getDisputedAmount(), dw.getDispute().getMerchantId(),merchantTxnId );
        RefundStatusReconRequest refundStatusReconRequest = new RefundStatusReconRequest();
        disputeResource.reconcile(refundStatusReconRequest);


        AssertionUtils.assertDisputeWorkflowStateEquals(
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                END,20L, TimeUnit.SECONDS);

    }

    @Test
    void testCreateDisputeFraudRejected(){
        MockingUtils.setupPaymentsApiNoRefundExistsMocking("TXN1234",
                TestDataUtils.transactionAmount, "");
        MockingUtils.setupGetMerchantProfileApiMocking("IRCTCINAPP", "P2P_MERCHANT");
        MockingUtils.setupHoldEventIngestionApiMocking("TXN1234");
        CreateDisputeRequest testCreateDisputeRequest = SohDisputeRequest.builder()
                .transactionId("TXN1234")
                .disputedAmount((int) TestDataUtils.transactionAmount)
                .disputeData(DisputeData.builder()
                        .disputeType(
                                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.FRAUD)
                        .disputeCategory(DisputeCategory.FRA)
                        .disputeStage(
                                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeStage.PRE_CHARGEBACK)
                        .build())
                .build();

        GenericResponse<CreateDisputeResponse> response = disputeResource.createDispute(TransactionType.FRA,
                com.phonepe.merchant.platform.stratos.models.commons.disputedata.DisputeType.FRAUD,
                testCreateDisputeRequest, TestDataUtils.getOlympusUser());
        Assertions.assertEquals(String.valueOf(HttpStatus.OK_200), response.getCode());
        CreateDisputeResponse responseData = response.getData();
        responseData.getDisputeId();
        DisputeWorkflow dw = disputeService.getDisputeWorkflow("TXN1234", DisputeType.FRA_FRAUD, DisputeStage.PRE_CHARGEBACK);
        MockingUtils.setupPlutusEventIngestionApiMocking(dw);

        AssertionUtils.assertDisputeWorkflowStateEquals(
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                MERCHANT_ACTION_REQUESTED,
                20, TimeUnit.SECONDS);
        disputeResource.triggerEvent(
                serviceUserPrincipal,
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                RECEIVE_FULFILMENT_DOCUMENTS, Constants.EMPTY_TRANSITION_CONTEXT);
        disputeResource.triggerEvent(
                serviceUserPrincipal,
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                COMPLETE_REPRESENTMENT, Constants.EMPTY_TRANSITION_CONTEXT);
        AssertionUtils.assertDisputeWorkflowStateEquals(
                dw.getTransactionReferenceId(), dw.getDisputeWorkflowId(),
                END,20L, TimeUnit.SECONDS);

    }

 */

    @Override
    protected DisputeStage getDisputeStage() {
        return DisputeStage.PRE_CHARGEBACK;
    }

    @Override
    protected DisputeType getDisputeType() {
        return DisputeType.FRA_FRAUD;
    }
}
