package com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStateMapper;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStateMapperV2;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.PaymentsService;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.FeedUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.publishfeeds.DisputeEntityPublishActor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.FraUtils;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.transition.Transition;

import java.util.Objects;

@Slf4j
@Singleton
@SuppressWarnings("java:S3776")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class StateChangeListener<S, E> extends StateMachineListenerAdapter<S, E> {
    private String transactionReferenceId;
    private String disputeWorkflowId;
    private final DisputeService disputeService;
    private final DisputeEntityPublishActor disputeEntityPublishActor;

    @Override
    public void transition(Transition<S, E> transition) {
        if(transition.getSource().equals(transition.getTarget()))
            return;
        Objects.requireNonNull(transactionReferenceId);
        Objects.requireNonNull(disputeWorkflowId);

        final var disputeWorkflow = disputeService
                .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);
        if(!(disputeWorkflow instanceof FinancialDisputeWorkflow) ||
                DisputeType.TOAS.contains(disputeWorkflow.getDisputeType())) {
            return;
        }

        DisputeWorkflowState previousState = DisputeWorkflowState.valueOf(transition.getSource().getId().toString());
        DisputeWorkflowState currentState = DisputeWorkflowState.valueOf(transition.getTarget().getId().toString());;
        DisputeStateMapper mapper = MapperUtils.getStateMapper(disputeWorkflow.getDisputeWorkflowVersion());
        var prevStatus = previousState.accept(mapper, disputeWorkflow);
        var currStatus = currentState.accept(mapper, disputeWorkflow);
        if (previousState == DisputeWorkflowState.RECEIVED ||
            (prevStatus != null && currStatus != null && !prevStatus.equals(currStatus))) {
            // create initial entry or update when Status changes
            pushTstoreEntity(disputeWorkflow);
        }
        else
            log.info("CHECK>>> NOT PUSHING TransactionId : {}, DisputeWorkflowId : {}, previousState: {}, currentState: {}",
                transactionReferenceId, disputeWorkflowId, transition.getSource().getId().toString(), transition.getTarget().getId().toString());

    }

    @SneakyThrows
    private void pushTstoreEntity(DisputeWorkflow disputeWorkflow){
        try {
            final var disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .build();

            disputeEntityPublishActor.publish(disputeWorkflowMessage);
        } catch (Exception e) {
            log.error("Exception while pushing tstore entity for disputeWorkflowId : {}",
                    disputeWorkflowId,
                    e);
            throw e;
        }

    }

    @Override
    public void stateContext(final StateContext<S, E> stateContext) {
        if (stateContext.getStage().equals(StateContext.Stage.STATEMACHINE_START)) {
            this.transactionReferenceId = stateContext.getExtendedState()
                .get(DisputeWorkflow.Fields.transactionReferenceId, String.class);

            this.disputeWorkflowId = stateContext.getExtendedState()
                .get(DisputeWorkflow.Fields.disputeWorkflowId, String.class);
        }
    }

}
