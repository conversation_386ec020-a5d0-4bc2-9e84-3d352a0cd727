package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Builder
public class DateRangeParam {

    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;

    @JsonCreator
    public DateRangeParam(
            @JsonProperty("startDate") final LocalDate startDate,
            @JsonProperty("endDate") final LocalDate endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
}
