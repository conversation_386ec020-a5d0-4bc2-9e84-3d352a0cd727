package com.phonepe.merchant.platform.stratos.models.row.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Transaction Id",
    "Source",
    "Row Id",
    "Row State",
    "Transaction Type",
    "Transaction Amount",
    "Chargeback Accepted Amount"
})
public class EdcMisRowSummary {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Source Id")
    private String sourceId;

    @JsonProperty("Transaction Id")
    private String transactionId;

    @JsonProperty("Transaction Type")
    private String transactionType;

    @JsonProperty("Transaction Amount")
    private BigDecimal transactionAmount;

    @JsonProperty("Chargeback Accepted Amount")
    private BigDecimal chargebackAcceptedAmount;
}
