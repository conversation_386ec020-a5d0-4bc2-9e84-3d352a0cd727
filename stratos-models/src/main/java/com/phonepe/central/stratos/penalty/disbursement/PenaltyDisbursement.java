package com.phonepe.central.stratos.penalty.disbursement;

import java.util.Date;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PenaltyDisbursement {

    @NotBlank
    private String disbursementId;
    @NotBlank
    private String beneficiaryId;
    @NotBlank
    private String penaltyId;
    @NotBlank
    private String penaltyClassId;
    @Min(1)
    private long disbursedAmount;
    @NotNull
    private PenaltyDisbursementState status;
    @NotNull
    private PenaltyDisbursementMode disbursementMode;
    @NotBlank
    private String disbursementTransactionId;
    @NotBlank
    private String transactionId;

    private Date createdAt;
    private Date updatedAt;

    public boolean isDisbursementSuccessful() {
        return status == PenaltyDisbursementState.COMPLETED;
    }
}
