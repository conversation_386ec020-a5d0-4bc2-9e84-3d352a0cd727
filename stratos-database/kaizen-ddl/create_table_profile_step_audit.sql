CREATE TABLE `profile_step_audit` (
  `id` bigint(20) NOT NULL,
  `REV` int(11) NOT NULL,
  `REVTYPE` tinyint(4) DEFAULT NULL,
  `profile_id` varchar(64) NOT NULL,
  `profile_step_id` varchar(64) NOT NULL,
  `profile_step_mapping_id` varchar(128) NOT NULL,
  `title` varchar(128) NOT NULL,
  `execution_rule` text NOT NULL,
  `screen_config` mediumtext NOT NULL,
  `created_at` datetime(3) DEFAULT current_timestamp(3),
  `last_updated_at` datetime(3) DEFAULT current_timestamp(3) ON UPDATE current_timestamp(3),
  `last_updated_by` varchar(128) NOT NULL,
  PRIMARY KEY (`id`,`REV`),
  UNIQUE KEY `unq_idx_profile_step_id_REV` (`profile_step_id`,`REV`),
  <PERSON><PERSON>Y `profile_id_idx` (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;