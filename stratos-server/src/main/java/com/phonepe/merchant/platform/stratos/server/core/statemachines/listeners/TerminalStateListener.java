package com.phonepe.merchant.platform.stratos.server.core.statemachines.listeners;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.*;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.DisputeWorkflowMessage;
import com.phonepe.merchant.platform.stratos.server.core.services.AmountHoldService;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.queue.actors.statechange.StateChangeHandlerActor;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.statemachine.StateContext;
import org.springframework.statemachine.listener.StateMachineListenerAdapter;
import org.springframework.statemachine.transition.Transition;

import java.util.Objects;

@Slf4j
@Singleton
@SuppressWarnings("java:S3776")
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TerminalStateListener<S, E> extends StateMachineListenerAdapter<S, E> {
    /* This listener is responsible for doing automated transitions from internal terminal
     * states of disputes to either END or INDEFINITE_HOLD state.
     */
    private final DisputeService disputeService;
    private final StateChangeHandlerActor stateChangeHandlerActor;
    private final AmountHoldService amountHoldService;
    private String transactionReferenceId;
    private String disputeWorkflowId;

    @Override
    public void transition(Transition<S, E> transition) {
        if(transition.getSource().equals(transition.getTarget()))
            return;
        Objects.requireNonNull(transactionReferenceId);
        Objects.requireNonNull(disputeWorkflowId);

        final var disputeWorkflow = disputeService
                .validateAndGetDisputeWorkflow(transactionReferenceId, disputeWorkflowId);
        performAutomaticTransition(disputeWorkflow);
    }

    @Override
    public void stateContext(final StateContext<S, E> stateContext) {
        if (stateContext.getStage().equals(StateContext.Stage.STATEMACHINE_START)) {
            this.transactionReferenceId = stateContext.getExtendedState()
                    .get(DisputeWorkflow.Fields.transactionReferenceId, String.class);

            this.disputeWorkflowId = stateContext.getExtendedState()
                    .get(DisputeWorkflow.Fields.disputeWorkflowId, String.class);

        }
    }

    @SneakyThrows
    private void performAutomaticTransition(final DisputeWorkflow disputeWorkflow) {
        final DisputeWorkflowState currentState = disputeWorkflow.getCurrentState();
        final DisputeWorkflowMessage disputeWorkflowMessage = DisputeWorkflowMessage.builder()
                .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
                .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
                .build();
        final DisputeWorkflowEvent event = currentState.accept(new DisputeWorkflowStateNonMandatoryVisitor<DisputeWorkflowEvent>() {
            @Override
            public DisputeWorkflowEvent visitCreditReceived() {
                if (disputeWorkflow.getDisputeType().equals(DisputeType.UPI_CHARGEBACK)
                        && disputeWorkflow.getDisputeStage().equals(DisputeStage.FIRST_LEVEL)) {
                    return processHoldReversal(disputeWorkflow);
                }
                return null;
            }

            @Override
            public DisputeWorkflowEvent visitMerchantAcceptedChargeback() {
                if (disputeWorkflow.getDisputeType().equals(DisputeType.FRA_FRAUD)) {
                    return DisputeWorkflowEvent.COMPLETE_ACCEPTANCE;
                }
                return null;
            }

            @Override
            public DisputeWorkflowEvent visitAcceptanceCompleted() {
                if (disputeWorkflow.getDisputeType().equals(DisputeType.UPI_CHARGEBACK)
                        && disputeWorkflow.getDisputeStage().equals(DisputeStage.FIRST_LEVEL)) {
                    return processHoldReversal(disputeWorkflow);
                }
                if (disputeWorkflow.getDisputeType().equals(DisputeType.FRA_FRAUD)) {
                    return DisputeWorkflowEvent.CREATE_CHARGEBACK_REFUND;
                }
                return null;
            }

            @Override
            public DisputeWorkflowEvent visitPartialCreditReceived() {
                return visitCreditReceived();
            }

            @Override
            public DisputeWorkflowEvent visitDebitReceived() {
                if (disputeWorkflow.getDisputeType().equals(DisputeType.EDC_CHARGEBACK) ||
                    disputeWorkflow.getDisputeType().equals(DisputeType.PG_CHARGEBACK) ||
                    (disputeWorkflow.getDisputeType().equals(DisputeType.UPI_CHARGEBACK) &&
                     disputeWorkflow.getDisputeStage().equals(DisputeStage.PRE_ARBITRATION))
                ) {
                    return processHoldReversal(disputeWorkflow);
                } else
                    return null;
            }

            @Override
            public DisputeWorkflowEvent visitPartialDebitReceived() {
                return visitDebitReceived();
            }

            @Override
            public DisputeWorkflowEvent visitRepresentedCompleted() {
                if (disputeWorkflow.getDisputeType().equals(DisputeType.UPI_CHARGEBACK)
                        && disputeWorkflow.getDisputeStage().equals(DisputeStage.FIRST_LEVEL)) {
                    return null;
                }
                if(disputeWorkflow.getDisputeType().equals(DisputeType.FRA_FRAUD))
                    return DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD;
                return processHoldReversal(disputeWorkflow);
            }
            @Override
            public DisputeWorkflowEvent visitCBRefundAccepted() {
                if(disputeWorkflow.getDisputeType().equals(DisputeType.FRA_FRAUD))
                    return DisputeWorkflowEvent.END_WORKFLOW;
                return processHoldReversal(disputeWorkflow);
            }

            @Override
            public DisputeWorkflowEvent visitCBRefundFailed() {
                return processHoldReversal(disputeWorkflow);
            }
            @Override
            public DisputeWorkflowEvent visitRecoverPartialHoldEventAccepted(){
                return DisputeWorkflowEvent.HOLD;
            }
            @Override
            public DisputeWorkflowEvent visitRgcsAcceptanceCompleted() {
                return DisputeWorkflowEvent.END_WORKFLOW;
            }

        });
        if (Objects.nonNull(event)) {
            disputeWorkflowMessage.setDisputeWorkflowEvent(event);
            stateChangeHandlerActor.publish(disputeWorkflowMessage);
        }
    }

    private DisputeWorkflowEvent processHoldReversal(DisputeWorkflow disputeWorkflow){
        FinancialDisputeWorkflow dw = DisputeWorkflowUtils.getFinancialDisputeWorkflow(disputeWorkflow);
        if(dw.getAcceptedAmount() == 0){
            if(amountHoldService.holdExist(dw.getTransactionReferenceId())){
                return DisputeWorkflowEvent.HOLD;
            }
            else {
                return DisputeWorkflowEvent.END_WORKFLOW;
            }
        }
        else {
            if(amountHoldService.holdExist(dw.getTransactionReferenceId())){
                return DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_HOLD;
            }
            else {
                return DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK;
            }
        }
    }
}

