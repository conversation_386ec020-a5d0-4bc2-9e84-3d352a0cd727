package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils;

import com.phonepe.edc.response.EdcTransactionDetailsResponse;
import com.phonepe.kratos.common.Address;
import com.phonepe.kratos.common.CardDetails;
import com.phonepe.kratos.common.DeviceDetails;
import com.phonepe.kratos.common.DisputeDetails;
import com.phonepe.kratos.common.InstrumentDetails;
import com.phonepe.kratos.common.MerchantDetails;
import com.phonepe.kratos.common.SenderDetails;
import com.phonepe.kratos.common.TransactionContext;
import com.phonepe.kratos.common.TransactionDetails;
import com.phonepe.kratos.common.TransactionServiceContext;
import com.phonepe.kratos.common.UPIDetails;
import com.phonepe.kratos.common.edcInstrumentDetails;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.visitors.disputeworkflow.AcceptedAmountDisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.core.visitors.disputeworkflow.PenaltyAmountDisputeWorkflowVisitor;
import com.phonepe.merchant.platform.stratos.server.core.visitors.payment.CardIssuerPaymentInstrumentVisitor;
import com.phonepe.merchant.platform.stratos.server.core.visitors.payment.MaskCardNumberPaymentInstrumentVisitor;
import com.phonepe.merchant.platform.stratos.server.core.visitors.payment.VpaPaymentInstrumentVisitor;
import com.phonepe.models.merchants.MerchantProfile;
import com.phonepe.models.payments.pay.MetaData;
import com.phonepe.models.payments.pay.SentPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.instrument.PaymentInstrument;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Optional;
import lombok.experimental.UtilityClass;

@UtilityClass
public class FraUtils {

    private static final String EXTERNAL_TO_MERCHANT_V2 = "EXTERNAL_TO_MERCHANT_V2";
    private static final String EDC_DEFAULT = "EDC_DEFAULT";

    public String getSafeEnumName(Enum value) {
        return Optional.ofNullable(value)
            .map(Enum::name)
            .orElse(null);
    }

    public static long toEpochSecond(final LocalDateTime localDateTime){
        return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }
    public static long toEpochMilli(final LocalDateTime localDateTime){
        return localDateTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public MerchantDetails toFraMerchantDetails(MerchantProfile merchantProfile){
        return MerchantDetails.newBuilder()
            .setMerchantID(merchantProfile.getMerchantId())
            .setMerchantMcc(merchantProfile.getMcc())
            .setMerchantType(getSafeEnumName(merchantProfile.getType()))
            .setMerchantCategory(merchantProfile.getCategory())
            .setMerchantSubCategory(merchantProfile.getSubCategory())
            .setMerchantSuperCategory(merchantProfile.getSuperCategory())
            .setMerchantCBSName(merchantProfile.getFullName())
            .setMerchantOnboardedType(getSafeEnumName(merchantProfile.getOnboardingType()))
            .build();
    }

    public TransactionDetails toFraTransactionDetails(DisputeWorkflow disputeWorkflow, TransactionDetail transactionDetail){

        SentPayment sentPayment = transactionDetail.getSentPayment();

        TransactionContext transactionContext = Optional.ofNullable(sentPayment.getContext())
            .map( context -> TransactionContext.newBuilder()
                .setInitiationMode(getSafeEnumName(context.getInitiationMode()))
                .setTransferMode(getSafeEnumName(context.getTransferMode()))
                .build())
            .orElse(null);
        return TransactionDetails.newBuilder()
            .setTransactionID(disputeWorkflow.getTransactionReferenceId())
            .setMerchantTransactionId(disputeWorkflow.getDispute()
                .getMerchantTransactionId())
            .setInstrumentTransactionId(disputeWorkflow.getDispute().getInstrumentTransactionId())
            .setTransactionAmount(disputeWorkflow.getDispute().getTransactionAmount())
            .setPaymentFlow(transactionDetail.getFlow())
            .setTransactionDate(sentPayment.getSentAt().toInstant().getEpochSecond())
            .setTransactionContext(transactionContext)
            .build();
    }

    public TransactionDetails toFraTransactionDetailsForEdc(DisputeWorkflow disputeWorkflow, EdcTransactionDetailsResponse transactionDetail){

        TransactionContext transactionContext = Optional.ofNullable(transactionDetail.getOriginationMode())
            .map( context -> TransactionContext.newBuilder()
                .setInitiationMode(getSafeEnumName(transactionDetail.getAuthMode()))
                .setTransactionServiceContext(TransactionServiceContext.newBuilder()
                    .setOriginationMode(transactionDetail.getOriginationMode())
                    .build())
                .build())
            .orElse(null);


        return TransactionDetails.newBuilder()
                .setTransactionID(disputeWorkflow.getTransactionReferenceId())
                .setMerchantTransactionId(disputeWorkflow.getDispute()
                    .getMerchantTransactionId())
                .setInstrumentTransactionId(disputeWorkflow.getDispute().getInstrumentTransactionId())
                .setTransactionAmount(disputeWorkflow.getDispute().getTransactionAmount())
                .setPaymentFlow(EXTERNAL_TO_MERCHANT_V2)
                .setTransactionDate(transactionDetail.getTransactionDate().toInstant(ZoneOffset.UTC).toEpochMilli())
                .setTransactionContext(transactionContext)
                .build();
    }

    public DisputeDetails toFraDisputeDetails(DisputeWorkflow disputeWorkflow){
        return DisputeDetails.newBuilder()
            .setDisputeId(disputeWorkflow.getDisputeId())
            .setDisputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .setDisputeType(getSafeEnumName(disputeWorkflow.getDisputeType()))
            .setDisputeStage(getSafeEnumName(disputeWorkflow.getDisputeStage()))
            .setDisputedAmount(disputeWorkflow.getDisputedAmount())
            .setAcceptedAmount(disputeWorkflow.accept(new AcceptedAmountDisputeWorkflowVisitor()))
            .setPenaltyAmount(disputeWorkflow.accept(new PenaltyAmountDisputeWorkflowVisitor()))
            .setCurrentState(getSafeEnumName(disputeWorkflow.getCurrentState()))
            .setDisputeCategory(getSafeEnumName(disputeWorkflow.getDispute().getDisputeCategory()))
            .setDisputeIssuer(getSafeEnumName(disputeWorkflow.getDispute().getDisputeIssuer()))
            .setRaisedTimestamp(toEpochSecond(disputeWorkflow.getRaisedAt()))
            .setRespondTimestamp(toEpochSecond(disputeWorkflow.getUpdatedAt()))
            .build();
    }

    public com.phonepe.kratos.common.Address toFraMerchantAddress(com.phonepe.models.merchants.Address merchantAddress){

        return Optional.ofNullable(merchantAddress)
            .map( data -> Address.newBuilder()
                .setBuilding(merchantAddress.getBuilding())
                .setStreet(merchantAddress.getStreet())
                .setLocality(merchantAddress.getLocality())
                .setCity(merchantAddress.getCity())
                .setState(merchantAddress.getState())
                .setCountry(merchantAddress.getCountry())
                .setPincode(merchantAddress.getPinCode())
                .setFormattedAddress(merchantAddress.getFormattedAddress())
                .setLandmark(merchantAddress.getLandmark())
                .setLineOne(merchantAddress.getLineOne())
                .setLineTwo(merchantAddress.getLineTwo())
                .build())
            .orElse(Address.newBuilder().build());
    }

    public InstrumentDetails toFraInstrumentDetails(PaymentInstrument instrument){
        return Optional.ofNullable(instrument)
            .map(paymentInstrument -> InstrumentDetails.newBuilder()
                .setInstrumentType(getSafeEnumName(paymentInstrument.getType()))
                .setProcessingRail(getSafeEnumName(paymentInstrument.getProcessingRail()))
                .setProcessingMode(getSafeEnumName(paymentInstrument.getProcessingModeType()))
                .setCardDetails(CardDetails.newBuilder()
                    .setMaskCardNumber(paymentInstrument.accept(new MaskCardNumberPaymentInstrumentVisitor()))
                    .setCardIssuer(paymentInstrument.accept(new CardIssuerPaymentInstrumentVisitor()))
                    .build())
                .setUpiDetails(UPIDetails.newBuilder()
                    .setVpa(paymentInstrument.accept(new VpaPaymentInstrumentVisitor()))
                    .build())
                .build())
            .orElse(null);
    }

    public InstrumentDetails toFraInstrumentDetailsForEdc(EdcTransactionDetailsResponse transactionDetailsResponse){

        return Optional.ofNullable(transactionDetailsResponse)
            .map(paymentInstrument -> InstrumentDetails.newBuilder()
                .setInstrumentType(getSafeEnumName(transactionDetailsResponse.getCardType()))
                .setProcessingMode(EDC_DEFAULT)
                .setCardDetails(CardDetails.newBuilder()
                    .setMaskCardNumber(transactionDetailsResponse.getMaskedCardNumber())
                    .setBinDetails(Long.parseLong(transactionDetailsResponse.getMaskedCardNumber()
                            .substring(0, 6)))
                    .setCardHolderName(transactionDetailsResponse.getCardHolderName())
                    .setCardIssuer(getSafeEnumName(transactionDetailsResponse.getCardNetwork()))
                    .build())
                .setEdcInstrumentDetails(edcInstrumentDetails.newBuilder()
                    .setEdcTerminalID(transactionDetailsResponse.getTerminalId())
                    .setEdcDeploymentDate(transactionDetailsResponse.getDeploymentDate())
                    .setEdcDeviceSerialNumber(transactionDetailsResponse.getDeviceSerialNo())
                    .setIntegrationPartner(getSafeEnumName(transactionDetailsResponse.getTenant()))
                    .setPaymentMode(getSafeEnumName(transactionDetailsResponse.getAuthMode()))
                    .build())
                .build())
            .orElse(null);
    }

    public DeviceDetails toFraDeviceDetails(MetaData metaData){
        return Optional.ofNullable(metaData)
            .map( data -> DeviceDetails.newBuilder()
                .setDeviceIpAddress(data.getDeviceIp())
                .setDeviceLongitude(String.valueOf(data.getDeviceLongitude()))
                .setDeviceLatitude(String.valueOf(data.getDeviceLatitude()))
                .build())
            .orElse(DeviceDetails.newBuilder().build());
    }

    public SenderDetails toFraSenderDetails( SentPayment sentPayment){
        return SenderDetails.newBuilder()
            .setUserID(sentPayment.getUserId())
            .setPhoneNumber(sentPayment.getTo().get(0).getPhone())
            .build();
    }
}
