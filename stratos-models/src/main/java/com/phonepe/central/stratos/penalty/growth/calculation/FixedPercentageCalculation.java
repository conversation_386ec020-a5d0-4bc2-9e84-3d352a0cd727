package com.phonepe.central.stratos.penalty.growth.calculation;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public abstract class FixedPercentageCalculation extends Calculation {

    @Min(0)
    @Max(100)
    @NotNull
    private BigDecimal percentage;

    @Min(0)
    private BigDecimal maxAmount;

    public FixedPercentageCalculation(CalculationType type, String amountPath, boolean isDefault,
                                      BigDecimal percentage, BigDecimal maxAmount) {
        super(type, amountPath, isDefault);
        this.percentage = percentage;
        this.maxAmount = maxAmount;
    }
}
