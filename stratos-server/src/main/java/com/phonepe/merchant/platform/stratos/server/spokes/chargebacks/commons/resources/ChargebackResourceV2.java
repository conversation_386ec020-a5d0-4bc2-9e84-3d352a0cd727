package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.resources;

import static com.phonepe.olympus.im.models.core.CoreConstants.BEARER;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.google.inject.Inject;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.DisputeWorkflowFilterParams;
import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.DisputeWorkflowSummaries;
import com.phonepe.merchant.platform.stratos.server.core.models.ApiState;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.platform.filters.annotation.ApiKillerMeta;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeIn;
import io.swagger.v3.oas.annotations.enums.SecuritySchemeType;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.security.SecurityScheme;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.annotation.security.RolesAllowed;
import javax.validation.Valid;
import javax.ws.rs.Consumes;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Path("/v2/chargeback")
@Tag(name = "Chargeback Related V2 APIs")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
@SecurityScheme(name = BEARER, type = SecuritySchemeType.APIKEY, in = SecuritySchemeIn.HEADER, paramName = "Authorization")
@SecurityRequirement(name = BEARER)
public class ChargebackResourceV2 {

    private final DisputeService disputeService;

    @POST
    @Path("/filter")
    @ExceptionMetered
    @RolesAllowed("chargeback/summary")
    @Operation(summary = "Fetch Chargeback Summary based on Filter Parameters")
    @ApiKillerMeta(tags = {ApiState.ApiStateString.READ_API}, statusCode = Constants.BLOCKED_ERROR_CODE)
    public DisputeWorkflowSummaries getChargebackSummary(
            @Valid final DisputeWorkflowFilterParams filter) {

        return disputeService.getDisputeWorkflowSummary(filter);
    }
}
