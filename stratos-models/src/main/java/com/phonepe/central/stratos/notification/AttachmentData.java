package com.phonepe.central.stratos.notification;


import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@Builder
@EqualsAndHashCode(callSuper = false)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentData {

    @NotBlank
    private String attachmentFileName;

    @NotBlank
    private String attachmentFileType;

    @NotBlank
    private String attachment;

}
