package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import static com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.ContestType.Names.FULL_CONTEST;
import static com.phonepe.merchant.platform.stratos.models.disputemanagement.requests.ContestType.Names.PARTIAL_CONTEST;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY,
        property = "contestType", visible = true)
@JsonSubTypes(value = {
        @JsonSubTypes.Type(value = FullContestPayload.class, name = FULL_CONTEST),
        @JsonSubTypes.Type(value = PartialContestPayload.class, name = PARTIAL_CONTEST),
})
public abstract class ContestPayload {
    @NotNull
    private ContestType contestType;
    @NotBlank
    private String merchantId;
    @NotBlank
    private String disputeId;
}
