package com.phonepe.central.stratos.penalty.response;

import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class EscalationResponse {

    @NotNull
    private Long id;
    @NotNull
    private EscalationType escalationType;
    @NotNull
    private String escalationMappingId;
    @NotNull
    private EscalationLevel escalationLevel;
    @NotNull
    private TenantInfo tenantInfo;
    @NotEmpty
    private Set<String> emailDl;
}
