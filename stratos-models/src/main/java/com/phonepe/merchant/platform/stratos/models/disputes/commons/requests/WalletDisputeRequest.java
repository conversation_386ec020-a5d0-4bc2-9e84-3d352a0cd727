package com.phonepe.merchant.platform.stratos.models.disputes.commons.requests;

import com.phonepe.merchant.platform.stratos.models.commons.TransactionType;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeData;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@NoArgsConstructor(force = true)
@EqualsAndHashCode(callSuper = true)
public class WalletDisputeRequest extends CreateDisputeRequest {

    @NotEmpty
    @Valid
    private final String reasonCode;

    @Builder
    public WalletDisputeRequest(@NotNull TransactionType transactionType,@NotEmpty String transactionId,
        @NotEmpty Integer disputedAmount, @NotNull @Valid DisputeData disputeData
        , String reasonCode) {
        super(transactionType, transactionId, disputedAmount, disputeData);
        this.reasonCode = reasonCode;
    }
}