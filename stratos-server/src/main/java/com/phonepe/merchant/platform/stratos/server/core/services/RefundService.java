package com.phonepe.merchant.platform.stratos.server.core.services;

import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.models.DisputedRefund;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.model.DisputeRefundV2;
import com.phonepe.models.response.GenericResponse;
import com.phonepe.services.refund.orchestrator.models.RefundStatus;
import com.phonepe.services.refund.orchestrator.models.v1.ROStatusRequest;
import com.phonepe.services.refund.orchestrator.models.v1.RefundResponse;


public interface RefundService {

    GenericResponse<RefundResponse> intiateRefund(final DisputedRefund disputedRefund);

    void refundStatusCallBack(final RefundResponse refundResponse);

    void updateRefundId(DisputeWorkflow disputeWorkflow, String refundId);
    RefundStatus getRefundStatus(final ROStatusRequest request);

    GenericResponse<RefundResponse> initiateRefund(final DisputeRefundV2 disputedRefund);
}
