package com.phonepe.merchant.platform.stratos.models.files.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Row Id",
    "Row State",
    "Code",
    "CB Received Date",
    "Transaction Date",
    "Transaction ID",
    "Bank Reference ID",
    "Amount",
    "Source",
    "Dispute Reason"
})
public class NBFileHistoryProcessingSummaryRow {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;

    @JsonProperty("Transaction Date")
    private String transactionDate;

    @JsonProperty("Transaction ID")
    private String transactionId;

    @JsonProperty("Bank Reference ID")
    private String bankReferenceId;

    @JsonProperty("Amount")
    private String amount;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Dispute Reason")
    private String disputeReason;

}