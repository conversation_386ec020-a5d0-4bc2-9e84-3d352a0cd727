package com.phonepe.merchant.platform.stratos.models.disputes.toa;

import com.phonepe.merchant.platform.stratos.models.disputes.toa.enums.ToaReceiverInstrument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ToaTransactionMetadata {

    @NotNull
    private ToaReceiverInstrument toaReceiverInstrument;
}
