package com.phonepe.merchant.platform.stratos.models.udir.response;


import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class UdirOutgoingRaiseComplaintResponse extends UdirRaiseComplaintResponse {

    private String complaintId;
    private String stratosComplaintId;

    public UdirOutgoingRaiseComplaintResponse() {
        super(UdirDisputeTypeDto.UDIR_OUTGOING_COMPLAINT);
    }

    @Builder
    public UdirOutgoingRaiseComplaintResponse(
        final DisputeStageDto disputeStage,
        final String transactionId, final String complaintId, final String stratosComplaintId) {
        super(UdirDisputeTypeDto.UDIR_OUTGOING_COMPLAINT, disputeStage, transactionId);
        this.complaintId = complaintId;
        this.stratosComplaintId = stratosComplaintId;
    }
}
