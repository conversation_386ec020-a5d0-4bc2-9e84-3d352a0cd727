package com.phonepe.merchant.platform.stratos.models.row;

import com.phonepe.merchant.platform.stratos.models.files.RowStateDto;
import java.math.BigDecimal;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class EdcRowDto extends RowDto {

    private String rowId;
    private RowStateDto rowState;
    private String sourceId;
    private String transactionId;
    private String pgTransactionId;
    private RowTransactionType transactionType;
    private BigDecimal transactionAmount;
    private BigDecimal chargebackAcceptedAmount;

    public EdcRowDto() {
        super(RowTypeDto.EDC_MIS_ROW);
    }

    @SuppressWarnings("java:S107")
    @Builder
    public EdcRowDto(final String rowId, final RowStateDto rowState, final String sourceId,
        final String transactionId,
        final String pgTransactionId, final RowTransactionType transactionType,
        final BigDecimal transactionAmount,
        final BigDecimal chargebackAcceptedAmount, final String code, final String interchange) {
        this();
        this.rowId = rowId;
        this.rowState = rowState;
        this.sourceId = sourceId;
        this.transactionId = transactionId;
        this.pgTransactionId = pgTransactionId;
        this.transactionType = transactionType;
        this.transactionAmount = transactionAmount;
        this.chargebackAcceptedAmount = chargebackAcceptedAmount;
    }
}
