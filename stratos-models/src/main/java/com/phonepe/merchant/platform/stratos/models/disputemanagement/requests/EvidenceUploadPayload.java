package com.phonepe.merchant.platform.stratos.models.disputemanagement.requests;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvidenceUploadPayload {
    @NotBlank
    private String merchantId;
    @NotBlank
    private String disputeId;
    private Map<String,Object> metadata;

}
