package com.phonepe.central.stratos.penalty.request.penalty;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({@JsonSubTypes.Type(name = PenaltySearchType.BASIC_SEARCH_TEXT, value = PenaltyBasicSearchRequest.class),
        @JsonSubTypes.Type(name = PenaltySearchType.DATE_RANGE_SEARCH_TEXT, value = PenaltyDateRangeSearchRequest.class),})
public abstract class PenaltySearchRequest {

    @NotNull
    private final PenaltySearchType type;
    @NotBlank
    private String penaltyClassId;

    public abstract <T> T accept(PenaltySearchRequestVisitor<T> visitor);

    public interface PenaltySearchRequestVisitor<T> {

        T visit(final PenaltyBasicSearchRequest searchRequest);

        T visit(final PenaltyDateRangeSearchRequest searchRequest);

    }
}


