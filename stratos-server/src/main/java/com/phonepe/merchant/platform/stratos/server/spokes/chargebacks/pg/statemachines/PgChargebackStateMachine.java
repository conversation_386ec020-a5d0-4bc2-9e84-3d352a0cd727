package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines;

import com.phonepe.merchant.platform.stratos.server.core.aerospike.commands.TransitionLockCommand;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.registries.keys.DisputeStateMachineRegistryKey;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.DisputeStateMachine;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.MandatoryCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.comments.impls.OptionalCommentUpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDebitAndDebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.AcceptPartialDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.DebitDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.FraudCheckDisputeAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.ResetChargebackAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.actions.updates.impls.UpdateDisputeStateAction;
import com.phonepe.merchant.platform.stratos.server.core.statemachines.interceptors.impls.DisputeErrorHandlingInterceptor;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoverChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.ApproveRecoveryReversalChargebackAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.BlockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.RaiseChargebackRecoveryReversalAccountingEventAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.statemachines.actions.UnblockRefundAction;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.statemachines.actions.PgChargebackCreateEntryAction;
import java.util.EnumSet;
import lombok.SneakyThrows;
import org.springframework.statemachine.config.builders.StateMachineConfigurationConfigurer;
import org.springframework.statemachine.config.builders.StateMachineStateConfigurer;
import org.springframework.statemachine.config.builders.StateMachineTransitionConfigurer;

public abstract class PgChargebackStateMachine extends DisputeStateMachine {

    private final PgChargebackCreateEntryAction createEntryAction;
    private final BlockRefundAction blockRefundAction;
    private final UpdateDisputeStateAction updateDisputeStateAction;
    private final AcceptDisputeAction acceptDisputeAction;
    private final AcceptPartialDisputeAction acceptPartialDisputeAction;
    private final DebitDisputeAction debitDisputeAction;
    private final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction;
    private final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction;
    private final ApproveRecoverChargebackAction approveRecoverChargebackAction;
    private final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction;
    private final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction;
    private final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction;
    private final UnblockRefundAction unblockRefundAction;
    private final ResetChargebackAction resetChargebackAction;
    private final FraudCheckDisputeAction fraudCheckUpdateDisputeAction;

    private final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction;

    @SuppressWarnings("java:S107")
    public PgChargebackStateMachine(final TransitionLockCommand transitionLockCommand,
        final DisputeErrorHandlingInterceptor disputeErrorHandlingInterceptor,
        final PgChargebackCreateEntryAction createEntryAction,
        final BlockRefundAction blockRefundAction,
        final UpdateDisputeStateAction updateDisputeStateAction,
        final AcceptDisputeAction acceptDisputeAction,
        final AcceptPartialDisputeAction acceptPartialDisputeAction,
        final DebitDisputeAction debitDisputeAction,
        final MandatoryCommentUpdateDisputeStateAction mandatoryCommentUpdateDisputeStateAction,
        final OptionalCommentUpdateDisputeStateAction optionalCommentUpdateDisputeStateAction,
        final ApproveRecoverChargebackAction approveRecoverChargebackAction,
        final RaiseChargebackRecoveryAccountingEventAction raiseChargebackRecoveryAccountingEventAction,
        final ApproveRecoveryReversalChargebackAction approveRecoveryReversalChargebackAction,
        final RaiseChargebackRecoveryReversalAccountingEventAction raiseChargebackRecoveryReversalAccountingEventAction,
        final UnblockRefundAction unblockRefundAction,
        final ResetChargebackAction resetChargebackAction,
        final FraudCheckDisputeAction fraudCheckUpdateDisputeAction,
        final AcceptDebitAndDebitDisputeAction acceptDebitAndDebitDisputeAction) {


        super(transitionLockCommand, disputeErrorHandlingInterceptor);
        this.createEntryAction = createEntryAction;
        this.blockRefundAction = blockRefundAction;
        this.updateDisputeStateAction = updateDisputeStateAction;
        this.acceptDisputeAction = acceptDisputeAction;
        this.acceptPartialDisputeAction = acceptPartialDisputeAction;
        this.debitDisputeAction = debitDisputeAction;
        this.mandatoryCommentUpdateDisputeStateAction = mandatoryCommentUpdateDisputeStateAction;
        this.optionalCommentUpdateDisputeStateAction = optionalCommentUpdateDisputeStateAction;
        this.approveRecoverChargebackAction = approveRecoverChargebackAction;
        this.raiseChargebackRecoveryAccountingEventAction = raiseChargebackRecoveryAccountingEventAction;
        this.approveRecoveryReversalChargebackAction = approveRecoveryReversalChargebackAction;
        this.raiseChargebackRecoveryReversalAccountingEventAction = raiseChargebackRecoveryReversalAccountingEventAction;
        this.resetChargebackAction = resetChargebackAction;
        this.unblockRefundAction = unblockRefundAction;
        this.fraudCheckUpdateDisputeAction = fraudCheckUpdateDisputeAction;
        this.acceptDebitAndDebitDisputeAction = acceptDebitAndDebitDisputeAction;
    }

    @Override
    public abstract DisputeStateMachineRegistryKey getRegistryKey();

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineConfigurationConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> config) {
        config
            .withConfiguration()
            .autoStartup(false);
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineStateConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> states) {
        states
            .withStates()
            .initial(DisputeWorkflowState.RECEIVED)
            .states(EnumSet.allOf(DisputeWorkflowState.class))
            .end(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .end(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .end(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED);
    }

    @Override
    @SneakyThrows
    protected void configure(
        final StateMachineTransitionConfigurer<DisputeWorkflowState, DisputeWorkflowEvent> transitions) {
        transitions
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.RECEIVED)
            .event(DisputeWorkflowEvent.CREATE_ENTRY)
            .action(createEntryAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.INTERNAL_MID_REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.INTERNAL_MID_REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REFUND_BLOCKED)
            .event(DisputeWorkflowEvent.BLOCK_REFUND)
            .action(blockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECEIVED)
            .target(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .event(DisputeWorkflowEvent.REQUEST_REPRESENTMENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REPRESENTMENT_REQUIRED)
            .target(DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPT_CHARGEBACK)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FRAUD_REJECTED)
            .target(DisputeWorkflowState.NPCI_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.FRAUD_REJECTED_TO_REPRESENTMENT) // Manual action
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE)
            .action(acceptDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD_TO_ACCEPTANCE)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.SUSPECTED_FRAUD)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)


            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .event(DisputeWorkflowEvent.NO_RESPONSE_FROM_MERCHANT_WITHIN_TTL)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_ACCEPTANCE)
            .action(acceptDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_NOT_RESPONDED_WITHIN_TTL)
            .target(DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_FULFILMENT_DOCUMENTS)
            .action(fraudCheckUpdateDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_PARTIAL_REPRESENTMENT)
            .action(acceptPartialDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.SUSPECTED_FRAUD)
            .event(DisputeWorkflowEvent.SUSPECTED_FRAUD)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.FRAUD_REJECTED)
            .event(DisputeWorkflowEvent.FRAUD_REJECT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REFUND_BLOCKED)
            .target(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_FULFILMENT_DOCUMENTS)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.FULFILMENT_DOCUMENTS_RECEIVED)
            .target(DisputeWorkflowState.PG_REPRESENTMENT_COMPLETED)
            .event(DisputeWorkflowEvent.COMPLETE_PG_REPRESENTMENT)
            .action(unblockRefundAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_PARTIAL_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PG_PARTIAL_REPRESENTMENT_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.RECEIVE_DEBIT)
            .action(debitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.MERCHANT_ACCEPTED_CHARGEBACK)
            .target(DisputeWorkflowState.DEBIT_RECEIVED)
            .event(DisputeWorkflowEvent.MERCHANT_ACCEPTED_CHARGEBACK_TO_DEBIT_RECEIVED)
            .action(acceptDebitAndDebitDisputeAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PG_ACCEPTANCE_COMPLETED)
            .target(DisputeWorkflowState.CHARGEBACK_CANCELLED)
            .event(DisputeWorkflowEvent.RESET_CHARGEBACK)
            .action(resetChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.PARTIAL_DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.DEBIT_RECEIVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .event(DisputeWorkflowEvent.ABSORB_CHARGEBACK)
            .action(optionalCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.CHARGEBACK_ABSORBED)
            .target(DisputeWorkflowState.CHARGEBACK_ABSORB_REVERSED)
            .event(DisputeWorkflowEvent.REVERSE_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.ABSORB_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_ABSORB_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.ABSORB_CHARGEBACK_REJECTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_REJECTED)
            .event(DisputeWorkflowEvent.REJECT_RECOVER_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_RECOVER_CHARGEBACK)
            .action(approveRecoverChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_RECOVER_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_RECOVER_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .event(DisputeWorkflowEvent.REQUEST_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.RECOVER_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.REJECT_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(mandatoryCommentUpdateDisputeStateAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_REQUESTED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .event(DisputeWorkflowEvent.APPROVE_REVERSAL_OF_RECOVERED_CHARGEBACK)
            .action(approveRecoveryReversalChargebackAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_APPROVED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .event(DisputeWorkflowEvent.RAISE_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(raiseChargebackRecoveryReversalAccountingEventAction)

            .and()
            .withExternal()
            .source(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_RAISED)
            .target(DisputeWorkflowState.REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT_ACCEPTED)
            .event(DisputeWorkflowEvent.ACCEPT_REVERSAL_OF_RECOVERED_CHARGEBACK_EVENT)
            .action(updateDisputeStateAction);
    }

}
