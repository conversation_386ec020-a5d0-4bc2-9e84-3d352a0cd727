package com.phonepe.merchant.platform.stratos.server.v2.files.fraud;

import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class FraudMeta extends FileRowMeta {
    private String agent;
    private String instrumentType;
    private String reasonCode;
    private String source;
    private String subReasonCode;
    private String remarks;
    private Integer ttl; //in days

    public FraudMeta() {
        super(DisputeType.FRA_FRAUD);
    }

    @Builder
    public FraudMeta(
            final String agent,
            final String instrumentType,
            final String reasonCode,
            final String source,
            final String subReasonCode,
            final String remarks,
            final Integer ttl) {
        this();
        this.agent = agent;
        this.instrumentType = instrumentType;
        this.reasonCode = reasonCode;
        this.source = source;
        this.subReasonCode = subReasonCode;
        this.remarks = remarks;
        this.ttl = ttl;
    }

}
