package com.phonepe.merchant.platform.stratos.models.disputes.commons.responses;


import static com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto.Names.UDIR_OUTGOING_COMPLAINT;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.udir.response.UdirOutgoingComplaintStatusResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "disputeType", visible = true)
@JsonSubTypes(value = {
    @Type(value = UdirOutgoingComplaintStatusResponse.class, name = UDIR_OUTGOING_COMPLAINT)
})
public abstract class CheckStatusResponse {

    private DisputeTypeDto disputeType;
    private DisputeStageDto disputeStage;
    private String transactionId;

    public CheckStatusResponse(DisputeTypeDto disputeType) {
        this.disputeType = disputeType;
    }
}
