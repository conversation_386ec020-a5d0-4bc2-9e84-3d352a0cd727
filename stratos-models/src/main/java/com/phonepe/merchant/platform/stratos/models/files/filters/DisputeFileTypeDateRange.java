package com.phonepe.merchant.platform.stratos.models.files.filters;

import com.phonepe.merchant.platform.stratos.models.commons.DateRange;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileTypeDto;
import com.phonepe.merchant.platform.stratos.models.files.FileFilter;
import com.phonepe.merchant.platform.stratos.models.files.FileFilterType;
import java.util.Set;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class DisputeFileTypeDateRange extends FileFilter {

    @NotEmpty
    private Set<DisputeTypeDto> disputeTypes;
    @NotEmpty
    private Set<FileTypeDto> fileTypes;

    @NotNull
    @Valid
    private DateRange dateRange;

    public DisputeFileTypeDateRange() {
        super(FileFilterType.DISPUTE_FILE_TYPE_DATE_RANGE);
    }

    @Builder
    public DisputeFileTypeDateRange(Set<DisputeTypeDto> disputeTypes,
        Set<FileTypeDto>fileTypes, DateRange dateRange) {
        this();
        this.dateRange = dateRange;
        this.disputeTypes = disputeTypes;
        this.fileTypes = fileTypes;
    }

    @Override
    public <T> T accept(FIleFilterVisitor<T> filterVisitor) {
        return filterVisitor.visit(this);
    }
}
