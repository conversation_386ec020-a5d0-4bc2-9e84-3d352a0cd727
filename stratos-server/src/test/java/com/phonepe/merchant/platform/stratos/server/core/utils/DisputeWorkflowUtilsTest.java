package com.phonepe.merchant.platform.stratos.server.core.utils;

import com.phonepe.error.configurator.model.service.ResourceErrorService;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.ErrorConfiguratorBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.NonFinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowState;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowVersion;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeException;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.net.MalformedURLException;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 */
@Slf4j
public class DisputeWorkflowUtilsTest extends ErrorConfiguratorBaseTest {

    @BeforeEach
    public void setup() throws MalformedURLException, URISyntaxException {
        ResourceErrorService<StratosErrorCodeKey> resourceErrorService = getResourceErrorService();
        log.error("resourceErrorService  : {}", resourceErrorService);
        DisputeExceptionUtil.init(resourceErrorService);
    }

    @Test
    public void testNonFinancialFlow(){
        final var stratosError = Assertions.assertThrows(DisputeException.class, () -> DisputeWorkflowUtils.getFinancialDisputeWorkflow(NonFinancialDisputeWorkflow.builder().build()));
        Assertions.assertEquals("Operation is not allowed",stratosError.getMessage());
    }
    @Test
    public void testignoreSignalForNullConfig(){
        Assertions.assertFalse(
                DisputeWorkflowUtils.ignoreSignal(null, FinancialDisputeWorkflow.builder()
            .build()));
        Map<DisputeType, Map<DisputeWorkflowVersion, List<DisputeWorkflowState>>> ignoreSignalStateConfig =
                new HashMap<>();
        Map<DisputeWorkflowVersion, List<DisputeWorkflowState>> versionStateMap = new HashMap<>();
        //versionStateMap.put(DisputeWorkflowVersion.V1, List.of(DisputeWorkflowState.ACCEPTED_CHARGEBACK));
        ignoreSignalStateConfig.put(DisputeType.UPI_CHARGEBACK,versionStateMap);
        Assertions.assertFalse(
            DisputeWorkflowUtils.ignoreSignal(null, FinancialDisputeWorkflow.builder()
                .disputeType(DisputeType.PG_CHARGEBACK)
            .build()));
        Assertions.assertFalse(
            DisputeWorkflowUtils.ignoreSignal(null, FinancialDisputeWorkflow.builder()
                .disputeType(DisputeType.UPI_CHARGEBACK)
                            .disputeWorkflowVersion(DisputeWorkflowVersion.V1)
                .build()));

    }
}
