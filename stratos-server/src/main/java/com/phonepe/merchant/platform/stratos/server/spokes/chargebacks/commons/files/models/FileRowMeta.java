package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType.Names;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.edc.files.models.EdcFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.netbanking.files.models.NetBankingFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.pg.files.models.PgFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.upi.files.models.UpiFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.wallet.files.WalletFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.toa.bbps.files.BBPSToaFileRowMeta;
import com.phonepe.merchant.platform.stratos.server.v2.files.fraud.FraudMeta;
import javax.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "type", visible = true)
@JsonSubTypes(value = {
    @Type(value = UpiFileRowMeta.class, name = Names.UPI_CHARGEBACK),
    @Type(value = PgFileRowMeta.class, name = Names.PG_CHARGEBACK),
    @Type(value = EdcFileRowMeta.class, name = Names.EDC_CHARGEBACK),
    @Type(value = NetBankingFileRowMeta.class, name = Names.NETBANKING_CHARGEBACK),
    @Type(value = BBPSToaFileRowMeta.class, name = Names.BBPS_TAT_BREACH_TOA),
    @Type(value = WalletFileRowMeta.class, name = Names.WALLET_CHARGEBACK),
    @Type(value = FraudMeta.class, name = Names.FRA_FRAUD)
})
public abstract class FileRowMeta {

    @NotEmpty
    private DisputeType type;
}
