package com.phonepe.merchant.platform.stratos.server.core.services.impls;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeEntityFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.DisputeFeed;
import com.phonepe.merchant.platform.stratos.models.feeds.EntityType;
import com.phonepe.merchant.platform.stratos.models.feeds.MetaType;
import com.phonepe.merchant.platform.stratos.models.feeds.TstoreFeed;
import com.phonepe.merchant.platform.stratos.server.core.configs.TstoreClientConfig;
import com.phonepe.merchant.platform.stratos.server.core.services.TstoreService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.platform.schema.models.SchemaParams;
import com.phonepe.platform.scroll.model.State;
import com.phonepe.platform.scroll.model.v2.BaseEntity;
import com.phonepe.platform.scroll.model.v2.CreatedSchemaParams;
import com.phonepe.platform.scroll.model.v2.Notification;
import com.phonepe.platform.scroll.model.v2.NotificationEntity;
import com.phonepe.tstore.client.TstoreClient;
import io.appform.functionmetrics.MonitoredFunction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Singleton
@RequiredArgsConstructor(onConstructor = @__({@Inject}))
public class TstoreServiceImpl implements TstoreService {
    private final TstoreClient tstoreClient;

    private final TstoreClientConfig tstoreClientConfig;

    private final Map<Class<?>, CreatedSchemaParams> schemaParamsMapping;

    @Override
    @MonitoredFunction
    public void createMerchantFeed(
            String entityId, TstoreFeed disputeFeed,
            String merchantId, String unitId,
            long createdAt, long updatedAt) {
        createFeed(entityId, disputeFeed,MetaType.MERCHANT.name(), merchantId, unitId, createdAt, updatedAt);
    }

    private void createFeed(
        final String entityId,
        final TstoreFeed disputeFeed,
        final String metaType,
        final String metaId,
        final String unitId,
        final long createdAt,
        final long updatedAt) {
        final var baseEntity = toBaseEntity(entityId, disputeFeed, metaType, metaId, createdAt, updatedAt);
        final var notification = toNotification(entityId, disputeFeed, metaId, unitId);
        final var notificationEntity = getNotificationEntity(baseEntity, notification, disputeFeed);

        final var published = tstoreClient.putEntityAndNotify(notificationEntity,
                notificationEntity.getNotification().getUnitId(), notificationEntity.getNotification().getEntityId());

        if (!published) {
            final var error = DisputeExceptionUtil.error(StratosErrorCodeKey.FEED_PUBLISH_ERROR, Map.of());
            log.error("Failed to publish feed entity: {}", disputeFeed, error);
            throw error;
        }
        log.info("Successfully published feed entity: {}, in unitId : {}", disputeFeed, unitId);
    }

    private SchemaParams getSchemaParams(TstoreFeed feed) {
        if (Objects.isNull(schemaParamsMapping.get(feed.getClass()))) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.FEED_SCHEMA_PARAMS_NOT_FOUND, Map.of());
        }
        return schemaParamsMapping.get(feed.getClass()).getEntitySchemaParams();
    }

    private BaseEntity toBaseEntity(
            final String entityId,
            final TstoreFeed feed,
            final String metaType,
            final String metaId,
            final long createdAt,
            final long updatedAt) {
        String type = feed.getEntityType().name();
        return BaseEntity.builder()
            .entityId(entityId)
            .type(type)
            .metaId(metaId)
            .metaType(metaType)
            .data(feed)
            .section(Constants.TRANSACTION)
            .state(State.COMPLETED)
            .createdAt(createdAt)
            .updatedAt(updatedAt)
            .build();
    }

    private Notification toNotification(
            final String entityId,
            final TstoreFeed feed,
            final String metaId,
            final String unitId) {
        String type = feed.getEntityType().name();
        return Notification.builder()
            .notificationReceivers(Map.of())
            .unitId(unitId)
            .entityId(entityId)
            .namespaceId(tstoreClientConfig.getNamespace())
            .type(type)
            .metaId(metaId)
            .notificationChannels(List.of())
            .createdAt(new Date())
            .build();
    }
    private NotificationEntity getNotificationEntity(
            final BaseEntity baseEntity,
            final Notification notification,
            TstoreFeed feed) {
        return NotificationEntity.builder()
            .notification(notification)
            .baseEntity(baseEntity)
            .schemaParams(getSchemaParams(feed))
            .createdAt(new Date())
            .build();
    }

}
