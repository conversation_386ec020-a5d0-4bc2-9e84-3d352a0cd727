package com.phonepe.merchant.platform.stratos.models.row.requests;

import com.phonepe.merchant.platform.stratos.models.disputes.commons.filters.DateRangeFilter;
import com.phonepe.merchant.platform.stratos.models.row.RowTypeDto;
import java.util.Set;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RowHistoryRequest {

    @NotEmpty
    private Set<RowTypeDto> rowTypes;
    @NotNull
    private DateRangeFilter dateRangeFilter;
}

