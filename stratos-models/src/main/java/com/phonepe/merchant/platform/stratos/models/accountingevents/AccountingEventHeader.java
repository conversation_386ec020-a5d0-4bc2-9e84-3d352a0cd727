package com.phonepe.merchant.platform.stratos.models.accountingevents;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountingEventHeader {

    @NotNull
    @NonNull
    private AccountingEventType eventType;

    @NonNull
    @NotEmpty
    private String transactionId;

    @NonNull
    @NotEmpty
    private String externalTransactionId;

    @NonNull
    @NotEmpty
    private String paymentId;

    private long transactionDate;

    private boolean compositeEvent;
}
