package com.phonepe.merchant.platform.stratos.models.disputemanagement.responses;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.PaginationDetails;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DisputeSummaries {
    private String merchantId;
    private List<DisputeSummary> disputes;
    private PaginationDetails paginationDetails;
}
