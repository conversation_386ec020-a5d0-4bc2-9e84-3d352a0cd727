package com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.impl;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute.Fields;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.repositories.CrudRepository;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import io.appform.dropwizard.sharding.dao.RelationalDao;

import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.criterion.DetachedCriteria;
import org.hibernate.criterion.Restrictions;

@Slf4j
@Singleton
public class DisputeRepository extends CrudRepository<Dispute> {


    /*
    This constructor is different from other repositories to power ShardCalculator<String> in DBModule.java
     */
    @Inject
    public DisputeRepository(final RelationalDao<Dispute> relationalDao) {
        super(
            relationalDao,
            null
        );
    }

    public Optional<Dispute> select(final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage currentDisputeStage) {
        final var detachedCriteria = DetachedCriteria.forClass(Dispute.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeType, disputeType))
            .add(Restrictions.eq(Fields.currentDisputeStage, currentDisputeStage));
        return this.select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public Optional<Dispute> select(final String disputeId, final String transactionReferenceId) {
        final var detachedCriteria = DetachedCriteria.forClass(Dispute.class)
            .add(Restrictions.eq(Fields.disputeId, disputeId));
        return this.select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public Optional<Dispute> select(final String transactionReferenceId,
        final DisputeType disputeType) {
        final var detachedCriteria = DetachedCriteria.forClass(Dispute.class)
            .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId))
            .add(Restrictions.eq(Fields.disputeType, disputeType));
        return this.select(transactionReferenceId, detachedCriteria).stream()
            .findFirst();
    }

    public List<Dispute> select(final String transactionReferenceId) {
        final var detachedCriteria = DetachedCriteria.forClass(Dispute.class)
                .add(Restrictions.eq(Fields.transactionReferenceId, transactionReferenceId));
        return this.select(transactionReferenceId, detachedCriteria);
    }
}
