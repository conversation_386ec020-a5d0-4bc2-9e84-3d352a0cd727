package com.phonepe.merchant.platform.stratos.models.udir;

import com.phonepe.payments.upiclientmodel.enums.ComplaintState;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UdirNpciResponseModel {

    private String complaintId;
    private ComplaintState state;
    private String adjFlag;
    private String adjCode;
    private String crn;
    private String adjRemark;
    private String adjReason;
    private String errorCode;
}
