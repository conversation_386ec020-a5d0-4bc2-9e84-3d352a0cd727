package com.phonepe.central.stratos.penalty.escalation;

import lombok.Getter;

public enum EscalationLevel {

    L4(EscalationLevel.L4_MESSAGE_TEXT, null) {
        @Override
        public <T> T accept(EscalationLevelVisitor<T> visitor) {
            return visitor.visitL4();
        }
    },
    L3(EscalationLevel.L3_MESSAGE_TEXT, EscalationLevel.L4) {
        @Override
        public <T> T accept(EscalationLevelVisitor<T> visitor) {
            return visitor.visitL3();
        }
    },
    L2(EscalationLevel.L2_MESSAGE_TEXT, EscalationLevel.L3) {
        @Override
        public <T> T accept(EscalationLevelVisitor<T> visitor) {
            return visitor.visitL2();
        }
    },
    L1(EscalationLevel.L1_MESSAGE_TEXT, EscalationLevel.L2) {
        @Override
        public <T> T accept(EscalationLevelVisitor<T> visitor) {
            return visitor.visitL1();
        }
    };

    @Getter
    private final String message;

    @Getter
    private final EscalationLevel next;

    EscalationLevel(String message,EscalationLevel next){
        this.message=message;
        this.next=next;
    }

    public abstract <T> T accept(EscalationLevelVisitor<T> visitor);

    public interface EscalationLevelVisitor<T> {
        T visitL1();
        T visitL2();
        T visitL3();
        T visitL4();
    }

    private static final String L1_MESSAGE_TEXT = "2 days before probable is converted into penalty";
    private static final String L2_MESSAGE_TEXT = "1 day before probable is converted into penalty";
    private static final String L3_MESSAGE_TEXT = "when probable is converted into penalty";
    private static final String L4_MESSAGE_TEXT = "1 day after penalty is created";

}
