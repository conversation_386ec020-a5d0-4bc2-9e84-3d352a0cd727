package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.enums.ToaReceiverInstrument;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@EqualsAndHashCode(callSuper = true)
public class TOAContext extends TransitionContext {

    @NotEmpty
    private String merchantOrderId;

    @NotEmpty
    private String toaTransactionId;

    @NotNull
    private ToaReceiverInstrument toaReceiverInstrument;
}
