package com.phonepe.central.stratos.notification.impl;

import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PenaltyDueNotificationTest {

    @Mock
    private PenaltyProbable probable;

    @Mock
    private ChangeNotificationVisitor<String> visitor;

    @InjectMocks
    private PenaltyDueNotification penaltyDueNotification;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        penaltyDueNotification= PenaltyDueNotification.builder()
                .probable(probable)
                .build();
    }

    @Test
    public void testPenaltyDueNotificationCreation() {
        assertNotNull(penaltyDueNotification);
        assertEquals(probable, penaltyDueNotification.getProbable());
    }

    @Test
    public void testAcceptMethod() {
        when(visitor.visit(any(PenaltyDueNotification.class))).thenReturn("Visited");

        String result = penaltyDueNotification.accept(visitor);
        assertEquals("Visited", result);

        verify(visitor).visit(penaltyDueNotification);
    }
}

