package com.phonepe.merchant.platform.stratos.models.files.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Row Id",
    "Row State",
    "Code",
    "UPI Transaction Id",
    "Beneficiery",
    "Adjtype",
    "Txndate",
    "Remitter",
    "Adjdate",
    "Txnamount",
    "Compensation amount",
    "Adjamount",
    "RRN",
})
public class UpiFileHistoryProcessingSummaryRow {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("UPI Transaction Id")
    private String upiTransactionId;

    @JsonProperty("Beneficiery")
    private String beneficiary;

    @JsonProperty("Adjtype")
    private String adjType;

    @JsonProperty("Txndate")
    private String txnDate;

    @JsonProperty("Remitter")
    private String remitter;

    @JsonProperty("Adjdate")
    private String adjDate;

    @JsonProperty("Txnamount")
    private String txnAmount;

    @JsonProperty("Compensation amount")
    private String compensationAmount;

    @JsonProperty("Adjamount")
    private String adjAmount;

    @JsonProperty("RRN")
    private String rrn;
}
