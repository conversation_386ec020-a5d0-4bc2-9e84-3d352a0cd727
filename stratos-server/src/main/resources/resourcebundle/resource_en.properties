deleteButton=delete
SUCCESS=Success
INTERNAL_SERVER_ERROR=Internal Server Error
SERIALIZATION_ERROR=Internal Serialization Error
DESERIALIZATION_ERROR=Internal Deserialization Error
DB_ERROR=Internal Database Error
COMMUNICATION_ERROR=Internal Communication Error
AEROSPIKE_WRITE_ERROR=Internal Aerospike Write Error
AEROSPIKE_READ_ERROR=Internal Aerospike Read Error
AEROSPIKE_DELETE_ERROR=Internal Aerospike Delete Error
INIT_ERROR=Unable to initialize
PAYMENTS_CLIENT_ERROR=Payments Client Error
FEED_PUBLISH_ERROR=Failed to publish feed object
QUEUE_ERROR=Error publishing in queue
TRANSFORMATION_ERROR=Error in transformation
HYSTRIX_TIMEOUT=Hystrix Timeout
AUTH_ERROR=Authorization Error
OPERATION_NOT_ALLOWED=Operation is not allowed
WRONG_INPUT_ERROR=Invalid Input
INVALID_PAGINATION_PARAMETERS=Invalid pagination parameters
INVALID_MERCHANT_ID=Invalid Merchant ID,
INVALID_FILE=Invalid file
INVALID_ROW=Invalid row
DISPUTE_NOT_FOUND=Dispute Not Found
DISPUTE_WORKFLOW_NOT_FOUND=Dispute Workflow Not Found
EVIDENCE_NOT_FOUND=Evidence Not Found
TRANSITION_NOT_ALLOWED=Transition not allowed
TRANSITION_LOCK_KEY_NOT_FOUND=Transition Lock Key Not Found
UNABLE_TO_ACQUIRE_TRANSITION_LOCK=Unable to acquire transition lock
DISPUTE_STATE_MACHINE_NOT_FOUND=Dispute state machine not found
DISPUTE_DATA_MISMATCH=Dispute data mismatch error
INVALID_DISPUTE_AMOUNT=Invalid Dispute Amount
INVALID_ACCEPTED_AMOUNT=Accepted amount cannot be greater than or equal to disputed amount
INVALID_CONTESTED_AMOUNT=Contested amount cannot be greater than disputed amount
INVALID_CREDIT_AMOUNT=Credit amount is invalid
INVALID_DEBIT_AMOUNT=Debit amount is invalid
FEED_SCHEMA_PARAMS_NOT_FOUND=Schema params not found
EMPTY_COMMENT_NOT_ALLOWED=Comment is mandatory field
COMMENT_LENGTH_EXCEEDED_LIMIT=Comment length exceeded allowed limit
RAISED_ACCOUNTING_EVENT_NOT_FOUND=Accounting event raised with given accounting event id not found
INVALID_UPI_ID=ID is invalid
DUPLICATE_FILE_NAME=Duplicate File Name
FILE_LOCK=Same file is under process
INVALID_FILE_NAME_FORMAT=Invalid File Name format
FILE_NOT_FOUND=File Not Found
UNSUPPORTED_TRANSACTION=Unsupported Transaction
UNSUPPORTED_DISPUTE_FILE=Unsupported Dispute File
INVALID_TRANSITION=Transition is invalid
INVALID_TRANSACTION=Transaction is invalid
INVALID_TRANSACTION_AMOUNT=Invalid Transaction amount
INVALID_END_DATE=End date cannot be after current date
PREVIOUS_DISPUTE_WORKFLOW_NOT_ENDED=Previous Dispute Workflow not Ended
INVALID_CHARGEBACK_REASON_CODE=CHARGEBACK REASON CODE IS INVALID
INVALID_PAYMENTS_ID=PAYMENTS TRANSACTION ID IS INVALID
DUPLICATE_CHARGEBACK=Chargeback Already Present
ID_GENERATION_FAILED=ID generation failed
UNSUPPORTED_DISPUTE_STAGE=Unsupported Dispute Stage
UNSUPPORTED_DISPUTE_TYPE=Unsupported Dispute Type
INVALID_UDIR_COMPLAINT=Invalid Udir Complaint
ROW_NOT_FOUND=Row not found
FIRST_LEVEL_DISPUTE_NOT_FOUND=First level dispute Not Found
UNSUPPORTED_PAYEE=Payee not Supported
INVALID_DISPUTE_TYPE=Invalid Dispute Type
ENUM_CLASS_NULL=Enum Class is null
FILTER_NOT_SUPPORTED=Filter Not Supported
INVALID_REFUND_ID=Invalid Refund Id
#TOA Errors
DUPLICATE_TOA=TOA Already present
INVALID_TOA=Invalid TOA
PAYMENT_INITIATION_FAILED=Payment initiation failed
PAYMENT_MERCHANT_TXN_STATUS_CHECK_FAILED=Payment merchant transaction status check failed 
TOA_RETRY_NOT_SUPPORTED=Toa Retry not supported
TOA_COMPLETE_EXTERNALLY_NOT_SUPPORTED=Process TOA externally not supported
TOA_INVALID_RE_INITIATE=Invalid re-initiation of a TOA
KS_ALREADY_ENGAGED=A Kill switch is already engaged
KS_NOT_ENGAGED=A Kill switch is not engaged
NOT_HUMAN_USER=Bad request, not an human user.
UNSUPPORTED_PAYMENT_DESTINATION=Unsupported payment destination.
FETCH_MERCHANT_ID_ERROR=Error while fetching merchant Id
MIDDLE_MAN_MERCHANT_DATA_NULL=Middleman Merchant data is null.
EDC_TRANSACTION_DETAIL_ERROR=Error while getting transaction detail from EDC
PGT_TRANSACTION_DETAIL_ERROR=Error while getting transaction detail from PG transport service
EDC_CLIENT_ERROR=EDC Client Error
INVALID_KRATOS_ACTION=Invalid Action
RECONCILE_FAILURE=Reconcile Failure
UNSUPPORTED_DISPUTE_CREATION=Dispute creation is not supported for this dispute type
UNSUPPORTED_DISPUTE_VALIDATION=Dispute validation is not supported for this dispute type
DISPUTE_METADATA_NOT_FOUND=Dispute metadata not found
UNABLE_TO_ENRICH_CHARGEBACK=Unable to enrich
TTL_BREACHED=Ttl already breached
PRE_ARB_NOT_SUPPORTED=Pre-arb not supported
KAIZEN_ACTION_DETAIL_NOT_FOUND=Kaizen action detail not found
KAIZEN_WORKFLOW_NOT_FOUND=Kaizen workflow not found
VALIDATION_FAILURE = Validation faliure 
KAIZEN_CALLBACK_ERROR = Error while processing kaizen callback
KAIZEN_WORKFLOW_ABORT = Kaizen workflow aborted
HOLD_NOT_FOUND = Amount not hold for this dispute
DUPLICATE_DISPUTE = Dispute already exist
CHARGEBACK_ALREADY_EXIST = Chargeback already exist for this transaction
REFUND_ALREADY_EXIST = Refund already exist for this transaction
FIRST_LEVEL_NOT_IN_TERMINAL_STATE = First Level not in terminal state
UNSUPPORTED_DISPUTE = Unsupported dispute
KRATOS_ACTOR_FAILURE = Kratos actor failure
EDC_ACTOR_FAILURE = Edc actor failure
MAKER_REQUEST_FAILED = Failed to raise maker request to warden
CALLBACK_PROCESSING_FAILED = Failed to process callback received at Stratos
PROBABLE_RECONCILE_ERROR=Error while reconcile probable
PROBABLE_GET_ERROR=Error while getting probable
PENALTY_GET_ERROR=Error while getting penalty
PROBABLE_REGISTER_ERROR=Error while registering probable
PROBABLE_ALREADY_PRESENT=Penalty probable already exist
PROBABLE_ALREADY_PRESENT_WITH_PREVIOUS_VERSION=Penalty probable already exist with previous version classes
PENALTY_CLASS_ALREADY_PRESENT=Penalty class already present with TenantName %s TenantSubCategory %s server
UNABLE_TO_CREATE_PENALTY_CLASS=Unable to create penalty class for Tenant
UNABLE_TO_UPDATE_PENALTY_CLASS=Unable to update penalty class for Tenant
UNABLE_TO_GET_PENALTY_SCHEDULE_TIME=Unable to get penalty schedule time
PENALTY_CLASS_CRITERIA_NOT_MET=Penalty class criteria disable criteria met or trigger criteria not met
PENALTY_DISBURSEMENT_ERROR=Error while disbursing penalty
PENALTY_CLASS_NAME_ALREADY_EXISTS= Penalty class name already exists for tenant and sub category
PENALTY_TENANT_INFO_LIST_IS_EMPTY= Don't have any authorized tenant as tenant info list is empty in repository scan
PENALTY_TENANT_NOT_AUTHORIZED=Tenant not authorized to penalty action
PENALTY_RECONCILE_ERROR=Penalty reconcile error
PENALTY_CLASS_ACTIVATION_FAILED=Penalty class activation failed
PENALTY_CLASS_NOT_FOUND=Penalty class not found or not active
PENALTY_CLASS_CLIENT_RESOLUTION_CONFIG_NOT_FOUND=Penalty class client resolution config not found
PENALTY_CLASS_DUPLICATION_FAILED=Penalty class duplication failed
PENALTY_CLASS_ACTIVE_UPDATE= Penalty class active update failed
PENALTY_ALREADY_PRESENT= Penalty entity already present for probable
PENALTY_CLASS_NOT_PRESENT = penalty class not present
NOTIFICATION_SERVICE_ERROR = Error in sending notification
NOTIFICATION_SENDING_ERROR = Error in sending email notification through zencast
SERVICE_CLIENT_ERROR = Error while calling external service
TEMPLATE_NOT_FOUND = Email template not found
PENALTY_ALREADY_PRESENT_WITH_PREVIOUS_CLASS_VERSION= Penalty entity already present with previous class version
FRA_REQUEST_CREATION_FAILED = Fra request creation from disputeWorkflow error 
ESCALATION_PUBLISH_ERROR = Error while publishing escalation
ESCALATION_NOT_FOUND = Escalation not found
DISPUTE_CREATION_LOCK = Error while creating lock for dispute creation
NON_EXTERNAL_MERCHANT_TRANSACTION = Transaction not belongs to external merchant
PENALTY_CLIENT_RESOLUTION_ERROR= Error while resolving client service
FRAUD_CHARGEBACK_NOT_ALLOWED =fraud chargeback is not allowed 
PAYMENT_NOT_IN_COMPLETED_STATE = payment not in completed state
RESET_NOT_ALLOWED= Reset not allowed after recovery is done from merchant
