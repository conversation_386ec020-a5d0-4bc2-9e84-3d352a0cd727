package com.phonepe.merchant.platform.stratos.models.udir.response;


import static com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto.Names.UDIR_OUTGOING_COMPLAINT;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonSubTypes.Type;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeInfo.As;
import com.fasterxml.jackson.annotation.JsonTypeInfo.Id;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeStageDto;
import com.phonepe.merchant.platform.stratos.models.udir.requests.UdirDisputeTypeDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonTypeInfo(use = Id.NAME, include = As.EXISTING_PROPERTY, property = "disputeType", visible = true)
@JsonSubTypes(value = {
    @Type(value = UdirOutgoingRaiseComplaintResponse.class, name = UDIR_OUTGOING_COMPLAINT)
})
public class UdirRaiseComplaintResponse {

    private UdirDisputeTypeDto disputeType;
    private DisputeStageDto disputeStage;
    private String transactionId;

    public UdirRaiseComplaintResponse(final UdirDisputeTypeDto disputeType) {
        this.disputeType = disputeType;
    }
}
