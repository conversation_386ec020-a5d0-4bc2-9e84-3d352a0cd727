package com.phonepe.central.stratos.penalty.response;

import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.meta.TenantInfo;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldNameConstants;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@FieldNameConstants
public class PenaltyResponse {

    @NotBlank
    private Penalty penalty;

    @NotBlank
    private TenantInfo tenantInfo;

}
