package com.phonepe.merchant.platform.stratos.models.commons;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DateRange {

    @NotNull
    private LocalDate startDate;

    @NotNull
    private LocalDate endDate;

    @JsonCreator
    public DateRange(
        @JsonProperty("startDate") final LocalDate startDate,
        @JsonProperty("endDate") final LocalDate endDate) {
        if (endDate.isBefore(startDate)) {
            throw new IllegalArgumentException(
                "Invalid Date Range - End Date should be after Start Date");
        }
        if (endDate.isAfter(LocalDate.now())) {
            throw new IllegalArgumentException(
                "Invalid Date Range - End Date should be not more than today");
        }
        this.startDate = startDate;
        this.endDate = endDate;
    }
}
