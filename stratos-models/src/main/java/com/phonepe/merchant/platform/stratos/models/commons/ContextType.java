package com.phonepe.merchant.platform.stratos.models.commons;

import lombok.experimental.UtilityClass;

public enum ContextType {
    EMPTY_CONTEXT,
    PARTIAL_ACCEPTANCE_CONTEXT,
    INSTITUTIONAL_CREDIT_CONTEXT,
    COMMENT_CONTEXT,
    INSTITUTIONAL_DEBIT_CONTEXT,
    TOA_CONTEXT,
    REFUND_CONTEXT
    ;

    @UtilityClass
    public static final class Names {

        public static final String EMPTY_CONTEXT = "EMPTY_CONTEXT";
        public static final String PARTIAL_ACCEPTANCE_CONTEXT = "PARTIAL_ACCEPTANCE_CONTEXT";
        public static final String INSTITUTIONAL_CREDIT_CONTEXT = "INSTITUTIONAL_CREDIT_CONTEXT";
        public static final String COMMENT_CONTEXT = "COMMENT_CONTEXT";
        public static final String INSTITUTIONAL_DEBIT_CONTEXT = "INSTITUTIONAL_DEBIT_CONTEXT";
        public static final String TOA_CONTEXT = "TOA_CONTEXT";
        public static final String REFUND_CONTEXT = "REFUND_CONTEXT";
    }
}
