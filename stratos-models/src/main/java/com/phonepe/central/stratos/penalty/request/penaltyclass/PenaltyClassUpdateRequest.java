package com.phonepe.central.stratos.penalty.request.penaltyclass;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import javax.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@EqualsAndHashCode
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.EXISTING_PROPERTY, property = "type")
@JsonSubTypes({ @JsonSubTypes.Type(name = PenaltyClassUpdateType.BASIC_UPDATE_TEXT, value = PenaltyClassBasicUpdateRequest.class),
        @JsonSubTypes.Type(name = PenaltyClassUpdateType.CRITERIA_UPDATE_TEXT, value = PenaltyClassCriteriaUpdateRequest.class), })
public abstract class PenaltyClassUpdateRequest {

    private final PenaltyClassUpdateType type;
    @NotBlank
    private String penaltyClassId;

    public abstract <T> T accept(PenaltyClassUpdateRequestVisitor<T> visitor);

    public interface PenaltyClassUpdateRequestVisitor<T> {

        T visit(final PenaltyClassBasicUpdateRequest updateRequest);

        T visit(final PenaltyClassCriteriaUpdateRequest updateRequest);

    }
}


