package com.phonepe.merchant.platform.stratos.models.commons.contexts;

import com.phonepe.merchant.platform.stratos.models.commons.TransitionContext;
import com.phonepe.merchant.platform.stratos.models.disputes.wallet.RefundReceiverInstrument;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RefundContext extends TransitionContext {

    @NotEmpty
    private String originalTransactionId;

    @NotEmpty
    private String refundTransactionId;

    @NotNull
    private RefundReceiverInstrument refundReceiverInstrument;
}
