package com.phonepe.central.stratos.penalty.server.integrationTests.resources;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.phonepe.central.stratos.notification.EmailCommunicationRequest;
import com.phonepe.central.stratos.penalty.Penalty;
import com.phonepe.central.stratos.penalty.escalation.EscalationLevel;
import com.phonepe.central.stratos.penalty.escalation.EscalationType;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;
import com.phonepe.central.stratos.penalty.request.DateRangeRequest;
import com.phonepe.central.stratos.penalty.server.queue.actors.EscalatedNotificationActor;
import com.phonepe.central.stratos.penalty.server.queue.messages.EscalatedEntityNotificationQueueMessage;
import com.phonepe.central.stratos.penalty.server.service.EscalationService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyProbableService;
import com.phonepe.central.stratos.penalty.server.service.PenaltyService;
import com.phonepe.central.stratos.penalty.server.utils.TestDataUtils;
import com.phonepe.merchant.platform.stratos.server.LoadOnlyOnClassLevelBaseTest;
import com.phonepe.merchant.platform.stratos.server.core.events.EventIngester;
import com.phonepe.merchant.platform.stratos.server.core.queue.ActionType;
import io.appform.dropwizard.actors.ConnectionRegistry;
import io.appform.dropwizard.actors.actor.ActorConfig;
import io.appform.dropwizard.actors.actor.ConsumerConfig;
import io.appform.dropwizard.actors.actor.MessageMetadata;
import io.appform.dropwizard.actors.exceptionhandler.ExceptionHandlingFactory;
import io.appform.dropwizard.actors.retry.RetryStrategyFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;
import java.util.stream.Collectors;

import static com.phonepe.central.stratos.penalty.server.integrationTests.resources.EscalationResourceIntergrationTest.getDateRangeRequest;
import static org.mockito.ArgumentMatchers.argThat;

public class EscalatedNotificationHandlerTest extends LoadOnlyOnClassLevelBaseTest {

    private EscalatedNotificationActorImpl escalatedNotificationActor;

    private PenaltyService penaltyService;

    private PenaltyProbableService penaltyProbableService;

    @Mock
    private EscalationService escalationService;

    @Mock
    private ConnectionRegistry connectionRegistry;
    @Mock
    private RetryStrategyFactory retryStrategyFactory;
    @Mock
    private ExceptionHandlingFactory exceptionHandlingFactory;
    @Mock
    private EventIngester eventIngester;

    private static final String penaltyClassId1 = "PENALTY_CLASS_ID_1";

    @BeforeEach
    public void init(){
        truncateDb();
        ObjectMapper mapper = guiceInjector.getInstance(ObjectMapper.class);
        penaltyService = Mockito.mock(PenaltyService.class);
        penaltyProbableService = Mockito.mock(PenaltyProbableService.class);

        Map<ActionType, ActorConfig> actorConfigMap = new EnumMap<ActionType, ActorConfig>(ActionType.class);
        ActorConfig actorConfig = ActorConfig.builder().consumer(ConsumerConfig.builder().build()).build();
        actorConfigMap.put(ActionType.ESCALATED_NOTIFICATION_HANDLER, actorConfig);
        escalatedNotificationActor = new EscalatedNotificationActorImpl(actorConfigMap, connectionRegistry, mapper,
                retryStrategyFactory, exceptionHandlingFactory, penaltyService, penaltyProbableService,
                escalationService, eventIngester);
    }

    @Test
    public void TestPenaltyEscalationLevelL1() {
        String emailId1 = "<EMAIL>";
        PenaltyProbable probable = TestDataUtils.getPenaltyProbable(penaltyClassId1);
        DateRangeRequest dateRangeRequest = getDateRangeRequest();
        var communicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId1))
                .build();
        var queueMessage = EscalatedEntityNotificationQueueMessage.builder()
                .mappingId("mappingId")
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L1)
                .escalatedEntities(List.of(probable))
                .communicationRequest(communicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();
        Mockito.doNothing().when(penaltyProbableService).sendNotificationForPenaltyDueShortly(Mockito.any(), Mockito.any(), Mockito.any());
        escalatedNotificationActor.handle(queueMessage, new MessageMetadata(false));
        Mockito.verify(penaltyProbableService).sendNotificationForPenaltyDueShortly(
                argThat(new PenaltyProbableListMatcher(List.of(probable))),
                argThat(argument -> Objects.equals(argument, communicationRequest)),
                argThat(argument -> Objects.equals(argument, dateRangeRequest))
        );
    }

    @Test
    public void TestPenaltyEscalationLevelL2() {
        String emailId2 = "<EMAIL>";
        PenaltyProbable probable = TestDataUtils.getPenaltyProbable(penaltyClassId1);
        DateRangeRequest dateRangeRequest = getDateRangeRequest();
        var communicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId2))
                .build();
        var queueMessage = EscalatedEntityNotificationQueueMessage.builder()
                .mappingId("mappingId")
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L2)
                .escalatedEntities(List.of(probable))
                .communicationRequest(communicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();
        Mockito.doNothing().when(penaltyProbableService).sendNotificationForPenaltyDueShortly(Mockito.any(), Mockito.any(), Mockito.any());
        escalatedNotificationActor.handle(queueMessage, new MessageMetadata(false));
        Mockito.verify(penaltyProbableService).sendNotificationForPenaltyDueShortly(
                argThat(new PenaltyProbableListMatcher(List.of(probable))),
                argThat(argument -> Objects.equals(argument, communicationRequest)),
                argThat(argument -> Objects.equals(argument, dateRangeRequest))
        );
    }

    @Test
    public void TestPenaltyEscalationLevelL3() {
        String emailId3 = "<EMAIL>";
        Penalty penalty = TestDataUtils.getPenalty(penaltyClassId1);
        DateRangeRequest dateRangeRequest = getDateRangeRequest();
        var communicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId3))
                .build();
        var queueMessage = EscalatedEntityNotificationQueueMessage.builder()
                .mappingId("mappingId")
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L3)
                .escalatedEntities(List.of(penalty))
                .communicationRequest(communicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();
        Mockito.doNothing().when(penaltyService).sendNotificationForPenaltiesCreation(Mockito.any(), Mockito.any(), Mockito.any());
        escalatedNotificationActor.handle(queueMessage, new MessageMetadata(false));
        Mockito.verify(penaltyService).sendNotificationForPenaltiesCreation(
                argThat(new PenaltyListMatcher(List.of(penalty))),
                argThat(argument -> Objects.equals(argument, communicationRequest)),
                argThat(argument -> Objects.equals(argument, dateRangeRequest))
        );
    }

    @Test
    public void TestPenaltyEscalationLevelL4() {
        String emailId4 = "<EMAIL>";
        Penalty penalty = TestDataUtils.getPenalty(penaltyClassId1);
        DateRangeRequest dateRangeRequest = getDateRangeRequest();
        var communicationRequest = EmailCommunicationRequest.builder()
                .emailIDs(Set.of(emailId4))
                .build();
        var queueMessage = EscalatedEntityNotificationQueueMessage.builder()
                .mappingId("mappingId")
                .escalationType(EscalationType.PENALTY)
                .escalationLevel(EscalationLevel.L4)
                .escalatedEntities(List.of(penalty))
                .communicationRequest(communicationRequest)
                .dateRangeRequest(dateRangeRequest)
                .build();
        Mockito.doNothing().when(penaltyService).sendNotificationForPenaltiesGrowing(Mockito.any(), Mockito.any(), Mockito.any());
        escalatedNotificationActor.handle(queueMessage, new MessageMetadata(false));
        Mockito.verify(penaltyService).sendNotificationForPenaltiesGrowing(
                argThat(new PenaltyListMatcher(List.of(penalty))),
                argThat(argument -> Objects.equals(argument, communicationRequest)),
                argThat(argument -> Objects.equals(argument, dateRangeRequest))
        );
    }

    class EscalatedNotificationActorImpl extends EscalatedNotificationActor {

        public EscalatedNotificationActorImpl(Map<ActionType, ActorConfig> actorConfigMap,
                                              ConnectionRegistry connectionRegistry, ObjectMapper mapper,
                                              RetryStrategyFactory retryStrategyFactory,
                                              ExceptionHandlingFactory exceptionHandlingFactory,
                                              PenaltyService penaltyService,
                                              PenaltyProbableService penaltyProbableService,
                                              EscalationService escalationService,
                                              EventIngester eventIngester) {
            super(actorConfigMap, connectionRegistry, mapper, retryStrategyFactory, exceptionHandlingFactory,
                    penaltyService, penaltyProbableService, escalationService, eventIngester);
        }

        @Override
        public boolean handle(final EscalatedEntityNotificationQueueMessage message,
                              final MessageMetadata messageMetadata) {
            return super.handle(message,messageMetadata);
        }
    }

    public static class PenaltyListMatcher implements ArgumentMatcher<List<Penalty>> {
        private final List<Penalty> expectedPenalties;

        public PenaltyListMatcher(List<Penalty> expectedPenalties) {
            this.expectedPenalties = expectedPenalties;
        }

        @Override
        public boolean matches(List<Penalty> actualPenalties) {
            return actualPenalties.stream().map(Penalty::getPenaltyId).collect(Collectors.toSet()).equals(
                    expectedPenalties.stream().map(Penalty::getPenaltyId).collect(Collectors.toSet()));
        }
    }

    public static class PenaltyProbableListMatcher implements ArgumentMatcher<List<PenaltyProbable>> {
        private final List<PenaltyProbable> expectedProbables;

        public PenaltyProbableListMatcher(List<PenaltyProbable> expectedProbables) {
            this.expectedProbables = expectedProbables;
        }

        @Override
        public boolean matches(List<PenaltyProbable> actualProbables) {
            return actualProbables.stream().map(PenaltyProbable::getProbableId).collect(Collectors.toSet()).equals(
                    expectedProbables.stream().map(PenaltyProbable::getProbableId).collect(Collectors.toSet()));
        }
    }
}
