package com.phonepe.central.stratos.notification.impl;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.phonepe.central.stratos.notification.ChangeNotificationVisitor;
import com.phonepe.central.stratos.notification.ChangeNotitification;
import com.phonepe.central.stratos.notification.NotificationType;
import com.phonepe.central.stratos.penalty.probables.PenaltyProbable;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class PenaltyProbableNotification extends ChangeNotitification {
    @Valid
    @NotNull
    private PenaltyProbable probable;

    @Builder
    @JsonCreator
    public PenaltyProbableNotification(@JsonProperty("probable") final PenaltyProbable probable) {
        super(NotificationType.PENALTY_PROBABLE_IDENTIFIED);
        this.probable = probable;
    }

    @Override
    public <T> T accept(ChangeNotificationVisitor<T> visitor) {
        return visitor.visit(this);
    }

}
