package com.phonepe.central.stratos.notification;

import java.util.Set;
import javax.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
public class EmailCommunicationRequest extends CommunicationRequest {

    @NotEmpty
    private Set<String> emailIDs;

    @Builder
    public EmailCommunicationRequest(Set<String> emailIDs){
        super(CommunicationType.EMAIL);
        this.emailIDs = emailIDs;
    }

    @Override
    public void accept(CommunicationRequestVisitor visitor) {
        visitor.visit(this);
    }
}
