package com.phonepe.merchant.platform.stratos.models.files.responses;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonPropertyOrder(value = {
    "Row Id",
    "Row State",
    "Code",
    "CB Received Date",
    "Source",
    "Tenant",
    "Terminal Id",
    "Merchant Id",
    "Bank Disputed Amount",
    "RRN",
    "Dispute Reason",
})
public class EdcFileHistoryProcessingSummaryRow {

    @JsonProperty("Row Id")
    private String rowId;

    @JsonProperty("Row State")
    private String rowState;

    @JsonProperty("Code")
    private String code;

    @JsonProperty("CB Received Date")
    private String cbReceivedDate;

    @JsonProperty("Source")
    private String source;

    @JsonProperty("Tenant")
    private String tenant;

    @JsonProperty("Terminal Id")
    private String terminalId;

    @JsonProperty("Merchant Id")
    private String merchantId;

    @JsonProperty("Bank Disputed Amount")
    private String bankDisputedAmount;

    @JsonProperty("RRN")
    private String rrn;

    @JsonProperty("Dispute Reason")
    private String disputeReason;

}
