package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files;

import com.github.fge.jackson.JsonLoader;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.server.core.configs.FileConfig;
import com.phonepe.merchant.platform.stratos.server.core.handlebars.HandleBarsService;
import com.phonepe.merchant.platform.stratos.server.core.helpers.files.rows.FileRowProcessor;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.FinancialDisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeStage;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowEvent;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeWorkflowStateNonMandatoryVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.FileType;
import com.phonepe.merchant.platform.stratos.server.core.models.MerchantResolver;
import com.phonepe.merchant.platform.stratos.server.core.models.MerchantResolverVisitor;
import com.phonepe.merchant.platform.stratos.server.core.services.DisputeService;
import com.phonepe.merchant.platform.stratos.server.core.services.MerchantMandateService;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.files.models.FileRowMeta;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils.ValidationUtils;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PartyMerchantIdVisitor;
import com.phonepe.models.payments.pay.ReceivedPayment;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.mandate.PaymentMandateContextVisitor;
import com.phonepe.models.payments.pay.mandate.PaymentMandatePayContext;
import com.phonepe.models.payments.pay.mandate.PaymentMandateRedeemContext;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static org.reflections.Reflections.log;

public abstract class BaseFileRowProcessor<T> implements FileRowProcessor {

    private final HandleBarsService handleBarsService;
    private final DisputeService disputeService;

    private final MerchantMandateService merchantMandateService;

    private static final List<DisputeWorkflowEvent> CREATE_ENTRY_EVENT = List.of(
        DisputeWorkflowEvent.CREATE_ENTRY);


    public BaseFileRowProcessor(final HandleBarsService handleBarsService,
        final DisputeService disputeService, final MerchantMandateService merchantMandateService) {
        this.handleBarsService = handleBarsService;
        this.disputeService = disputeService;
        this.merchantMandateService = merchantMandateService;
    }

    @Override
    public void process(final String fileId, final FileType fileType, final DisputeType disputeType,
        final String content, final FileConfig fileConfig)
        throws Exception { // NOSONAR
        final var disputeFileConfig = fileConfig.getDisputeFileConfig();

        // Dispute File Row Meta
        final var fileRowJson = handleBarsService
            .transform(disputeFileConfig.getMetaConfig(),
                JsonLoader.fromString(content));
        final var fileRowMeta = MapperUtils
            .deserialize(fileRowJson, FileRowMeta.class);

        // DisputeWorkflow
        final var disputeWorkflowJson = handleBarsService
            .transform(disputeFileConfig.getDisputeWorkflowConfig(),
                JsonLoader.fromString(content));
        final var disputeWorkflowFilePojo = MapperUtils
            .deserialize(disputeWorkflowJson, FinancialDisputeWorkflow.class);

        // Dispute
        final var disputeJson = handleBarsService.transform(disputeFileConfig.getDisputeConfig(),
            JsonLoader.fromString(content));
        final var disputeFilePojo = MapperUtils.deserialize(disputeJson, Dispute.class);

        final var instrumentTransactionId = disputeFilePojo.getInstrumentTransactionId();

        final var transactionDetails = getTransactionDetails(instrumentTransactionId, fileRowMeta);

        final var dispute = getDispute(disputeWorkflowFilePojo, transactionDetails, disputeFilePojo,
            disputeType, instrumentTransactionId);

        final var disputeWorkflow = enrichAndGetDisputeWorkflow(transactionDetails,
            disputeWorkflowFilePojo, fileId, disputeType, fileConfig, dispute.getDisputeId());

        checkDuplicateDispute(disputeWorkflow.getTransactionReferenceId(),
            disputeWorkflow.getDisputeType(), disputeWorkflow.getDisputeStage(),
            disputeWorkflow.getCurrentEvent());

        final var operationResult = triggerEvent(disputeWorkflow, dispute, transactionDetails);

        if (operationResult == null) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_TRANSITION,
                Map.of(Constants.MESSAGE,
                    String.format("Invalid start state for %s from %s",
                        disputeWorkflow.getTransactionReferenceId(),
                        disputeWorkflow.getCurrentState()),
                    Constants.DISPUTE_TYPE, disputeWorkflow.getDisputeType()
                )
            );
        }
        processMetadata(fileRowMeta, dispute, disputeWorkflow);
    }

    public void processMetadata(
            FileRowMeta fileRowMeta,
            Dispute dispute,
            DisputeWorkflow disputeWorkflow){
        //NOOP
    }

    public abstract DisputeWorkflow enrichAndGetDisputeWorkflow(
        T transactionDetails,
        FinancialDisputeWorkflow disputeWorkflowFilePojo, String fileId, DisputeType disputeType,
        FileConfig fileConfig, String disputeId);


    public abstract T getTransactionDetails(String instrumentTransactionId,
        FileRowMeta fileRowMeta);

    public abstract Dispute getDispute(DisputeWorkflow disputeWorkflow,
        T transactionDetail, Dispute disputeFilePojo, DisputeType disputeType,
        String instrumentTransactionId);

    public Boolean triggerEvent(DisputeWorkflow disputeWorkflow, Dispute dispute, T transactionDetails) {

        return disputeWorkflow.getCurrentState()
            .accept(new DisputeWorkflowStateNonMandatoryVisitor<>() {
                @Override
                public Boolean visitReceived() {
                    if (!DisputeType.TOAS.contains(dispute.getDisputeType())) {
                        ValidationUtils.validateEntryDisputeAmount(disputeWorkflow, dispute, disputeService);
                    }
                    disputeService.createEntry(dispute, disputeWorkflow);
                    return true;
                }
            });
    }

    private void checkDuplicateDispute(final String transactionReferenceId,
        final DisputeType disputeType,
        final DisputeStage disputeStage,
        final DisputeWorkflowEvent currentEvent) {
        if (CREATE_ENTRY_EVENT.contains(currentEvent)
            && disputeService.getDisputeWorkflowOptional(transactionReferenceId, disputeType,
            disputeStage).isPresent()) {
            if (DisputeType.TOAS.contains(disputeType)) {
                throw DisputeExceptionUtil.error(StratosErrorCodeKey.DUPLICATE_TOA, Map.of(Constants.MESSAGE,
                    String.format("TOA already registered for transactionId: %s, disputeType: %s, disputeStage: %s",
                        transactionReferenceId, disputeType, disputeStage)));
            }
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.DUPLICATE_CHARGEBACK,
                Map.of(Constants.MESSAGE,
                    String.format(
                        "Chargeback already present for txnId %s, disputeType %s, disputeStage %s",
                        transactionReferenceId, disputeType, disputeStage))
            );
        }
    }

    public String getMerchantIdByTransactionDetails(TransactionDetail transactionDetail) {
        final var receivedPayment = transactionDetail.getReceivedPayment();
        MerchantResolver merchantResolver = getMerchantResolver(receivedPayment);
        return merchantResolver.accept(new MerchantResolverVisitor<String>() {
            @Override
            public String visitPayment() {
                log.info("Calling payment to fetch merchant Id for {}", receivedPayment.getTransactionId());
                return receivedPayment.getTo()
                    .accept(new PartyMerchantIdVisitor(receivedPayment));
            }

            @Override
            public String visitMms() {

                return receivedPayment.getMandateContext().accept(
                    new PaymentMandateContextVisitor<String>() {
                        @Override
                        public String visit(PaymentMandatePayContext paymentMandatePayContext) {
                            throw DisputeExceptionUtil.error(StratosErrorCodeKey.UNSUPPORTED_TRANSACTION,
                                Map.of(Constants.MESSAGE,
                                    "Unsupported PaymentMandateContextType "
                                        + paymentMandatePayContext.getType(),
                                    Constants.TRANSACTION_ID,
                                    receivedPayment.getTransactionId()));
                        }

                        //              Resolving merchant from autopay orchestrator(MMS).
                        //              Generic success false response without any code from MMS would mean that middleman merchant is not applicable and hence fallback should be payments
                        @Override
                        public String visit(
                            PaymentMandateRedeemContext paymentMandateRedeemContext) {
                            log.info("Calling MMS to fetch merchant id for {}", receivedPayment.getTransactionId());
                            try {
                                var response = merchantMandateService.middleManChargebackDetails(
                                    paymentMandateRedeemContext.getMandateId(),
                                    Objects.requireNonNull(
                                        transactionDetail.getSentPayment()).getUserId(),
                                    paymentMandateRedeemContext.getRedemptionId());
                                if (!response.isSuccess() && Objects.isNull(response.getCode()))
                                    return visitPayment();
                                else {
                                    if(!Objects.isNull(response.getData())) {
                                        return response.getData().getMerchantId();
                                    }else {
                                        throw  DisputeExceptionUtil.error(StratosErrorCodeKey.MIDDLE_MAN_MERCHANT_DATA_NULL,
                                            Map.of(Constants.MESSAGE, "Middleman Merchant Data received from MMS is Null",
                                                Constants.TRANSACTION_ID, receivedPayment.getTransactionId()));
                                    }
                                }
                            } catch (Exception e) {
                                throw DisputeExceptionUtil.propagate(
                                    StratosErrorCodeKey.FETCH_MERCHANT_ID_ERROR, e,
                                    Map.of(Constants.MESSAGE,
                                        "Error While Fetching Merchant Id from MMS",
                                        Constants.TRANSACTION_ID, receivedPayment.getTransactionId()));
                            }

                        }
                    });


            }

        });
    }

    private MerchantResolver getMerchantResolver(ReceivedPayment receivedPayment) {
        return Optional.ofNullable(receivedPayment).isPresent() && Optional.ofNullable(
            receivedPayment.getMandateContext()).isPresent() ? MerchantResolver.MMS
            : MerchantResolver.PAYMENT;
    }


}
