package com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.utils;

import com.phonepe.merchant.platform.stratos.models.disputemanagement.responses.EvidenceDetail;
import com.phonepe.merchant.platform.stratos.models.disputes.DisputeSummaryRow;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummary;
import com.phonepe.merchant.platform.stratos.models.disputes.chargebacks.responses.ChargebackSummaryRow;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.DisputeMetadataDto;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.RefundAction;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.TransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.WalletTransactionDetails;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.RefundEligibilityResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeFraActionMetadataResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeMetadataBaseResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeMetadataResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.commons.responses.metadata.DisputeSohMetadataResponse;
import com.phonepe.merchant.platform.stratos.models.disputes.toa.responses.ToaSummary;
import com.phonepe.merchant.platform.stratos.models.error.StratosErrorCodeKey;
import com.phonepe.merchant.platform.stratos.models.files.FileRow;
import com.phonepe.merchant.platform.stratos.models.files.responses.EdcFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.models.files.responses.NBFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.models.files.responses.PgFirstLevelFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.models.files.responses.PgPreArbFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.models.files.responses.UpiFileHistoryProcessingSummaryRow;
import com.phonepe.merchant.platform.stratos.models.row.response.EdcMisRowSummary;
import com.phonepe.merchant.platform.stratos.models.row.response.PgMisRowSummary;
import com.phonepe.merchant.platform.stratos.server.core.clients.kratos.KratosRecommendedAction;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Dispute;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.DisputeWorkflow;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.Row;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.FraudActionDisputeMetadata;
import com.phonepe.merchant.platform.stratos.server.core.mariadb.entities.metadatas.SettlementOnHoldMetadata;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeMetadataType.DisputeMetadataTypeVisitor;
import com.phonepe.merchant.platform.stratos.server.core.models.DisputeType;
import com.phonepe.merchant.platform.stratos.server.core.models.RowContext;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.EdcRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.NotionalCreditPulseMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.PgMisRowMessage;
import com.phonepe.merchant.platform.stratos.server.core.queue.messages.ToaProcessorMessage;
import com.phonepe.merchant.platform.stratos.server.core.utils.Constants;
import com.phonepe.merchant.platform.stratos.server.core.utils.DisputeWorkflowUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.DtoUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.MapperUtils;
import com.phonepe.merchant.platform.stratos.server.core.utils.TypeReferences;
import com.phonepe.merchant.platform.stratos.server.core.utils.exception.DisputeExceptionUtil;
import com.phonepe.merchant.platform.stratos.server.spokes.chargebacks.commons.visitors.PaymentContextMerchantTransactionIdVisitor;
import com.phonepe.merchant.platform.stratos.server.v2.files.fraud.FraudMeta;
import com.phonepe.models.payments.banking.account.ProviderAccountType;
import com.phonepe.models.payments.pay.TransactionDetail;
import com.phonepe.models.payments.pay.context.InitiationMode;
import com.phonepe.models.payments.pay.instrument.WalletPaymentInstrument;
import com.phonepe.models.payments.pay.view.ProcessingRail;
import com.phonepe.models.wallet.common.enums.ReversalCategory;
import com.phonepe.ruleengine.model.integration.FraudAction;
import com.phonepe.services.refund.orchestrator.models.ReversalType;
import com.phonepe.services.refund.orchestrator.models.accounting.MerchantFulfillmentReversalAccountingContext;
import com.phonepe.services.refund.orchestrator.models.accounting.WalletTopupFulfilmentReverseV2AccountingContext;
import com.phonepe.services.refund.orchestrator.models.payments.sources.VpaReversalDestination;
import com.phonepe.services.refund.orchestrator.models.payments.sources.VpaReversalSource;
import com.phonepe.services.refund.orchestrator.models.v1.RefundContextV1;
import com.phonepe.services.refund.orchestrator.models.v1.RefundContextV2;
import com.phonepe.services.refund.orchestrator.models.v1.RefundRequest;
import com.phonepe.services.refund.orchestrator.models.walletinterop.WalletInteropContext;
import com.phonepe.stratos.kaizen.storage.mariadb.entities.metadatas.StoredDocumentUploadWithMetaDataActionMetadata;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@UtilityClass
public class TransformationUtils {

    private static final String ROW_CONTEXT_NULL = "Row Context is Null";
    private static final String CB_RECEIVED_DATE = "CB Received Date";
    private static final String SOURCE = "Source";
    private static final String DISPUTE_REASON = "Dispute Reason";
    private static final Logger log = LoggerFactory.getLogger(TransformationUtils.class);

    public ChargebackSummary toChargebackSummary(final DisputeWorkflow disputeWorkflow) {
        final var dispute = disputeWorkflow.getDispute();
        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);
        return ChargebackSummary.builder()
            .disputeId(dispute.getDisputeId())
            .disputeFileId(disputeWorkflow.getDisputeSourceId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion().name())
            .disputeType(disputeWorkflow.getDisputeType().name())
            .disputeStage(disputeWorkflow.getDisputeStage().name())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .merchantId(dispute.getMerchantId())
            .merchantTransactionId(dispute.getMerchantTransactionId())
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .communicationId(disputeWorkflow.getCommunicationId())
            .transactionAmount(dispute.getTransactionAmount())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .acceptedAmount(financialDisputeWorkflow.getAcceptedAmount())
            .penaltyAmount(financialDisputeWorkflow.getPenaltyAmount())
            .currentState(disputeWorkflow.getCurrentState().name())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .respondBy(disputeWorkflow.getRespondBy())
            .disputeCategory(getSafeEnumName(dispute.getDisputeCategory()))
            .disputeIssuer(getSafeEnumName(dispute.getDisputeIssuer()))
            .build();
    }

    public String getFileName(){
        SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd");
        String formattedDate = dateFormatter.format(new Date());
        return "Dispute_"+formattedDate;
    }


    public double paisaToRupees(final double amountInPaisa) {
        return amountInPaisa / 100.0;
    }

    public ChargebackSummaryRow toChargebackSummaryRow(
        final DisputeWorkflow disputeWorkflow) {
        final var dispute = disputeWorkflow.getDispute();
        final var financialDisputeWorkflow = DisputeWorkflowUtils.getFinancialDisputeWorkflow(
            disputeWorkflow);
        return ChargebackSummaryRow.builder()
            .disputeId(dispute.getDisputeId())
            .disputeFileId(disputeWorkflow.getDisputeSourceId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion().name())
            .disputeType(disputeWorkflow.getDisputeType().name())
            .disputeStage(disputeWorkflow.getDisputeStage().name())
            .transactionReferenceId(dispute.getTransactionReferenceId())
            .merchantId(dispute.getMerchantId())
            .merchantTransactionId(dispute.getMerchantTransactionId())
            .instrumentTransactionId(dispute.getInstrumentTransactionId())
            .disputeReferenceId(dispute.getDisputeReferenceId())
            .rrn(dispute.getRrn())
            .disputeCategory(getSafeEnumName(dispute.getDisputeCategory()))
            .communicationId(disputeWorkflow.getCommunicationId())
            .transactionAmount(paisaToRupees(dispute.getTransactionAmount()))
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .acceptedAmount(financialDisputeWorkflow.getAcceptedAmount())
            .penaltyAmount(financialDisputeWorkflow.getPenaltyAmount())
            .currentState(disputeWorkflow.getCurrentState().name())
            .disputeIssuer(getSafeEnumName(dispute.getDisputeIssuer()))
            .raisedAt(disputeWorkflow.getRaisedAt())
            .respondBy(disputeWorkflow.getRespondBy())
            .build();
    }

    public ToaSummary toToaSummary(DisputeWorkflow disputeWorkflow) {
        return ToaSummary.builder()
            .disputeId(disputeWorkflow.getDisputeId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion().name())
            .disputeType(disputeWorkflow.getDisputeType().name())
            .disputeStage(disputeWorkflow.getDisputeStage().name())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .merchantId(disputeWorkflow.getDispute().getMerchantId())
            .merchantTransactionId(disputeWorkflow.getDispute().getMerchantTransactionId())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .currentState(disputeWorkflow.getCurrentState().name())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .disputeIssuer(getSafeEnumName(disputeWorkflow.getDispute().getDisputeIssuer()))
            .disputeCategory(getSafeEnumName(disputeWorkflow.getDispute().getDisputeCategory()))
            .disputeReferenceId(disputeWorkflow.getDispute().getDisputeReferenceId())
            .creationDate(disputeWorkflow.getDispute().getCreatedAt().toString())
            .updationDate(disputeWorkflow.getDispute().getUpdatedAt().toString())
            .build();
    }

    public DisputeSummaryRow toDisputeSummaryRow(DisputeWorkflow disputeWorkflow) {
        return DisputeSummaryRow.builder()
            .disputeId(disputeWorkflow.getDisputeId())
            .disputeWorkflowId(disputeWorkflow.getDisputeWorkflowId())
            .disputeWorkflowVersion(disputeWorkflow.getDisputeWorkflowVersion().name())
            .disputeType(disputeWorkflow.getDisputeType().name())
            .disputeStage(disputeWorkflow.getDisputeStage().name())
            .transactionReferenceId(disputeWorkflow.getTransactionReferenceId())
            .merchantId(disputeWorkflow.getDispute().getMerchantId())
            .merchantTransactionId(disputeWorkflow.getDispute().getMerchantTransactionId())
            .disputedAmount(disputeWorkflow.getDisputedAmount())
            .currentState(disputeWorkflow.getCurrentState().name())
            .raisedAt(disputeWorkflow.getRaisedAt())
            .disputeIssuer(getSafeEnumName(disputeWorkflow.getDispute().getDisputeIssuer()))
            .disputeCategory(getSafeEnumName(disputeWorkflow.getDispute().getDisputeCategory()))
            .build();

    }

    public UpiFileHistoryProcessingSummaryRow toUpiFileHistoryProcessingSummaryRow(
        FileRow fileRow) {
        return UpiFileHistoryProcessingSummaryRow.builder()
            .rowId(fileRow.getRowId())
            .rowState(fileRow.getRowState().name())
            .code(fileRow.getRowContext().getCode())
            .upiTransactionId(
                String.valueOf(fileRow.getRowContext().getContent().get("UPI Transaction ID")))
            .beneficiary(String.valueOf(fileRow.getRowContext().getContent().get("Beneficiery")))
            .adjType(String.valueOf(fileRow.getRowContext().getContent().get("Adjtype")))
            .txnDate(String.valueOf(fileRow.getRowContext().getContent().get("Txndate")))
            .remitter(String.valueOf(fileRow.getRowContext().getContent().get("Remitter")))
            .adjDate(String.valueOf(fileRow.getRowContext().getContent().get("Adjdate")))
            .txnAmount(String.valueOf(fileRow.getRowContext().getContent().get("Txnamount")))
            .compensationAmount(
                String.valueOf(fileRow.getRowContext().getContent().get("Compensation amount")))
            .adjAmount(String.valueOf(fileRow.getRowContext().getContent().get("Adjamount")))
            .rrn(String.valueOf(fileRow.getRowContext().getContent().get("RRN"))).build();
    }

    public PgPreArbFileHistoryProcessingSummaryRow toPgPreArbFileHistoryFirstLevelProcessingSummaryRow(
        FileRow fileRow) {
        return PgPreArbFileHistoryProcessingSummaryRow.builder()
            .rowId(fileRow.getRowId())
            .rowState(fileRow.getRowState().name())
            .code(fileRow.getRowContext().getCode())
            .bankDisputedAmount(
                String.valueOf(fileRow.getRowContext().getContent().get("Amount")))
            .cbReceivedDate(
                String.valueOf(fileRow.getRowContext().getContent().get(CB_RECEIVED_DATE)))
            .transactionId(String.valueOf(
                fileRow.getRowContext().getContent().get("Transaction ID")))
            .source(String.valueOf(fileRow.getRowContext().getContent().get("Source Of CB")))
            .build();
    }

    public EdcFileHistoryProcessingSummaryRow toEdcFileHistoryProcessingSummaryRow(
        final FileRow fileRow) {
        return EdcFileHistoryProcessingSummaryRow.builder()
            .rowId(fileRow.getRowId())
            .rowState(fileRow.getRowState().name())
            .code(fileRow.getRowContext().getCode())
            .cbReceivedDate(
                String.valueOf(fileRow.getRowContext().getContent().get(CB_RECEIVED_DATE)))
            .source(String.valueOf(fileRow.getRowContext().getContent().get(SOURCE)))
            .tenant(String.valueOf(fileRow.getRowContext().getContent().get("Tenant")))
            .terminalId(String.valueOf(fileRow.getRowContext().getContent().get("Terminal Id")))
            .merchantId(String.valueOf(fileRow.getRowContext().getContent().get("Merchant Id")))
            .bankDisputedAmount(
                String.valueOf(fileRow.getRowContext().getContent().get("Bank Disputed Amount")))
            .rrn(String.valueOf(fileRow.getRowContext().getContent().get("RRN")))
            .disputeReason(
                String.valueOf(fileRow.getRowContext().getContent().get(DISPUTE_REASON)))
            .build();
    }

    public PgFirstLevelFileHistoryProcessingSummaryRow toPgFirstLevelFileHistoryPreArbProcessingSummaryRow(
        FileRow fileRow) {
        return PgFirstLevelFileHistoryProcessingSummaryRow.builder()
            .rowId(fileRow.getRowId())
            .rowState(fileRow.getRowState().name())
            .code(fileRow.getRowContext().getCode())
            .bankRefNo(String.valueOf(fileRow.getRowContext().getContent().get("Bank Ref No")))
            .reasonCode(String.valueOf(fileRow.getRowContext().getContent().get("Reason Code")))
            .bankDisputedAmount(
                String.valueOf(fileRow.getRowContext().getContent().get("Bank Disputed Amount")))
            .pgId(String.valueOf(fileRow.getRowContext().getContent().get("PG_ID(RET_BID)")))
            .cbReceivedDate(
                String.valueOf(fileRow.getRowContext().getContent().get(CB_RECEIVED_DATE)))
            .transactionId(String.valueOf(
                fileRow.getRowContext().getContent().get("SM Transaction id(SRC_PRN)")))
            .source(String.valueOf(fileRow.getRowContext().getContent().get(SOURCE)))
            .disputeReason(
                String.valueOf(fileRow.getRowContext().getContent().get(DISPUTE_REASON)))
            .build();
    }

    public EdcMisRowSummary transformEdcMisRowSummary(Row row) {
        var message = MapperUtils.deserialize(
            Optional.ofNullable(row.getRowContext()).map(RowContext::getContent)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.SERIALIZATION_ERROR,
                    Map.of(Constants.MESSAGE, ROW_CONTEXT_NULL))),
            EdcRowMessage.class);
        return EdcMisRowSummary.builder()
            .rowId(row.getRowId())
            .rowState(row.getRowState().name())
            .sourceId(row.getSourceId())
            .transactionId(message.getTransactionId())
            .transactionType(message.getInstrumentType())
            .transactionAmount(message.getTransactionAmount())
            .chargebackAcceptedAmount(message.getChargebackAcceptedAmount())
            .build();
    }

    public PgMisRowSummary transformPgMisRowSummary(final Row row) {
        var message = MapperUtils.deserialize(
            Optional.ofNullable(row.getRowContext()).map(RowContext::getContent)
                .orElseThrow(() -> DisputeExceptionUtil.error(StratosErrorCodeKey.SERIALIZATION_ERROR,
                    Map.of(Constants.MESSAGE, ROW_CONTEXT_NULL))),
            PgMisRowMessage.class);

        return PgMisRowSummary.builder()
            .rowId(row.getRowId())
            .rowState(row.getRowState().name())
            .sourceId(row.getSourceId())
            .netAmount(message.getNetAmount().toString())
            .transactionId(message.getTransactionId())
            .transactionType(message.getTransactionType().name())
            .interchange(message.getInterchange())
            .build();
    }
    public RefundEligibilityResponse buildResponseForRefundEligibility(
        final RefundAction refundAction, final String message) {
        return RefundEligibilityResponse.builder()
            .refundAction(refundAction)
            .message(message)
            .build();
    }

    public String getSafeEnumName(Enum value) {
        return Optional.ofNullable(value)
            .map(Enum::name)
            .orElse(null);
    }

    public RefundEligibilityResponse getProcessResponseForRefundEligibility() {
        return TransformationUtils.buildResponseForRefundEligibility(RefundAction.PROCESS,
            "Disputed is raised but refund cannot be blocked anymore.");
    }

    public RefundEligibilityResponse getHoldResponseForRefundEligibility() {
        return TransformationUtils.buildResponseForRefundEligibility(RefundAction.HOLD,
            "Dispute is raised and refund is blocked by Stratos.");
    }

    public RefundEligibilityResponse getFailedResponseForRefundEligibility() {
        return TransformationUtils.buildResponseForRefundEligibility(RefundAction.FAILED,
            "Dispute is raised and refund doesn't exist.");
    }

    public KratosRecommendedAction getRecommendedActionFromFraudAction(@NotNull final FraudAction fraudAction) {
        try {
            return KratosRecommendedAction.valueOf(fraudAction.getType());
        } catch (IllegalArgumentException exception) {
            throw DisputeExceptionUtil.error(StratosErrorCodeKey.INVALID_KRATOS_ACTION,
                Map.of("action", fraudAction.getType(),
                    Constants.MESSAGE, "action value did not match"));
        }
    }
    public static DisputeMetadataResponse toDisputeMetadataResponse(
        List<DisputeMetadata> disputeMetadataList ) {
        Map<DisputeMetadataDto, List<DisputeMetadataBaseResponse>> result = new EnumMap<>(DisputeMetadataDto.class);

        Map<DisputeMetadataType, List<DisputeMetadata>>  disputeMetadataTypeListMap = disputeMetadataList.stream()
            .collect(Collectors
                .groupingBy(DisputeMetadata::getDisputeMetadataType, Collectors.toList()));

        for(Entry<DisputeMetadataType, List<DisputeMetadata>> item: disputeMetadataTypeListMap.entrySet()){
            DisputeMetadataDto type = DisputeMetadataDto.valueOf(item.getKey().name());
            List<DisputeMetadataBaseResponse> metadataBaseResponses = item.getValue().stream()
                .map((disputeMetadata ->
                 disputeMetadata.getDisputeMetadataType().accept(
                    new DisputeMetadataTypeVisitor<DisputeMetadataBaseResponse>() {
                        // ADD individual transformation here

                        @Override
                        public DisputeMetadataBaseResponse visitFraAction() {
                            FraudActionDisputeMetadata fraudActionDisputeMetadata = (FraudActionDisputeMetadata)disputeMetadata;

                            return DisputeFraActionMetadataResponse.builder()
                                .transactionId(fraudActionDisputeMetadata.getTransactionReferenceId())
                                .disputeWorkflowId(fraudActionDisputeMetadata.getDisputeWorkflowId())
                                .actionType(fraudActionDisputeMetadata.getActionType())
                                .reasonCode(fraudActionDisputeMetadata.getReasonCode())
                                .build();
                        }
                        @Override
                        public DisputeMetadataBaseResponse visitSohMetadata() {
                            SettlementOnHoldMetadata settlementOnHoldMetadata = (SettlementOnHoldMetadata)disputeMetadata;
                            FraudMeta metadata = settlementOnHoldMetadata.getSohMeta();
                            return DisputeSohMetadataResponse.builder()
                                .transactionId(settlementOnHoldMetadata.getTransactionReferenceId())
                                .disputeWorkflowId(settlementOnHoldMetadata.getDisputeWorkflowId())
                                .reasonCode(metadata.getReasonCode())
                                .subReasonCode(metadata.getSubReasonCode())
                                .source(metadata.getSource())
                                .agent(metadata.getAgent())
                                .instrumentType(metadata.getInstrumentType())
                                .remark(metadata.getRemarks())
                                .build();
                        }
                    })
            )).toList();

            result.put(type, metadataBaseResponses);

        }

        return DisputeMetadataResponse.builder()
            .disputeMetadata(result)
            .build();
    }

    public NBFileHistoryProcessingSummaryRow toNBFileHistoryProcessingSummaryRow(
        FileRow fileRow) {
        return  NBFileHistoryProcessingSummaryRow.builder()
            .rowId(fileRow.getRowId())
            .rowState(fileRow.getRowState().name())
            .code(fileRow.getRowContext().getCode())
            .cbReceivedDate(
                String.valueOf(fileRow.getRowContext().getContent().get(CB_RECEIVED_DATE)))
            .transactionDate(
                String.valueOf(fileRow.getRowContext().getContent().get("Transaction Date")))
            .transactionId(String.valueOf(fileRow.getRowContext().getContent().get("Transaction ID")))
            .bankReferenceId(String.valueOf(fileRow.getRowContext().getContent().get("Bank Reference ID")))
            .amount(String.valueOf(fileRow.getRowContext().getContent().get("Amount")))
            .source(String.valueOf(fileRow.getRowContext().getContent().get(SOURCE)))
            .disputeReason(
                String.valueOf(fileRow.getRowContext().getContent().get(DISPUTE_REASON)))
            .build();
    }

    public RefundRequest getRefundRequestFromTransactionDetails(
        final TransactionDetail transactionDetail,
        final Dispute dispute, final String merchantTxnId, final Long amount) throws Exception {

        var sentPayment = transactionDetail.getSentPayment();

        return RefundRequest.builder().refundContext(RefundContextV1.builder()
                .payerId(sentPayment.getUserId())
                .merchantId(dispute.getMerchantId())
                .merchantTxnId(merchantTxnId)
                .merchantForwardTxnId(
                    sentPayment.getContext().visit(new PaymentContextMerchantTransactionIdVisitor())
                )
                .paymentForwardTxnId(sentPayment.getTransactionId())
                .initiationAmount(amount)
                .totalReversalAmount(amount)
                .originalTransactionTime(sentPayment.getSentAt().getTime())
                .reversalCategory(ReversalCategory.ORDER_FAILURE)
                .reversalType(ReversalType.FULFILMENT_FAILURE).build())
            .callbackPath("/v1/callback/refund-status")
            .accountingContext(MerchantFulfillmentReversalAccountingContext.builder()
                .serviceType("NETBANKING")
                .providerId(dispute.getDisputeIssuer().name())
                .providerName(dispute.getMerchantId()).build())
            .build();

    }


    public RefundRequest toRefundRequestsForExternalMerchant(
        final TransactionDetails transactionDetails, final String merchantTxnId, final Long amount){

        return RefundRequest.builder()
            .refundContext(RefundContextV2.builder()
                .initiationAmount(amount)
                .reversalCategory(ReversalCategory.UPI_IOP_DEBIT_REVERSAL_CHRGBK)
                .reversalType(ReversalType.WALLET_INTEROP_CHARGEBACK)
                .paymentForwardTxnId(transactionDetails.getTransactionId())
                .reversalSource(VpaReversalSource.builder()
                    .vpa(((WalletTransactionDetails)transactionDetails).getDestinationVpa())
                    .name(((WalletTransactionDetails)transactionDetails).getDestinationName())
                    .build())
                .reversalTxnId(merchantTxnId)
                .reversalDestination(VpaReversalDestination.builder()
                    .vpa(((WalletTransactionDetails)transactionDetails).getSourceVpa())
                    .build())
                .build())
            .accountingContext(WalletTopupFulfilmentReverseV2AccountingContext.builder()
                .mcc(((WalletTransactionDetails)transactionDetails).getMcc())
                .build())
            .callbackPath("/v1/callback/refund-status")
            .walletInteropContext(
                WalletInteropContext.builder()
                    .originalPaymentTId(transactionDetails.getGlobalTransactionId())
                    .initiationMode(InitiationMode.DEFAULT)
                    .providerAccountType(ProviderAccountType.DEFAULT)
                    .build()
            )
            .build();
    }

    public ToaProcessorMessage toToaProcessorMessageFromNotionalCreditPulseMessage(
        final NotionalCreditPulseMessage notionalCreditPulseMessage) {

        return ToaProcessorMessage.builder()
            .disputeType(DisputeType.NOTIONAL_CREDIT_TOA)
            .transactionId(notionalCreditPulseMessage.getTransactionId())
            .eventId(notionalCreditPulseMessage.getCatalystId())
            .build();

    }

    public EvidenceDetail toEvidenceDetail(
            final StoredDocumentUploadWithMetaDataActionMetadata evidence, String dwId, String docstoreBaseUrl) {

        String docstoreUrlPath = docstoreBaseUrl + evidence.getDocumentId();
        return EvidenceDetail.builder()
            .evidenceId(evidence.getActionId())
            .disputeWorkflowId(dwId)
            .metadata(evidence.getMetadata()==null?null:MapperUtils.deserialize(evidence.getMetadata(), TypeReferences.MAP_OF_STRING_OBJECT))
            .status(DtoUtils.evidenceStatusToDto(evidence.getStatus()))
            .documentId(docstoreUrlPath)
            .build();

    }

    public WalletTransactionDetails toWalletTransactionDetails(TransactionDetail transactionDetail, ProcessingRail rail){

        final var sentPayment = transactionDetail.getSentPayment();
        final var walletSentPaymentInstrument = ValidationUtils.validateWalletExternalMerchantTransaction(transactionDetail, rail);

        log.info("Payment walletSentPaymentInstrument  for wallet service is {}", walletSentPaymentInstrument);

        final var merchantVpa = transactionDetail.getSentPayment().getTo().get(0).getVpa();

        WalletTransactionDetails transactionDetails = WalletTransactionDetails.builder()
            .instrumentId(walletSentPaymentInstrument.getInstrumentId())
            .transactionId(sentPayment.getTransactionId())
            .transactionAmount(walletSentPaymentInstrument.getAmount())
            .merchantId(merchantVpa)
            .rrn(((WalletPaymentInstrument) walletSentPaymentInstrument).getUtr())
            .upiTransactionId(((WalletPaymentInstrument) walletSentPaymentInstrument).getUpiTransactionId())
            .sentTime(transactionDetail.getSentPayment().getSentAt())
            .globalTransactionId(sentPayment.getGlobalPaymentId())
            .paymentState(transactionDetail.getSentPayment().getPaymentState().name())
            .sourceVpa(((WalletPaymentInstrument) walletSentPaymentInstrument).getVpa())
            .destinationVpa(transactionDetail.getSentPayment().getTo().get(0).getVpa())
            .destinationName(transactionDetail.getSentPayment().getTo().get(0).getName())
            .mcc(transactionDetail.getSentPayment().getTo().get(0).getMcc())
            .build();

        log.info("Wallet TransactionDetail {}", transactionDetail);
        return transactionDetails;
    }

    public WalletTransactionDetails toWalletTransactionDetailsForWalletCbs(TransactionDetail transactionDetail, ProcessingRail rail){

        final var sentPayment = transactionDetail.getSentPayment();
        final var walletSentPaymentInstrument = ValidationUtils.validateWalletExternalMerchantTransactionForWalletCbs(transactionDetail, rail);

        log.info("Wallet sentPayment Instrument from wallet service {}", walletSentPaymentInstrument);

        final var merchantVpa = transactionDetail.getSentPayment().getTo().get(0).getVpa();

        WalletTransactionDetails transactionDetails = WalletTransactionDetails.builder()
            .instrumentId(walletSentPaymentInstrument.getInstrumentId())
            .transactionId(sentPayment.getTransactionId())
            .transactionAmount(walletSentPaymentInstrument.getAmount())
            .merchantId(merchantVpa)
            .rrn(((WalletPaymentInstrument) walletSentPaymentInstrument).getUtr())
            .upiTransactionId(((WalletPaymentInstrument) walletSentPaymentInstrument).getUpiTransactionId())
            .sentTime(transactionDetail.getSentPayment().getSentAt())
            .globalTransactionId(sentPayment.getGlobalPaymentId())
            .paymentState(transactionDetail.getSentPayment().getPaymentState().name())
            .sourceVpa(((WalletPaymentInstrument) walletSentPaymentInstrument).getVpa())
            .destinationVpa(transactionDetail.getSentPayment().getTo().get(0).getVpa())
            .destinationName(transactionDetail.getSentPayment().getTo().get(0).getName())
            .mcc(transactionDetail.getSentPayment().getTo().get(0).getMcc())
            .build();

        log.info("Wallet TransactionDetail {}", transactionDetail);
        return transactionDetails;
    }
}
